{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "Launch Chrome",
      "url": "http://localhost:3303/",
      "webRoot": "${workspaceFolder}"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "run test",
      "program": "${workspaceFolder}/scripts/test.js",
      "args": ["--config=jest.config.js", "trackbutton"]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "yarn gen",
      "runtimeExecutable": "yarn",
      "runtimeArgs": [
        "graphql-codegen"
      ],
      "cwd": "${workspaceFolder}",
      "outputCapture": "std"
    }
  ]
}
