import * as Sentry from '@sentry/react';
import { Layout } from '@shopify/polaris';
import { useFeatureAvailable } from 'aftershipBillingUi/BillingSDK';
import FormPage from 'components/FormPage';
import { PageCode } from 'constants/pushCenter';
import { RbacAction, RbacResource } from 'constants/rbac/resource';
import { useRBACPermission } from 'hooks/rbac/useRBACPermission';
import useIsAfterShipPayingUser from 'hooks/useIsAfterShipPayingUser';
import usePushCenterLaunchModal from 'hooks/usePushCenterLaunchModal';
import { useToast } from 'hooks/useToast';
import ReturnWindow from 'pages/EligibilityRules/components/EligibilityForm/components/ReturnWindow';
import { RETURN_WINDOW_BASE_ON } from 'pages/EligibilityRules/components/EligibilityForm/components/ReturnWindow/types';
import GiftReturn from 'pages/ReturnsSettings/components/GiftReturn';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
    useMutationOrderLookupRules,
    useQueryOrderLookupRules,
} from 'resources/orderLookup';
import {
    useMutationGiftReturn,
    useQueryGiftReturn,
} from 'resources/settings/giftReturn';
import { useQueryInventory } from 'resources/settings/inventory';
import { useSettingReturnsV4 } from 'resources/settings/returnsSetting';
import { getResponseErrorMsg } from 'resources/utils';
import {
    useMutationEligibilityRules,
    useQueryEligibilityRules,
} from 'store/eligibilityRules';
import FeatureCode from 'types/featureCode';

import { schema } from './schema';
import { getFormikInitialValues, getSubmitPayload } from './utils';

export interface FormOrderLookupValues {
    by_email_enabled: boolean;
    by_postal_code_enabled: boolean;
}
export enum FORM_FIELDS {
    returnWindow = 'returnWindow',
    returnWindowBaseOn = 'returnWindowBaseOn',
    preventUnfulfilledOrdersEnabled = 'preventUnfulfilledOrdersEnabled',
}

export interface FormValues {
    [FORM_FIELDS.returnWindow]: string;
    [FORM_FIELDS.returnWindowBaseOn]: RETURN_WINDOW_BASE_ON;
    giftReturnEnabled: boolean;
    [FORM_FIELDS.preventUnfulfilledOrdersEnabled]: boolean;
}

const GeneralPage = () => {
    const { contextHolder } = usePushCenterLaunchModal({
        pageCode: PageCode.ORDER_LOOKUP,
    });
    const { openToast } = useToast();
    const { t } = useTranslation();

    const hasSettingsViewPermission = useRBACPermission(
        RbacAction.VIEW,
        RbacResource.SETTINGS
    );

    const hasEligibilityRulesViewPermission = useRBACPermission(
        RbacAction.VIEW,
        RbacResource.SETTINGS_ELIGIBILITY_RULES
    );
    const hasEligibilityRulesEditPermission = useRBACPermission(
        RbacAction.EDIT,
        RbacResource.SETTINGS_ELIGIBILITY_RULES
    );

    const hasInventoryViewPermission = useRBACPermission(
        RbacAction.VIEW,
        RbacResource.SETTINGS_INVENTORY
    );

    const hasGiftReturnsViewPermission = useRBACPermission(
        RbacAction.VIEW,
        RbacResource.SETTINGS_GIFT_RETURN
    );
    const hasGiftReturnsEditPermission = useRBACPermission(
        RbacAction.EDIT,
        RbacResource.SETTINGS_GIFT_RETURN
    );

    const { available: giftReturnAvailable } = useFeatureAvailable(
        FeatureCode.GIFT_RETURN
    );

    const {
        data: eligibilityRules,
        error: eligibilityRulesError,
        isLoading: isEligibilityRulesLoading,
        refetch: refetchEligibilityRules,
    } = useQueryEligibilityRules(hasEligibilityRulesViewPermission);

    const {
        isLoading: isMutateEligibilityRulesLoading,
        mutateAsync: mutateEligibilityRules,
    } = useMutationEligibilityRules();

    const {
        data: settingReturns,
        error: settingReturnsError,
        isLoading: isSettingReturnsLoading,
    } = useSettingReturnsV4(hasSettingsViewPermission);
    const {
        data: inventoryData,
        error: inventoryError,
        isLoading: isInventoryLoading,
    } = useQueryInventory(hasInventoryViewPermission);

    const {
        data: giftReturnData,
        error: giftReturnError,
        isLoading: isGiftReturnLoading,
        refetch: refetchGiftReturnSetting,
    } = useQueryGiftReturn(hasGiftReturnsViewPermission);
    const {
        mutateAsync: mutateGiftReturn,
        isLoading: isMutateGiftReturnLoading,
    } = useMutationGiftReturn();

    const isMountingLoading =
        isEligibilityRulesLoading ||
        isSettingReturnsLoading ||
        isInventoryLoading ||
        isGiftReturnLoading;

    const isSubmitting =
        isMutateEligibilityRulesLoading || isMutateGiftReturnLoading;

    const error =
        settingReturnsError ||
        eligibilityRulesError ||
        inventoryError ||
        giftReturnError;

    const isAfterShipConnected = useIsAfterShipPayingUser();
    const initialValues = getFormikInitialValues(
        eligibilityRules,
        inventoryData,
        giftReturnData
    );
    const {
        refund_to_store_credit_active: refundStoreActive = false,
        exchange_active: exchangeActive = false,
    } = settingReturns || {};
    const canEnableGiftReturn = refundStoreActive || exchangeActive;

    const handleSubmit = async (values: FormValues) => {
        const { eligibilityRulesPayload, giftReturnPayload } = getSubmitPayload(
            values
        );

        const promiseConfigList = [
            {
                enabled: hasEligibilityRulesEditPermission,
                mutate: () => mutateEligibilityRules(eligibilityRulesPayload),
                refetch: refetchEligibilityRules,
            },
            {
                enabled: hasGiftReturnsEditPermission && giftReturnAvailable,
                mutate: () => mutateGiftReturn(giftReturnPayload),
                refetch: refetchGiftReturnSetting,
            },
        ];

        // 有 RBAC / Billing 权限的才会调用接口
        Promise.all(
            promiseConfigList
                .filter(config => config.enabled)
                .map(config => config.mutate())
        ).then(() => {
            promiseConfigList
                .filter(config => config.enabled)
                .forEach(config => config.refetch());
            openToast(t('action.setting.saved'));
        });
    };

    return (
        <FormPage<FormValues>
            title={t('order.lookup.title')}
            loading={isSubmitting}
            mountingLoading={isMountingLoading}
            initialValues={initialValues}
            schema={schema(canEnableGiftReturn, isAfterShipConnected)}
            errorMessage={getResponseErrorMsg(error)?.message}
            onSubmit={handleSubmit}
        >
            <Layout>
                <Sentry.ErrorBoundary>{contextHolder}</Sentry.ErrorBoundary>
                <ReturnWindow isReturnTrackingEnable={isAfterShipConnected} />
                <GiftReturn canEnableGiftReturn={canEnableGiftReturn} />
            </Layout>
        </FormPage>
    );
};

export default GeneralPage;
