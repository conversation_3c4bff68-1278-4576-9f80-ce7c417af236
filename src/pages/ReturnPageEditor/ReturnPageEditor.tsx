import { useAuth } from '@aftership/automizely-product-auth';
import { Formik } from 'formik';
import { useToast } from 'hooks/useToast';
import { useUploadFile } from 'hooks/useUploadFile';
import { useWarrantyTranslation } from 'hooks/useWarrantyTranslate';
import { isEqual, isString, omitBy } from 'lodash';
import moment from 'moment';
import { HelpWidgetProps } from 'pages/ReturnPageEditorNew/widget/settings/HelpWidget/types';
import template from 'pages/ReturnPolicy/template';
import { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { RouteComponentProps, useHistory } from 'react-router-dom';
import {
    useMutationOrderLookupRules,
    useQueryOrderLookupRules,
} from 'resources/orderLookup';
import { ShoppingRecommendationType } from 'resources/refundAndExchanges/typings';
import {
    ReturnPageStatus,
    useMutationSettingReturnsPage,
    useSettingReturnsPage,
} from 'resources/settings/returnsPage';
import {
    useMutationPatchSettingShop,
    useSettingShop,
} from 'resources/settings/shop';
import returnPolicyEffects from 'saga/returnPolicy';
import errorStore from 'store/error';
import fallbackValue from 'utils/fallbackValue';

import ReturnsPageEditorNew from '../ReturnPageEditorNew/ReturnPageEditor';
import SetDefaultLanguageModal from './components/SetDefaultLanguageModal/SetDefaultLanguageModal';
import Title from './components/Title';
import { PageEditorFormValues } from './components/types/content';
import WarnAboutUnsavedChangedModal from './components/WarnAboutUnsavedChangedModal';
import useClickwrap from './hooks/useClickwarp';
import {
    useGetI18nValues,
    useMutateI18nValues,
} from './hooks/useI18nResources';
import { PageEditorProvider } from './PageEditorContext';
import { schema } from './schema';
import { adapter, base64ToFile, MAX_LOGO_HEIGHT } from './utils';

export interface StateProps {
    returnPolicyContent: string | null;
}

export interface DispatchProps {}

export type RouteProps = Partial<RouteComponentProps<{}>>;

export type Props = DispatchProps & StateProps & RouteProps;

const TYPE_ERROR_CODE = 422;
const TYPE_ERROR_PREFIX = 'File type:';
const DEFAULT_FONT = 'Lato';

const ReturnPageEditor = ({ returnPolicyContent }: Props) => {
    const { t } = useWarrantyTranslation();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const {
        fetchClickwrapConfig: {
            data: clickwrapConfig,
            refetch: refetchClickwrap,
        },
        patchClickwrapConfig: { mutateAsync: mutateClickwrap },
    } = useClickwrap();
    const {
        data: settingReturnsPage,
        refetch: refetchSettingReturnsPage,
    } = useSettingReturnsPage();
    const {
        mutateAsync: mutateSettingReturnsPage,
    } = useMutationSettingReturnsPage();
    const { data: settingShop, refetch: refetchSettingShop } = useSettingShop();
    const { mutateAsync: mutateSettingShop } = useMutationPatchSettingShop();

    const {
        mutateAsync: mutateOrderLookupRules,
    } = useMutationOrderLookupRules();

    const {
        data: orderLookupRules,
        refetch: refetchOrderLookupRules,
    } = useQueryOrderLookupRules();

    const { uploadFile } = useUploadFile();
    const [{ organization }] = useAuth();

    const {
        i18nValues,
        isLoading: isResourcesLoading,
        refetch: refetchTranslationResources,
    } = useGetI18nValues();

    const isReturnPageAlreadyPublished =
        settingReturnsPage?.returns_page_status === ReturnPageStatus.PUBLISHED;

    const { updateI18nValues } = useMutateI18nValues();

    const dispatch = useDispatch();

    const { openToast } = useToast();

    const clearError = () => {
        dispatch(errorStore.actions.clear());
    };

    const [isGoingBack, setIsGoingBack] = useState(false);

    const history = useHistory();

    const logoScale = useMemo(() => {
        const logoHeight =
            settingReturnsPage?.logo_image?.height ?? MAX_LOGO_HEIGHT;
        return logoHeight >= MAX_LOGO_HEIGHT
            ? 100
            : Math.round((logoHeight / MAX_LOGO_HEIGHT) * 100);
    }, [settingReturnsPage?.logo_image?.height]);

    const handleUploadError = (e: any, key: string) => {
        const { code, message } = (e as any).response.data.meta || {};
        if (
            code === TYPE_ERROR_CODE &&
            (message as string).startsWith(TYPE_ERROR_PREFIX)
        ) {
            dispatch(
                errorStore.actions.addError({
                    key,
                    message: t('page_editor.upload.wrong_type.message'),
                    details: [],
                })
            );
        }
        throw new Error('upload image failed');
    };

    const uploadChatSchemaImage = async (values: PageEditorFormValues) => {
        const schemaString = values.supportChatWidgetSchema;
        if (schemaString) {
            const schema = JSON.parse(schemaString);
            const props = schema?.$elements?.$props as HelpWidgetProps;
            const iconPath = props?.button_config?.icon_path;
            if (iconPath && iconPath.startsWith('data:image')) {
                const file = base64ToFile(iconPath);
                try {
                    const res = await uploadFile({
                        file,
                        type: 'image',
                    });
                    props.button_config.icon_path = res.url;
                } catch (e) {
                    handleUploadError(e, 'supportChatWidgetSchema');
                }

                values.supportChatWidgetSchema = JSON.stringify(schema);
            }
        }
        // 当 widget 没有修改时，不提交到后端，因为后端会有操作会有 feature code 的判定
        if (
            values.supportChatWidgetSchema ===
            settingReturnsPage?.support_chat_widget_schema
        ) {
            values.supportChatWidgetSchema = undefined;
        }
    };

    const handleSubmit = async (values: PageEditorFormValues) => {
        clearError();
        setIsSubmitting(true);

        try {
            await Promise.all([
                updateSettingReturnsPage(values),
                !isEqual(values.clickwrapConfig, clickwrapConfig) &&
                    mutateClickwrap(values.clickwrapConfig!),
                updateI18nValues(values, i18nValues),
                mutateSettingShop({
                    store_url: values.storeUrl,
                }),
                mutateOrderLookupRules({ order_lookup: values.orderLookup! }),
            ]);

            await Promise.all([
                refetchClickwrap(),
                refetchSettingReturnsPage(),
                refetchTranslationResources(),
                refetchSettingShop(),
                refetchOrderLookupRules(),
            ]);

            setIsSubmitting(false);
            openToast({
                message: t('toast.message.save.success'),
                isErrorStatus: false,
            });
        } catch (e) {
            setIsSubmitting(false);
            openToast({
                message: t('toast.message.save.fail'),
                isErrorStatus: true,
            });
        }
    };

    const updateSettingReturnsPage = async (values: PageEditorFormValues) => {
        await uploadChatSchemaImage(values);

        const orgId = organization?.id || '';

        const defaultLogo = settingReturnsPage?.logo_image;

        const data = adapter(values, orgId, defaultLogo);
        const { images, nestImages } = data;

        const imagesArray = Object.entries(images).filter(
            ([, value]) => value instanceof File
        );

        if (imagesArray.length) {
            for (const [key, value] of imagesArray) {
                if (value instanceof File) {
                    try {
                        const res = await uploadFile({
                            file: value,
                            type: 'image',
                        });
                        Object.assign(images, {
                            [key]: {
                                src: res.url,
                                width: res.width,
                                height: res.height,
                            },
                        });
                    } catch (e) {
                        handleUploadError(e, key);
                    }
                }
            }
        }

        // upload marketing_assets, File
        let assets = nestImages.marketing_assets || [];
        assets = assets.filter(asset => asset.image_url);

        if (assets) {
            // @ts-ignore
            for (const [index, asset] of assets.entries()) {
                const image = asset.image_url as any;
                if (image instanceof File) {
                    try {
                        const res = await uploadFile({
                            file: image,
                            type: 'image',
                        });
                        Object.assign(asset, {
                            image_url: res.url,
                            destination_url: asset.destination_url,
                        });
                    } catch (e) {
                        const { code, message } =
                            (e as any).response.data.meta || {};
                        if (
                            code === TYPE_ERROR_CODE &&
                            (message as string).startsWith(TYPE_ERROR_PREFIX)
                        ) {
                            dispatch(
                                errorStore.actions.addError({
                                    key: `marketingAssets[${index}][image_url]`,
                                    message: t(
                                        'page_editor.upload.wrong_type.message'
                                    ),
                                    details: [],
                                })
                            );
                        }
                    }
                }
            }
        }

        const transformedAssets = { marketing_assets: assets || [] };
        Object.assign(data.data, transformedAssets);
        if (
            values.returnPolicyContent &&
            values.returnPolicyContent !== template
        ) {
            dispatch(
                returnPolicyEffects.actions.updateReturnPolicyContent(
                    values.returnPolicyContent
                )
            );
        }

        localStorage.setItem(
            'page_editor_last_update',
            moment().format('MMM D, Y')
        );

        await mutateSettingReturnsPage({
            ...data.data,
            ...omitBy(images, isString),
        });
    };

    const title = (
        <Title
            name={t('page.pageEditor.title')}
            returnPageStatus={settingReturnsPage?.returns_page_status}
        />
    );

    return (
        <PageEditorProvider>
            <Formik<PageEditorFormValues>
                onSubmit={handleSubmit}
                validationSchema={schema}
                initialValues={{
                    contactUs: settingReturnsPage?.contact_url,
                    termPage: settingReturnsPage?.terms_url,
                    privacyPage: settingReturnsPage?.privacy_url,
                    returnsPolicy: settingReturnsPage?.policy_url,
                    returnPolicyContent: returnPolicyContent ?? template,
                    facebook: settingReturnsPage?.social_facebook,
                    twitter: settingReturnsPage?.social_twitter,
                    instagram: settingReturnsPage?.social_instagram,
                    color: settingReturnsPage?.theme_color,
                    storeLogo: settingReturnsPage?.logo_image?.src,
                    storeLogoWidth: settingReturnsPage?.logo_image?.width,
                    storeLogoHeight: settingReturnsPage?.logo_image?.height,
                    storeUrl: settingShop?.store_url,
                    favicon: settingReturnsPage?.favicon?.src,
                    heroImage: settingReturnsPage?.hero_image?.src,
                    // 数组类型变更后会重置整个表单
                    menuItems: settingReturnsPage?.menu_items,
                    // 数组类型变更后会重置整个表单
                    marketingAssets: settingReturnsPage?.marketing_assets,
                    customizedReturnPolicy:
                        settingReturnsPage?.external_return_policy_page,
                    showReturnsPagePoweredBy: !settingReturnsPage?.show_returns_page_powered_by,
                    onStoreBannerBackground:
                        settingReturnsPage?.exchange_for_anything_in_store_banner_background ||
                        '#000000',
                    onStoreBannerFont:
                        settingReturnsPage?.exchange_for_anything_in_store_banner_font ||
                        DEFAULT_FONT,
                    primaryFont:
                        settingReturnsPage?.returns_page_primary_font ||
                        DEFAULT_FONT,
                    bodyFont:
                        settingReturnsPage?.returns_page_body_font ||
                        DEFAULT_FONT,
                    storeName: settingShop?.store_name,
                    logoPosition:
                        settingReturnsPage?.logo_position_on_large_screen,
                    bypassSingleResolutionSelection: !!settingReturnsPage?.bypass_single_resolution_selection_enabled,
                    bypassSingleReturnMethodSelection: !!settingReturnsPage?.bypass_single_return_method_selection_enabled,
                    bypassSingleRefundDestinationSelection: !!settingReturnsPage?.bypass_single_refund_destination_selection_enabled,
                    returnPageStatus: settingReturnsPage?.returns_page_status,
                    hideReplaceSummaryAndPrice:
                        settingReturnsPage?.hide_replace_summary_and_price,
                    logoScale,
                    clickwrapConfig,
                    orderLookup: {
                        by_email_enabled: !!orderLookupRules?.order_lookup
                            ?.by_email_enabled,
                        by_postal_code_enabled: !!orderLookupRules?.order_lookup
                            ?.by_postal_code_enabled,
                        by_phone_number_enabled: !!orderLookupRules
                            ?.order_lookup?.by_phone_number_enabled,
                    },

                    exchangeForAnythingRecommendationActive: !!settingReturnsPage?.exchange_for_anything_recommendation_active,
                    exchangeForAnythingRecommendationType:
                        settingReturnsPage?.exchange_for_anything_recommendation_type ||
                        ShoppingRecommendationType.SimilarProduct,
                    exchangeForAnythingRecommendationExcludeValues:
                        settingReturnsPage
                            ?.exchange_for_anything_recommendation_exclude_conditions
                            ?.values || [],
                    exchangeForAnythingHeroImage:
                        settingReturnsPage?.exchange_for_anything_hero_image
                            ?.src,
                    contactDetails: {
                        allow_edit_return_shipping_address: fallbackValue(
                            settingReturnsPage?.allow_edit_return_shipping_address,
                            true
                        ),
                        allow_edit_contact_recipient: fallbackValue(
                            settingReturnsPage?.allow_edit_contact_recipient,
                            true
                        ),
                    },
                    supportChatWidgetSchema:
                        settingReturnsPage?.support_chat_widget_schema,
                    returnTrackingPageWidgetSchema:
                        settingReturnsPage?.return_tracking_page_widget_schema,
                    ...i18nValues,
                }}
                /* 防止提交时被修改的内容闪现原内容的问题 */
                enableReinitialize={!isSubmitting}
                validateOnBlur
                validateOnMount={!isResourcesLoading}
                isInitialValid={false}
            >
                {({
                    dirty,
                    isValid,
                    submitForm,
                    resetForm,
                    setFieldValue,
                    errors,
                }) => {
                    const primaryAction = isReturnPageAlreadyPublished
                        ? {
                              disabled: !dirty || !isValid,
                              content: t('action.save.content'),
                              loading: isSubmitting,
                              onAction: () => {
                                  submitForm();
                              },
                          }
                        : {
                              disabled: dirty && !isValid, // validate when dirty
                              content: t('action.push.content'),
                              loading: isSubmitting,
                              onAction: () => {
                                  setFieldValue(
                                      'returnPageStatus',
                                      ReturnPageStatus.PUBLISHED
                                  );
                                  submitForm();
                              },
                          };
                    const onBack = () => {
                        setIsGoingBack(true);
                        if (!dirty) {
                            history.replace('/returns-page');
                        }
                    };
                    const secondaryAction = !isReturnPageAlreadyPublished
                        ? {
                              disabled: !dirty || !isValid,
                              content: t('label.save_draft'),
                              loading: isSubmitting,
                              onAction: () => {
                                  submitForm();
                              },
                          }
                        : undefined;
                    return (
                        <>
                            {/* <WarnAboutUnsavedChanges dirty={dirty} /> */}
                            <SetDefaultLanguageModal />

                            <WarnAboutUnsavedChangedModal
                                title={t('modal.pageEditor.warning.title')}
                                message={t('modal.pageEditor.warning.message')}
                                actionContent={t(
                                    'modal.pageEditor.warning.actionContent'
                                )}
                                isOpen={dirty && isGoingBack}
                                onConfirm={() => {
                                    resetForm();
                                    history.replace('/returns-page');
                                }}
                                onCancel={() => setIsGoingBack(false)}
                                destructive={true}
                                loading={false}
                                closeModal={() => setIsGoingBack(false)}
                            />

                            <ReturnsPageEditorNew
                                onBack={onBack}
                                primaryAction={primaryAction}
                                secondaryAction={secondaryAction}
                                title={title}
                            />
                        </>
                    );
                }}
            </Formik>
        </PageEditorProvider>
    );
};

export default ReturnPageEditor;
