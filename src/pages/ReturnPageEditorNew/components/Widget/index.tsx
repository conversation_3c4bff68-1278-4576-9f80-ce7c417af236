import { OutlineAction } from '@aftership/atta-engine/editor-ui';
import { usePageEditorContext } from 'pages/ReturnPageEditor/PageEditorContext';
import { useState } from 'react';
import { useT } from 'utils/i18n';

import AddButton from './AddButton';
import { WidgetSettingWrapper } from './components/WidgetSetting';
import { WidgetNameEnum, widgetsMap, widgetTypeList } from './const';
import { Empty } from './Empty';
import { useAutoInstall } from './hooks/useAutoInstall';
import { useWidgetContainer } from './hooks/useWidgetContainer';
import styles from './index.module.scss';
import WidgetItem from './WidgetItem';
import { WidgetType } from './WidgetType';

export default function Widget() {
    const { widget } = usePageEditorContext();
    const { t } = useT();

    const container = useWidgetContainer();
    useAutoInstall(container);

    const [forceOpenAddWidget, setForceOpenAddWidget] = useState(false);

    // 弹窗关闭时重置状态
    const handleAddWidgetClose = () => {
        setForceOpenAddWidget(false);
    };

    // 如果当前有 widget，则显示 widget 的设置组件
    if (widget) {
        const { SettingComponent, elementName, displayName } = widgetsMap[
            widget
        ];
        if (SettingComponent) {
            return (
                <WidgetSettingWrapper
                    displayName={displayName}
                    elementName={elementName}
                >
                    <SettingComponent />
                </WidgetSettingWrapper>
            );
        }
    }

    const widgetElements = container?.children || [];

    const helpWidget = widgetElements.find(
        (item: any) => item.name === 'HelpWidget'
    );
    const trackingWidget = widgetElements.find(
        (item: any) => item.name === 'TrackingWidget'
    );

    const availableWidgets = [];

    if (!helpWidget) {
        availableWidgets.push(widgetsMap[WidgetNameEnum.HelpWidget]);
    }

    if (!trackingWidget) {
        availableWidgets.push(widgetsMap[WidgetNameEnum.TrackingWidget]);
    }

    if (widgetElements.length === 0) {
        return (
            <Empty
                list={availableWidgets}
                parentElement={container}
                forceOpen={forceOpenAddWidget}
                onClose={handleAddWidgetClose}
            />
        );
    }

    const addedWidgets = widgetElements
        .map((element: any) => {
            const widgetName = element.name as WidgetNameEnum;
            return widgetsMap[widgetName];
        })
        .filter(Boolean);

    const widgetsList = widgetTypeList.map(item => {
        return {
            type: item,
            list: addedWidgets.filter(
                (widget: any) => widget.type === item.type
            ),
        };
    });

    return (
        <div className={styles.widgetContainer}>
            <div className={styles.widgetsWrapper}>
                {widgetsList.map((widgets, index) => {
                    if (widgets.list.length === 0) return null;
                    return (
                        <WidgetType key={index} type={widgets.type}>
                            {widgets.list.map((item: any) => (
                                <WidgetItem
                                    key={item.displayName}
                                    elementName={item.elementName}
                                    displayName={item.displayName}
                                    featureCode={item.featureCode}
                                    description={item.description}
                                    makeElement={item.makeElement}
                                    addWidget={false}
                                    icon={item.icon}
                                    label={item.label}
                                    backgroundColor={item.backgroundColor}
                                    type={item.type}
                                    widgetName={item.widgetName}
                                    parentElement={container}
                                />
                            ))}
                        </WidgetType>
                    );
                })}
            </div>
            {availableWidgets.length > 0 && widgetElements.length > 0 && (
                <div className={styles.addButtonContainer}>
                    <AddButton
                        list={availableWidgets}
                        parentElement={container}
                        forceOpen={forceOpenAddWidget}
                        onClose={handleAddWidgetClose}
                        button={
                            <OutlineAction
                                iconSize={18}
                                content={t('widget.empty.add_widget')}
                            />
                        }
                    />
                </div>
            )}
        </div>
    );
}
