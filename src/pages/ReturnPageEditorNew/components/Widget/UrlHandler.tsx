/*  eslint-disable spellcheck/spell-checker */
import { setPreviewPopoverVisible } from '@aftership/atta-engine/editor-ui';
import { usePageEditorContext } from 'pages/ReturnPageEditor/PageEditorContext';
import React, { useEffect, useRef } from 'react';
import { patchQuery, useQueryMap } from 'utils/location';

import { WidgetNameEnum } from './const';
import { useWidgetContainer } from './hooks/useWidgetContainer';

const POPOVER_ID = 'atta-add-widget-button-popover';
const HOVER_ID = 'TrackingWidget'; // 与 widget.elementName 保持一致
/**
 * @description 处理 URL 参数，自动唤起添加 widget 弹窗, 并聚焦到 目标 widget
 */
const WidgetUrlHandler: React.FC = () => {
    const {
        schemaInitialized,
        selectTab,
        selectWidget,
    } = usePageEditorContext();
    const queryMap = useQueryMap();
    const container = useWidgetContainer();
    const listHandled = useRef(false);
    const targetHandled = useRef(false);

    // 1) 切 Tab → 保证 Popup 和 聚焦逻辑能挂载
    useEffect(() => {
        if (!schemaInitialized) return;
        if (
            (queryMap.widget_list === 'true' ||
                queryMap.target_widget === 'return_tracking') &&
            selectTab &&
            typeof selectTab === 'function'
        ) {
            selectTab('2');
        }
    }, [
        schemaInitialized,
        queryMap.widget_list,
        queryMap.target_widget,
        selectTab,
    ]);

    // 2) 处理 widget_list
    useEffect(() => {
        if (!schemaInitialized || listHandled.current) return;
        if (queryMap.widget_list === 'true') {
            setPreviewPopoverVisible(POPOVER_ID, true);
            patchQuery({ widget_list: undefined, tab: '2' });
            listHandled.current = true;
        }
    }, [schemaInitialized, queryMap.widget_list]);

    // 3) 处理 target_widget=return_tracking
    useEffect(() => {
        if (!schemaInitialized || targetHandled.current) return;
        if (queryMap.target_widget === 'return_tracking') {
            const items = container?.children || [];
            const has = items.find(
                (c: any) => c.name === WidgetNameEnum.TrackingWidget
            );
            if (has) {
                selectWidget(WidgetNameEnum.TrackingWidget);
            } else {
                setPreviewPopoverVisible(POPOVER_ID, true);
                // hover 高亮
                setTimeout(() => {
                    const el = document.getElementById(HOVER_ID);
                    if (el) {
                        el.dispatchEvent(
                            new MouseEvent('mouseover', {
                                bubbles: true,
                                cancelable: true,
                                view: window,
                            })
                        );
                    }
                }, 300);
            }
            patchQuery({ target_widget: undefined, tab: '2' });
            targetHandled.current = true;
        }
    }, [schemaInitialized, queryMap.target_widget, container, selectWidget]);

    return null;
};

export default WidgetUrlHandler;
