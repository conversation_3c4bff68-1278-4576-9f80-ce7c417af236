import { useAuth } from '@aftership/automizely-product-auth';
import useIsAfterShipSubscriber from 'hooks/useIsAfterShipSubscriber';
import { useMemo } from 'react';
import useQuerySubscriptions from 'resources/billing/useQuerySubscriptions';
import useQueryTrackingPages from 'resources/tracking/useQueryTrackingPages';

interface UseTrackingWidgetDataProps {
    refetchOnWindowFocus?: boolean;
}

/**
 * 统一管理 TrackingWidget 相关的数据获取和 loading 状态
 */
const useTrackingWidgetData = ({
    refetchOnWindowFocus = false,
}: UseTrackingWidgetDataProps = {}) => {
    const [{ organization }] = useAuth();

    // 获取 tracking pages 数据
    const trackingPagesQuery = useQueryTrackingPages({
        orgId: organization?.id,
    });

    // 获取 returns 订阅数据
    const subscriptionsQuery = useQuerySubscriptions({
        productCode: 'returns',
        orgId: organization?.id,
        refetchOnWindowFocus: true,
    });

    // 使用改装后的 tracking 订阅检查 hook (包含 loading 状态)
    const {
        IsAfterShipPayingUser: hasTrackingSubscription,
        isLoading: isLoadingAftershipSubscriptions,
    } = useIsAfterShipSubscriber({
        refetchOnWindowFocus,
    });

    // 汇总所有 loading 状态
    const isLoading = useMemo(() => {
        return (
            trackingPagesQuery.isLoading ||
            subscriptionsQuery.isLoading ||
            isLoadingAftershipSubscriptions
        );
    }, [
        trackingPagesQuery.isLoading,
        subscriptionsQuery.isLoading,
        isLoadingAftershipSubscriptions,
    ]);

    return {
        isLoading,
        trackingPagesData: trackingPagesQuery.data,
        subscriptionsData: subscriptionsQuery.data,
        hasTrackingSubscription,
        isLoadingTrackingPages: trackingPagesQuery.isLoading,
        isLoadingSubscriptions: subscriptionsQuery.isLoading,
        isLoadingAftershipSubscriptions,
    };
};

export default useTrackingWidgetData;
