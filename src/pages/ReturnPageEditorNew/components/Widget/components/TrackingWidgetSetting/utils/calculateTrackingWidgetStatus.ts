import { TrackingPage } from 'resources/tracking/useQueryTrackingPages';
import { TrackingWidgetStatus } from '../types';

interface CalculateTrackingWidgetStatusParams {
    trackingPages: TrackingPage[];
    hasReturnsSubscription: boolean;
    hasTrackingSubscription: boolean;
    selectedTrackingPage: string;
    originalTrackingPageId: string;
    hasDataLoaded: boolean;
}

/**
 * 纯函数计算 TrackingWidget 的状态
 * 将复杂的状态计算逻辑抽离为独立的纯函数，便于测试和维护
 * 
 * @param params - 计算状态所需的所有参数
 * @returns TrackingWidgetStatus - 计算得出的状态
 */
export const calculateTrackingWidgetStatus = ({
    trackingPages,
    hasReturnsSubscription,
    hasTrackingSubscription,
    selectedTrackingPage,
    originalTrackingPageId,
    hasDataLoaded,
}: CalculateTrackingWidgetStatusParams): TrackingWidgetStatus => {
    // 1. 页面数据检查 - 根据设计稿，优先检查页面数量
    if (trackingPages.length === 0) {
        // 如果之前已经加载过数据，且原始保存有页面ID，说明页面被删除了
        if (hasDataLoaded && originalTrackingPageId) {
            return TrackingWidgetStatus.PAGE_DELETED_SINGLE;
        }
        return TrackingWidgetStatus.INITIAL;
    }

    // 2. 未订阅tracking服务 - 需要区分是否之前配置过
    if (!hasTrackingSubscription) {
        if (originalTrackingPageId) {
            // 之前配置过，现在订阅过期 → 引导upgrade
            return TrackingWidgetStatus.SUBSCRIPTION_EXPIRED;
        } else {
            // 从未配置过，显示初始配置面板
            return TrackingWidgetStatus.NO_SUBSCRIPTION;
        }
    }

    // 3. 页面数量和服务状态检查
    if (trackingPages.length === 1) {
        const singlePage = trackingPages[0];
        // 单页面且未启用 returns 服务
        if (!singlePage.service_types?.includes('returns')) {
            return TrackingWidgetStatus.SERVICE_DISABLED;
        }
        // 单页面已启用 returns 服务
        if (selectedTrackingPage) {
            // 检查选中页面是否存在
            if (selectedTrackingPage !== singlePage.id) {
                return TrackingWidgetStatus.PAGE_DELETED_SINGLE;
            }
            // 检查订阅状态
            if (!hasReturnsSubscription) {
                return TrackingWidgetStatus.SUBSCRIPTION_EXPIRED;
            }
        }
        return TrackingWidgetStatus.REPLACE_PAGE;
    }

    // 4. 多个页面的情况
    if (trackingPages.length > 1) {
        // 优先检查原始保存的页面是否被删除
        if (originalTrackingPageId) {
            // 检查原始保存的页面是否存在
            const originalPageExists = trackingPages.some(
                p => p.id === originalTrackingPageId
            );

            if (!originalPageExists) {
                return TrackingWidgetStatus.PAGE_DELETED_MULTIPLE;
            }
        }

        // 现在检查原始保存的页面状态（用于service检查）
        const pageToCheck = originalTrackingPageId || selectedTrackingPage;
        if (pageToCheck) {
            // 检查页面是否启用 returns 服务
            const currentPage = trackingPages.find(
                p => p.id === pageToCheck
            );

            if (
                currentPage &&
                !currentPage.service_types?.includes('returns')
            ) {
                return TrackingWidgetStatus.PAGE_INVALID;
            }

            // 检查 returns 订阅状态
            if (!hasReturnsSubscription) {
                return TrackingWidgetStatus.SUBSCRIPTION_EXPIRED;
            }
        }

        // 检查是否有启用 returns 服务的页面
        const pagesWithReturns = trackingPages.filter(p =>
            p.service_types?.includes('returns')
        );

        if (pagesWithReturns.length === 0) {
            return TrackingWidgetStatus.REPLACE_PAGE;
        }
    }

    // 5. 正常状态 - 默认返回REPLACE_PAGE状态，表示可以选择页面
    return TrackingWidgetStatus.REPLACE_PAGE;
};
