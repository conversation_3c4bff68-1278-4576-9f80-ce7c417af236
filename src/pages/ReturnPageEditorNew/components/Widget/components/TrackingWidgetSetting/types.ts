// 组件状态枚举
export enum TrackingWidgetStatus {
    INITIAL = 'initial', // 初始状态，需要创建tracking page
    NO_SUBSCRIPTION = 'no_subscription', // 未订阅tracking服务
    SERVICE_DISABLED = 'service_disabled', // 服务未启用
    REPLACE_PAGE = 'replace_page', // 替换页面状态
    PAGE_INVALID = 'page_invalid', // 页面无效
    PAGE_DELETED_MULTIPLE = 'page_deleted_multiple', // 多个页面但选中的被删除
    PAGE_DELETED_SINGLE = 'page_deleted_single', // 只有一个页面但被删除
    SUBSCRIPTION_EXPIRED = 'subscription_expired', // 订阅到期但有选中页面
    READY = 'ready', // 就绪状态，默认值
}

// 国际化文案键名定义
export enum TrackingWidgetI18nKey {
    TITLE = 'widget.tracking_widget.title',
    DESCRIPTION_INITIAL = 'widget.tracking_widget.description.initial',
    DESCRIPTION_NO_SUBSCRIPTION = 'widget.tracking_widget.description.no_subscription',
    DESCRIPTION_SERVICE_DISABLED = 'widget.tracking_widget.description.service_disabled',
    DESCRIPTION_REPLACE_PAGE = 'widget.tracking_widget.description.replace_page',
    LEARN_MORE = 'widget.tracking_widget.learn_more',
    BUTTON_CREATE = 'widget.tracking_widget.button.create',
    BUTTON_ENABLE = 'widget.tracking_widget.button.enable',
    BUTTON_TEXT_LABEL = 'widget.tracking_widget.form.button_text.label',
    BUTTON_TEXT_PLACEHOLDER = 'widget.tracking_widget.form.button_text.placeholder',
    SELECT_PAGE_LABEL = 'widget.tracking_widget.form.select_page.label',
    SELECT_PLACEHOLDER = 'widget.tracking_widget.form.select_page.placeholder',
    BANNER_PAGE_INVALID = 'widget.tracking_widget.banner.page_invalid',
    BANNER_PAGE_DELETED_MULTIPLE = 'widget.tracking_widget.banner.page_deleted_multiple',
    BANNER_PAGE_DELETED_SINGLE = 'widget.tracking_widget.banner.page_deleted_single',
    BANNER_SUBSCRIPTION_EXPIRED = 'widget.tracking_widget.banner.subscription_expired',
    BANNER_UPGRADE_REQUIRED = 'widget.tracking_widget.banner.upgrade_required',
    BANNER_ACTION_ENABLE = 'widget.tracking_widget.banner.action.enable',
    BANNER_ACTION_UPGRADE = 'widget.tracking_widget.banner.action.upgrade',
    BANNER_ACTION_RECREATE = 'widget.tracking_widget.banner.action.recreate',
    SELECT_ERROR = 'widget.tracking_widget.form.select_page.error',
    DELETED_PAGE_PREFIX = 'widget.tracking_widget.deleted_page_prefix',
}

// 状态配置类型
export interface StatusConfig {
    description: string;
    showLearnMore: boolean;
    banner?: {
        status: 'critical' | 'warning';
        content: string;
        actionText?: string;
        actionHandler?: () => void;
    };
    primaryButton?: {
        text: string;
        icon?: React.ReactNode;
        handler: () => void;
    };
    showForm: boolean;
    formConfig?: {
        buttonTextEditable: boolean;
        buttonTextValue: string;
        buttonTextError?: string;
        selectEditable: boolean;
        selectValue: string;
        selectOptions: Array<{
            label: string;
            value: string;
            enabled?: boolean;
            isDeleted?: boolean;
        }>;
        selectError?: string;
        characterCount: string;
    };
}

// TrackingWidget 元素的 props 类型定义
export type TrackingWidgetProps = {
    button_text: string;
    tracking_page_id: string | null;
};

// 表单标签类型
export interface FormLabels {
    buttonTextLabel: string;
    buttonTextPlaceholder: string;
    selectLabel: string;
    selectPlaceholder: string;
}
