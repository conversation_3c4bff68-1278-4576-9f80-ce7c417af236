import { useAttaEditor } from '@aftership/atta-engine/editor';
import { useAuth } from '@aftership/automizely-product-auth';
import useIsAfterShipSubscriber from 'hooks/useIsAfterShipSubscriber';
import { useReturnsTrackingTurnOnModal } from 'pages/ReturnTracking/hooks/useReturnsTrackingTurnOnModal';
import { useReturnTrackingStatus } from 'pages/ReturnTracking/hooks/useReturnTrackingStatus';
import { useMemo } from 'react';
import useQuerySubscriptions from 'resources/billing/useQuerySubscriptions';
import useQueryTrackingPages from 'resources/tracking/useQueryTrackingPages';
import FeatureCode from 'types/featureCode';

import useOriginalTrackingPageId from '../components/TrackingWidgetSetting/hooks/useOriginalTrackingPageId';
import useTrackingWidgetStatus from '../components/TrackingWidgetSetting/hooks/useTrackingWidgetStatus';
import { TrackingWidgetStatus } from '../components/TrackingWidgetSetting/types';

interface UseReturnTrackingWidgetLogicProps {
    featureCode?: FeatureCode;
    addWidget?: boolean;
    elementName?: string;
}

interface ReturnTrackingWidgetState {
    isReturnTrackingWidget: boolean;
    isReturnTrackingEnable: boolean;
    showWarningIcon: boolean;
    returnsTrackingTurnOnModal: React.ReactNode;
    onTrackingWidgetClick: () => void;
}

export const useReturnTrackingWidgetLogic = ({
    featureCode,
    addWidget = false,
    elementName,
}: UseReturnTrackingWidgetLogicProps): ReturnTrackingWidgetState => {
    const [{ organization }] = useAuth();

    const {
        elementTree: { getElement },
    } = useAttaEditor();

    // 判断是否为 return tracking widget
    const isReturnTrackingWidget = featureCode === FeatureCode.RETURN_TRACKING;

    // 只有当是 return tracking widget 时才获取状态，避免不必要的请求
    const { isReturnTrackingEnable } = useReturnTrackingStatus(
        isReturnTrackingWidget
    );

    // 获取当前元素的 tracking page id (仅对非添加模式的 tracking widget)
    const currentElement =
        !addWidget && isReturnTrackingWidget
            ? getElement(elementName || '')
            : null;
    const trackingPageId = currentElement?.props?.tracking_page_id;

    // 从后端API获取原始保存的 tracking page ID
    const originalTrackingPageId = useOriginalTrackingPageId();

    // 获取 tracking pages 数据 (仅对有 tracking page id 的 tracking widget)
    const {
        data: trackingPagesData,
        isLoading: isLoadingTrackingPages,
    } = useQueryTrackingPages({
        orgId: organization?.id,
    });

    // 获取订阅数据
    const { data: subscriptionsData } = useQuerySubscriptions({
        productCode: 'returns',
        orgId: organization?.id,
    });

    // 使用现有的tracking订阅检查hook
    const {
        IsAfterShipPayingUser: hasTrackingSubscription,
    } = useIsAfterShipSubscriber();

    // 处理数据转换
    const trackingPages = useMemo(() => {
        return trackingPagesData?.tracking_pages || [];
    }, [trackingPagesData]);

    // 检查是否有 returns 订阅功能
    const hasReturnsSubscription = useMemo(() => {
        const activeSubscriptions = subscriptionsData?.subscriptions?.filter(
            ({ active }) => active
        );
        return Boolean(activeSubscriptions && activeSubscriptions.length > 0);
    }, [subscriptionsData]);

    // 使用状态管理hook检查删除状态 (仅对有 tracking page id 的非添加模式 tracking widget)
    const { currentStatus } = useTrackingWidgetStatus({
        trackingPages,
        hasReturnsSubscription,
        hasTrackingSubscription,
        selectedTrackingPage: trackingPageId || '',
        originalTrackingPageId: originalTrackingPageId || '',
        isLoadingTrackingPages,
    });

    // 判断是否显示红色感叹号 - 当 tracking page 有问题时（被删除或service未启用）
    const showWarningIcon = useMemo(() => {
        return (
            isReturnTrackingWidget &&
            [
                TrackingWidgetStatus.PAGE_DELETED_SINGLE,
                TrackingWidgetStatus.PAGE_DELETED_MULTIPLE,
                TrackingWidgetStatus.PAGE_INVALID,
                TrackingWidgetStatus.SERVICE_DISABLED,
            ].includes(currentStatus)
        );
    }, [isReturnTrackingWidget, currentStatus]);

    // 获取 return tracking 开启弹窗
    const {
        modal: returnsTrackingTurnOnModal,
        onOpen: openTrackingModal,
    } = useReturnsTrackingTurnOnModal();

    const onTrackingWidgetClick = () => {
        // 如果是 return tracking widget 且功能未开启，显示开启弹窗
        if (isReturnTrackingWidget && !isReturnTrackingEnable) {
            openTrackingModal();
        }
    };

    return {
        isReturnTrackingWidget,
        isReturnTrackingEnable,
        showWarningIcon,
        returnsTrackingTurnOnModal,
        onTrackingWidgetClick,
    };
};
