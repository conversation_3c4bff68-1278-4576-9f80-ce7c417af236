import { useAuth } from '@aftership/automizely-product-auth';
import { useMemo } from 'react';
import useQuerySubscriptions from 'resources/billing/useQuerySubscriptions';

interface IProps {
    refetchOnWindowFocus?: boolean;
}

const useIsAfterShipPayingUser = ({
    refetchOnWindowFocus = false,
}: IProps = {}) => {
    const [{ organization }] = useAuth();

    const { data, isLoading } = useQuerySubscriptions({
        productCode: 'aftership',
        orgId: organization?.id,
        refetchOnWindowFocus,
    });

    const IsAfterShipPayingUser = useMemo(() => {
        const activeSubscriptions = data?.subscriptions?.filter(
            ({ active }) => active
        );

        return Boolean(
            activeSubscriptions?.find(
                ({ plan: { type } }) => type === 'main' || type === 'trial'
            )
        );
    }, [data?.subscriptions]);

    return {
        IsAfterShipPayingUser,
        isLoading,
    };
};

export default useIsAfterShipPayingUser;
