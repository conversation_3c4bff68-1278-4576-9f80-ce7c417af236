import {EmptyState as PolarisEmptyState} from '@shopify/polaris';
import React from 'react';

interface Props {
	heading: string;
	content: string;
	action: React.ComponentProps<typeof PolarisEmptyState>['action'];
	image?: string;
}

export default function EmptyState({heading, content, action, image}: Props) {
	return (
		<PolarisEmptyState
			heading={heading}
			action={action}
			image={
				image ||
				'https://cdn.shopify.com/s/files/1/0757/9955/files/empty-state.svg'
			}
		>
			<p>{content}</p>
		</PolarisEmptyState>
	);
}
