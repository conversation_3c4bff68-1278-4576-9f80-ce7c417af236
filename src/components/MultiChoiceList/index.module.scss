.WrapMultiChoiceList__Container,
.WrapSecondChoiceList__Container {
	.searchInput {
		margin-bottom: 8px;
	}
	.container {
		width: 100%;
		display: flex;
		flex-wrap: nowrap;
		justify-content: space-between;
		.label {
			color: #202223;
    		font-weight: 400;
			max-width: calc(100% - 40px);
			text-overflow: clip;
			word-wrap: break-word;
		}
	}
	:global(.Polaris-Choice__Label) {
		width: 100%;
	}
	:global(.Polaris-Choice) {
		width: 100%;
	}
	:global(.Polaris-Icon--colorSubdued) {
		width: 15px;
		height: 15px;
	}
}

.WrapMultiChoiceList__Container_300 {
	width: 300px;
}

.WrapSecondChoiceList__Container .checkboxItem {
	display: flex;
	flex-direction: column;
	// margin-bottom: 8px;
}
