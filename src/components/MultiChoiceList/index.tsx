// @ts-nocheck
import {Text<PERSON>ield, Icon, Stack, TextStyle, ChoiceList} from '@shopify/polaris';
import {SearchMinor} from '@shopify/polaris-icons';
import React, {useState, useEffect, ReactNode} from 'react';
import {Trans, useTranslation} from 'react-i18next';

import styles from './index.module.scss';

interface IProps {
	choices: any;
	renderChoice: any;
	onChange: (value: string[] | undefined) => void;
	withSearchField: boolean;
	placeholder?: string;
	selected: string[];
	customBottom?: React.ReactNode;
	titleRender?: (selected: string[]) => ReactNode;
}

const MultiChoiceList = ({
	choices,
	renderChoice,
	onChange,
	withSearchField,
	placeholder,
	selected,
	customBottom,
	titleRender,
}: IProps) => {
	const {t} = useTranslation();
	const [query, setQuery] = useState('');
	const [searchResults, setSearchResults] = useState(choices);

	const handleChange = value => {
		setQuery(value);
	};

	const onChoiceChange = (value: string[]) => {
		onChange(value.length > 0 ? value : undefined);
	};

	// recalculate choices list to display based on the changes of `query` and `choices` only
	useEffect(() => {
		const queryLowerCase = query.toLocaleLowerCase();
		const results = choices.filter(({label}) =>
			label?.toLowerCase().includes(queryLowerCase)
		);
		setSearchResults(results);
	}, [query, choices]);

	return (
		<>
			<div className={styles.WrapMultiChoiceList__Container}>
				{withSearchField ? (
					<div className={styles.searchInput}>
						<TextField
							label=""
							prefix={<Icon source={SearchMinor} color="base" />}
							placeholder={placeholder || t('SEARCH_6ea19')}
							value={query}
							onChange={handleChange}
							labelHidden
						/>
					</div>
				) : null}
				{titleRender && titleRender(selected)}
				{choices.length > 0 ? (
					<>
						<ChoiceList
							title=""
							choices={searchResults.map(choice => ({
								label: renderChoice(choice),
								value: choice.value,
							}))}
							selected={selected}
							onChange={v => {
								onChoiceChange(v);
							}}
							allowMultiple
						/>
						{withSearchField && !searchResults.length ? (
							<Stack distribution="center">
								<TextStyle variation="subdued">
									{/* t('NO_RESULTS_ee3a3') */}
									<Trans
										i18nKey="NO_RESULTS_ee3a3"
										values={{query}}
										components={{
											strong: (
												<TextStyle variation="strong" />
											),
										}}
									/>
								</TextStyle>
							</Stack>
						) : null}
					</>
				) : (
					<div>
						<TextStyle variation="subdued">
							{t('NO_RESULT_f7f24')}
						</TextStyle>
					</div>
				)}
			</div>
			{customBottom && (
				<div
					style={{
						position: 'relative',
						zIndex: 1,
						marginTop: 15,
						marginBottom: -30,
						background: '#fff',
					}}
				>
					{customBottom}
				</div>
			)}
		</>
	);
};

export * from './CommonChoice';
export {styles};
export default MultiChoiceList;
