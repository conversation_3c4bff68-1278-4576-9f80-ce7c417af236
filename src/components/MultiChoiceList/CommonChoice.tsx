import {TextStyle} from '@shopify/polaris';
import React from 'react';

import styles from './index.module.scss';

interface CommonChoiceProps {
	label: string;
	value?: string | number;
}

export function CommonChoice(props: CommonChoiceProps) {
	return (
		<div className={styles.container}>
			<div className={styles.label}>{props.label}</div>
			{props.value !== undefined && (
				<TextStyle variation="subdued">{props.value}</TextStyle>
			)}
		</div>
	);
}
