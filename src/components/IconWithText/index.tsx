import {Icon, InlineError, Stack, TextStyle} from '@shopify/polaris';
import {AlertMinor, InfoMinor} from '@shopify/polaris-icons';
import React from 'react';

export const IconWithText = ({
	status,
	message,
}: {
	status: 'warning' | 'info' | 'error';
	message: JSX.Element | string;
}) => {
	if (status === 'error') {
		return <InlineError fieldID="" message={message} />;
	}
	return (
		<Stack spacing="tight" wrap={false}>
			<Icon
				source={status === 'warning' ? AlertMinor : InfoMinor}
				color={status === 'warning' ? 'warning' : 'subdued'}
			/>
			{status === 'warning' ? (
				<span style={{color: 'var(--Text-Warning, #916A00)'}}>
					{message}
				</span>
			) : (
				<TextStyle variation="subdued">{message}</TextStyle>
			)}
		</Stack>
	);
};
