import ChartJS, {ChartTooltipModel} from 'chart.js';
import React, {useEffect, useRef, useState} from 'react';
import './baseChart.css';

export type BaseChartProps = {
	height?: number;
	width?: number;
	chartData: ChartJS.ChartConfiguration;
	xPadding?: number;
	customTooltip?: (
		model: ChartJS.ChartTooltipModel,
		chartData: ChartJS.ChartConfiguration
	) => React.ReactNode;
	tooltipOptions?: ChartJS.ChartTooltipOptions;
};

const Tooltip: React.FC<{
	model?: ChartTooltipModel;
	chartData: ChartJS.ChartConfiguration;
	customTooltip?: (
		model: ChartJS.ChartTooltipModel,
		chartData: ChartJS.ChartConfiguration
	) => React.ReactNode;
}> = ({model, chartData, customTooltip}) => {
	if (!model || !model.dataPoints) {
		return null;
	}
	return <>{customTooltip?.(model, chartData)}</>;
};

const BaseChart: React.FC<BaseChartProps> = ({
	chartData,
	customTooltip,
	tooltipOptions,
	height,
	width,
	xPadding,
}) => {
	const [model, setModel] = useState<ChartTooltipModel>();
	const [canvasRef, setCanvasRef] = useState<HTMLCanvasElement | null>(null);
	const chartRef = useRef<ChartJS>();

	useEffect(() => {
		if (!canvasRef) return;
		const mergeData = {...chartData};

		if (customTooltip) {
			if (!mergeData.options) {
				mergeData.options = {};
			}
			mergeData.options.tooltips = {
				enabled: false,
				custom: setModel,
				mode: 'point',
			};

			if (tooltipOptions) {
				mergeData.options.tooltips = {
					...mergeData.options.tooltips,
					...tooltipOptions,
				};
			}
		}

		if (xPadding) {
			const datasets = mergeData?.data?.datasets;
			const labels = mergeData?.data?.labels;
			if (datasets) {
				for (const data of datasets) {
					if (data.data) {
						for (let i = 0; i < xPadding; i++) {
							data.data.push(null as any);
							data.data.unshift(null as any);
						}
					}
				}
			}
			if (labels) {
				for (let i = 0; i < xPadding; i++) {
					labels.push('');
					labels.unshift('');
				}
			}
		}

		if (chartRef.current) {
			chartRef.current.data = mergeData.data!;
			chartRef.current.update();

			return;
		}
		chartRef.current = new ChartJS(canvasRef, mergeData);
	}, [canvasRef, chartData]);

	useEffect(() => {
		return () => {
			chartRef.current?.destroy();
		};
	}, []);

	return (
		<div style={{height, width}} className="Base__Char__wrapper">
			<canvas
				style={{width: '100%', height: '100%'}}
				ref={setCanvasRef}
			/>
			<Tooltip
				model={model}
				chartData={chartData}
				customTooltip={customTooltip}
			/>
		</div>
	);
};

export default BaseChart;
