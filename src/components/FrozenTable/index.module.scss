.frozenTable {
	:global {
		tbody {
			position: relative;
		}

		.Polaris-DataTable__Cell--firstColumn {
			opacity: 1;
			position: sticky !important;
			left: 0;
			z-index: 2;
		}

		.Polaris-DataTable__ScrollContainer::-webkit-scrollbar {
			display: none;
		}

		.Polaris-DataTable__Cell {
			background-color: #fff;
			padding: 0;
		}

		.Polaris-DataTable__Cell--header {
			border-top: 0.1rem solid var(--p-divider);
			border-bottom: 0.1rem solid var(--p-divider);
			font-weight: normal;
		}

		.Polaris-DataTable--condensed .Polaris-DataTable__Navigation {
			display: none;
		}

		.Polaris-DataTable__TableRow:nth-child(even) {
			background: #fafbfb;
		}

		.Polaris-DataTable__TableRow {
			.removeBorderTop {
				border: none;
			}
		}

		.as-shipment-own-scroll-bar::-webkit-scrollbar {
			background-color: transparent;
			height: 8px;
		}
		.as-shipment-own-scroll-bar::-webkit-scrollbar-thumb {
			background: #8c9196;
			border-radius: 35px;
			height: 8px;
		}
	}
}
.scrollBar {
	position: sticky;
	bottom: 4px;
	height: 20px;
	z-index: 2;
	font-size: 0 !important;
	overflow-x: scroll;
	margin-top: -12px;
}

.scrollBar::-webkit-scrollbar {
	background-color: transparent;
	height: 10px;
}
.scrollBar::-webkit-scrollbar-thumb {
	background: #8c9196;
	border-radius: 35px;
	height: 10px;
}

.scrolled {
	:global {
		.Polaris-DataTable__Cell--firstColumn {
			box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
		}
	}
}

.frozenTable .frozenTableCell {
	padding: 16px;
}

.loading {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 3;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: var(--p-overlay);
}
