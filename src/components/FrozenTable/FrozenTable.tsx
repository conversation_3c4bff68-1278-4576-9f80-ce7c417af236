/* eslint-disable jsx-a11y/no-static-element-interactions */

import {DataTable, Spinner} from '@shopify/polaris';
import React, {useEffect, useState} from 'react';
import {createPortal} from 'react-dom';

import {FrozenTableAvgColumn} from './FrozenTableAvgColumn';
import {FrozenTableColumn} from './FrozenTableColumn';
import {FrozenTableRow} from './FrozenTableRow';
import styles from './index.module.scss';

export type FrozenTableProps = InternalTableProps;

export interface FrozenTable extends React.FC<InternalTableProps> {
	Row: typeof FrozenTableRow;
	Column: typeof FrozenTableColumn;
	AvgColumn: typeof FrozenTableAvgColumn;
}

interface InternalTableProps {
	header: JSX.Element | JSX.Element[];
	children: JSX.Element | JSX.Element[];
	loading?: boolean;
}

const InternalTable = (props: InternalTableProps) => {
	const [frozenTableRef, setFrozenTableRef] = useState<HTMLDivElement | null>(
		null
	);
	const [totalScrollWidth, setTotalScrollWidth] = useState(0);
	const [totalVisibleWidth, setTotalVisibleWidth] = useState(0);
	const [isOptionOnOwnScroll, setIsOptionOnOwnScroll] = useState(false);

	const {header, children, loading} = props;
	const headEle = frozenTableRef?.querySelector('thead') as HTMLElement;
	const bodyEle = frozenTableRef?.querySelector('tbody') as HTMLElement;

	useEffect(() => {
		const frozenTableScrollContainer = frozenTableRef?.querySelector(
			'.Polaris-DataTable__ScrollContainer'
		) as Element;
		const frozenTableContainer = frozenTableRef?.querySelector(
			'.Polaris-DataTable__Table'
		) as Element;

		const totalScrollWidth = frozenTableContainer?.clientWidth || 0;

		setTotalScrollWidth(totalScrollWidth);
		setTotalVisibleWidth(frozenTableScrollContainer?.clientWidth || 0);

		const scrollEvent = () => {
			if (!isOptionOnOwnScroll) {
				const asOwnShipmentScrollBar = (frozenTableRef
					?.closest('.Polaris-Layout')
					?.querySelector('.as-shipment-own-scroll-bar') ||
					frozenTableRef
						?.closest('.Polaris-Page')
						?.querySelector('.as-shipment-own-scroll-bar') ||
					document?.querySelector(
						'.shipment-table-modal .as-shipment-own-scroll-bar'
					)) as Element;

				asOwnShipmentScrollBar.scrollLeft =
					frozenTableScrollContainer?.scrollLeft || 0;
			}
		};

		const onResize = () => {
			setTotalScrollWidth(
				(
					frozenTableRef?.querySelector(
						'.Polaris-DataTable__Table'
					) as Element
				)?.clientWidth
			);
			setTotalVisibleWidth(
				(
					frozenTableRef?.querySelector(
						'.Polaris-DataTable__ScrollContainer'
					) as Element
				)?.clientWidth || 0
			);
		};
		window.addEventListener('resize', onResize, true);

		frozenTableRef
			?.querySelector('.Polaris-DataTable__ScrollContainer')
			?.addEventListener('scroll', () => {
				scrollEvent();
			});

		return (
			frozenTableRef
				?.querySelector('.Polaris-DataTable__ScrollContainer')
				?.removeEventListener('scroll', () => {
					scrollEvent();
				}),
			window.removeEventListener('resize', onResize)
		);
	}, [
		frozenTableRef,
		(frozenTableRef?.querySelector('.Polaris-DataTable__Table') as Element)
			?.clientWidth,
	]);

	useEffect(() => {
		if (!frozenTableRef) return () => {};

		const onScroll = (ev: Event) => {
			const target = ev.target as HTMLElement;
			if (!frozenTableRef.contains(target)) return;
			const {scrollLeft} = target;
			if (scrollLeft > 0) {
				frozenTableRef.classList.add(styles.scrolled);
			} else {
				frozenTableRef.classList.remove(styles.scrolled);
			}
		};
		window.addEventListener('scroll', onScroll, true);
		return () => {
			window.removeEventListener('scroll', onScroll, true);
		};
	}, [frozenTableRef]);

	return (
		<>
			<div className={styles.frozenTable} ref={setFrozenTableRef}>
				<DataTable columnContentTypes={[]} headings={[]} rows={[]} />
				{headEle && createPortal(header, headEle)}
				{bodyEle &&
					createPortal(
						<>
							{children}
							{loading && (
								<div className={styles.loading}>
									<Spinner />
								</div>
							)}
						</>,
						bodyEle
					)}
			</div>

			<div
				className={`${styles.scrollBar} as-shipment-own-scroll-bar`}
				style={{
					width: `${totalVisibleWidth}px`,
				}}
				onMouseEnter={() => {
					setIsOptionOnOwnScroll(true);
				}}
				onMouseLeave={() => {
					setIsOptionOnOwnScroll(false);
				}}
				onScroll={() => {
					const frozenTableScrollContainer = (frozenTableRef
						?.closest('.Polaris-Layout')
						?.querySelector(
							'.Polaris-DataTable__ScrollContainer'
						) ||
						frozenTableRef
							?.closest('.Polaris-Page')
							?.querySelector(
								'.Polaris-DataTable__ScrollContainer'
							) ||
						document?.querySelector(
							'.shipment-table-modal .Polaris-DataTable__ScrollContainer'
						)) as Element;

					if (isOptionOnOwnScroll) {
						frozenTableScrollContainer.scrollLeft =
							frozenTableRef
								?.closest('.Polaris-Layout')
								?.querySelector('.as-shipment-own-scroll-bar')
								?.scrollLeft ||
							frozenTableRef
								?.closest('.Polaris-Page')
								?.querySelector('.as-shipment-own-scroll-bar')
								?.scrollLeft ||
							document?.querySelector(
								'.shipment-table-modal .as-shipment-own-scroll-bar'
							)?.scrollLeft ||
							0;
					}
				}}
			>
				<div
					style={{
						width: `${totalScrollWidth}px`,
						height: '8px',
					}}
				/>
			</div>
		</>
	);
};

const FrozenTable = InternalTable as unknown as FrozenTable;
FrozenTable.Row = FrozenTableRow;
FrozenTable.Column = FrozenTableColumn;
FrozenTable.AvgColumn = FrozenTableAvgColumn;

export default FrozenTable;
