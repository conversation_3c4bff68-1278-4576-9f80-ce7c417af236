import React from 'react';

export interface FrozenTableAvgColumnProps {
	span?: number;
	fullWidth?: boolean;
	lastAlignRight?: boolean;
	children: JSX.Element | JSX.Element[];
}

export const FrozenTableAvgColumn: React.FC<FrozenTableAvgColumnProps> =
	props => {
		const length = React.Children.count(props.children);
		return (
			<td className="Polaris-DataTable__Cell ">
				<div style={{display: 'flex', textAlign: 'left'}}>
					{React.Children.map(
						props.children,
						(item: JSX.Element, index: number) => (
							<div
								key={index.toString()}
								style={{
									margin: '10px 0',
									padding: '0 16px',
									width: `${(1 / length) * 100}%`,
									flexShrink: 0,
									textAlign:
										index === length - 1 &&
										props.lastAlignRight
											? 'right'
											: 'left',
								}}
							>
								{item.props.children}
							</div>
						)
					)}
				</div>
			</td>
		);
	};
