import React from 'react';

import {classnames} from 'utils/classnames';

import styles from './index.module.scss';

export interface FrozenTableColumnProps {
	width?: number;
	span?: number;
	children: React.ReactNode;
	fullWidth?: boolean;
	className?: string;
	tableCellStyle?: React.CSSProperties;
}

export const FrozenTableColumn: React.FC<FrozenTableColumnProps> = props => {
	return (
		<td
			className={classnames('Polaris-DataTable__Cell', props.className)}
			colSpan={props.span}
			style={{width: props.width || 'initial'}}
		>
			<div
				style={{
					padding: props.fullWidth ? 0 : undefined,
					...props.tableCellStyle,
				}}
				className={styles.frozenTableCell}
			>
				{props.children}
			</div>
		</td>
	);
};
