import React from 'react';

import {classnames} from 'utils/classnames';

import {FrozenTableColumn} from './FrozenTableColumn';

export const FrozenTableRow: React.FC<{
	children:
		| React.ReactElement<typeof FrozenTableColumn>
		| Array<React.ReactElement<typeof FrozenTableColumn>>;
	hoverable?: boolean;
	isHeader?: boolean;
	onClick?: VoidFunction;
	onMouseEnter?: VoidFunction;
	onMouseLeave?: VoidFunction;
}> = props => {
	const {
		isHeader,
		hoverable = !isHeader,
		onClick,
		onMouseEnter,
		onMouseLeave,
	} = props;

	return (
		<tr
			className={`Polaris-DataTable__TableRow ${
				hoverable ? 'Polaris-DataTable--hoverable' : ''
			}`}
			onClick={onClick}
			onMouseEnter={onMouseEnter}
			onMouseLeave={onMouseLeave}
		>
			{React.Children.map(
				props.children,
				(item: JSX.Element, index: number) =>
					React.cloneElement(item, {
						...item.props,
						className: classnames(
							item.props?.className,
							isHeader && 'Polaris-DataTable__Cell--header',
							index === 0 &&
								'Polaris-DataTable__Cell--firstColumn'
						),
					})
			)}
		</tr>
	);
};
