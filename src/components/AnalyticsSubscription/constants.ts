export const subDashboardKey = [
	'exceptionRateOverTimeTable',
	'highestExceptionRateTable',
	'highestShippingVolumeTable',
	'highestShippingVolumeByLaneTable',
	'onTimeShipmentByLaneTable',
	'transitTimeDistributionTable',
	'onTimeShipmentsByCarrierServiceTable',
	'shipmentsOverTimeTable',
	'successfulDeliveryAttemptsByCarrierTable',
	'successfulDeliveryAttemptsOverTimeTable',
	'transitTimeByCarrierServiceTable',
	'transitTimeByLaneTable',
	'transitTimeAvgOverTimeTable',
	'transitTimePercentileOverTimeTable',
	'orderToDeliveryAvgOrderToDeliveryTimeOverTimeTable',
	'orderToDeliveryTimeDistributionTable',
	'orderToDeliveryAvgProcessingTimeOverTimeTable',
	'orderToDeliveryProcessingTimeDistributionTable',
	'orderToDeliveryAvgPickupTimeOverTimeTable',
	'orderToDeliveryPickupTimeDistributionTable',
	'orderToDeliveryAvgTransitTimeOverTimeTable',
	'orderToDeliveryTransitTimeDistributionTable',

	'trackingPagesPerformanceOverTimeTable',
	'trackingPagesAttributedOrdersOverTimeTable',
	'trackingPagesAttributedRevenueOverTimeTable',
	'trackingPagesMarketingClicksBySectionTable',
	'trackingPagesTopMarketingAssetsTable',
	'trackingPagesTopProductRecommendationsTable',
	'trackingPageClickThroughRateTable',
	'trackingPagesPerformanceOverTimeTable',
	'trackingPagesAttributedOrdersOverTimeTable',
	'trackingPagesAttributedRevenueOverTimeTable',
	'trackingPagesMarketingClicksBySectionTable',
	'trackingPagesTopMarketingAssetsTable',
	'trackingPagesTopProductRecommendationsTable',
	'shipmentsByDestinationTable',
	'averageTransitTimeByDestination',
	'shipmentReviewsRatingByCarrierTable',
	'shipmentReviewsRatingOverTimeTable',
];

export const filterPermissionKeyMap = {
	datePicker: [
		'shipments',
		'exceptions',
		'onTimeShipments',
		'transitTimes',
		'notifications',
		'shipmentReviews',
		'shipmentReviewsRatingByCarrierTable',
		'shipmentReviewsRatingOverTimeTable',
		'exceptionRateOverTimeTable',
		'highestExceptionRateTable',
		'highestShippingVolumeTable',
		'highestShippingVolumeByLaneTable',
		'onTimeShipmentByLaneTable',
		'onTimeShipmentsByCarrierServiceTable',
		'shipmentsOverTimeTable',
		'successfulDeliveryAttemptsByCarrierTable',
		'successfulDeliveryAttemptsOverTimeTable',
		'transitTimeByCarrierServiceTable',
		'transitTimeByLaneTable',
		'transitTimeAvgOverTimeTable',
		'transitTimePercentileOverTimeTable',
		'shipmentsByDestinationTable',
		'averageTransitTimeByDestination',
		'transitTimeDistributionTable',
		// order-to-delivery dashboard
		'orderToDeliveryTime',
		'orderToDeliveryAvgOrderToDeliveryTimeOverTimeTable',
		'orderToDeliveryTimeDistributionTable',
		'orderToDeliveryAvgProcessingTimeOverTimeTable',
		'orderToDeliveryProcessingTimeDistributionTable',
		'orderToDeliveryAvgPickupTimeOverTimeTable',
		'orderToDeliveryPickupTimeDistributionTable',
		'orderToDeliveryAvgTransitTimeOverTimeTable',
		'orderToDeliveryTransitTimeDistributionTable',

		// Tracking page analytics dashboard
		'trackingPages',
		'trackingPagesPerformanceOverTimeTable',
		'trackingPagesAttributedOrdersOverTimeTable',
		'trackingPagesAttributedRevenueOverTimeTable',
		'trackingPagesMarketingClicksBySectionTable',
		'trackingPagesTopMarketingAssetsTable',
		'trackingPagesTopProductRecommendationsTable',
	],
	compareDatePicker: [
		'shipments',
		'exceptions',
		'onTimeShipments',
		'transitTimes',
		'orderToDeliveryTime',
		'trackingPages',
	],
	locationPicker: [
		'shipments',
		'exceptions',
		'onTimeShipments',
		'transitTimes',
		'transitTimeDistributionTable',
		'exceptionRateOverTimeTable',
		'highestExceptionRateTable',
		'highestShippingVolumeTable',
		'highestShippingVolumeByLaneTable',
		'onTimeShipmentByLaneTable',
		'onTimeShipmentsByCarrierServiceTable',
		'shipmentsOverTimeTable',
		'successfulDeliveryAttemptsByCarrierTable',
		'successfulDeliveryAttemptsOverTimeTable',
		'transitTimeByCarrierServiceTable',
		'transitTimeByLaneTable',
		'transitTimeAvgOverTimeTable',
		'transitTimePercentileOverTimeTable',
		'shipmentsByDestinationTable',
		'averageTransitTimeByDestination',

		// order-to-delivery dashboard
		'orderToDeliveryTime',
		'orderToDeliveryAvgOrderToDeliveryTimeOverTimeTable',
		'orderToDeliveryTimeDistributionTable',
		'orderToDeliveryAvgProcessingTimeOverTimeTable',
		'orderToDeliveryProcessingTimeDistributionTable',
		'orderToDeliveryAvgPickupTimeOverTimeTable',
		'orderToDeliveryPickupTimeDistributionTable',
		'orderToDeliveryAvgTransitTimeOverTimeTable',
		'orderToDeliveryTransitTimeDistributionTable',
	],
	carrierPicker: [
		'shipments',
		'exceptions',
		'onTimeShipments',
		'transitTimes',
		'transitTimeDistributionTable',
		'exceptionRateOverTimeTable',
		'highestExceptionRateTable',
		'highestShippingVolumeTable',
		'highestShippingVolumeByLaneTable',
		'onTimeShipmentByLaneTable',
		'onTimeShipmentsByCarrierServiceTable',
		'shipmentsOverTimeTable',
		'successfulDeliveryAttemptsByCarrierTable',
		'successfulDeliveryAttemptsOverTimeTable',
		'transitTimeByCarrierServiceTable',
		'transitTimeByLaneTable',
		'transitTimeAvgOverTimeTable',
		'transitTimePercentileOverTimeTable',
		'shipmentsByDestinationTable',
		'averageTransitTimeByDestination',

		// order to delivery dashboard
		'orderToDeliveryTime',
		'orderToDeliveryAvgOrderToDeliveryTimeOverTimeTable',
		'orderToDeliveryTimeDistributionTable',
		'orderToDeliveryAvgProcessingTimeOverTimeTable',
		'orderToDeliveryProcessingTimeDistributionTable',
		'orderToDeliveryAvgPickupTimeOverTimeTable',
		'orderToDeliveryPickupTimeDistributionTable',
		'orderToDeliveryAvgTransitTimeOverTimeTable',
		'orderToDeliveryTransitTimeDistributionTable',
	],
	customFieldPicker: [
		'shipments',
		'exceptions',
		'onTimeShipments',
		'transitTimes',
		'transitTimeDistributionTable',
		'notifications',
		'exceptionRateOverTimeTable',
		'highestExceptionRateTable',
		'highestShippingVolumeTable',
		'highestShippingVolumeByLaneTable',
		'onTimeShipmentByLaneTable',
		'onTimeShipmentsByCarrierServiceTable',
		'shipmentsOverTimeTable',
		'successfulDeliveryAttemptsByCarrierTable',
		'successfulDeliveryAttemptsOverTimeTable',
		'transitTimeByCarrierServiceTable',
		'transitTimeByLaneTable',
		'transitTimeAvgOverTimeTable',
		'transitTimePercentileOverTimeTable',
		'shipmentsByDestinationTable',
		'averageTransitTimeByDestination',

		// order to delivery dashboard
		'orderToDeliveryTime',
		'orderToDeliveryAvgOrderToDeliveryTimeOverTimeTable',
		'orderToDeliveryTimeDistributionTable',
		'orderToDeliveryAvgProcessingTimeOverTimeTable',
		'orderToDeliveryProcessingTimeDistributionTable',
		'orderToDeliveryAvgPickupTimeOverTimeTable',
		'orderToDeliveryPickupTimeDistributionTable',
		'orderToDeliveryAvgTransitTimeOverTimeTable',
		'orderToDeliveryTransitTimeDistributionTable',
	],
	moreFiltersPicker: [
		'shipments',
		'exceptions',
		'onTimeShipments',
		'transitTimes',
		'transitTimeDistributionTable',
		'exceptionRateOverTimeTable',
		'highestExceptionRateTable',
		'highestShippingVolumeTable',
		'highestShippingVolumeByLaneTable',
		'onTimeShipmentByLaneTable',
		'onTimeShipmentsByCarrierServiceTable',
		'shipmentsOverTimeTable',
		'successfulDeliveryAttemptsByCarrierTable',
		'successfulDeliveryAttemptsOverTimeTable',
		'transitTimeByCarrierServiceTable',
		'transitTimeByLaneTable',
		'transitTimeAvgOverTimeTable',
		'transitTimePercentileOverTimeTable',
		'shipmentsByDestinationTable',
		'averageTransitTimeByDestination',

		// order to delivery dashboard
		'orderToDeliveryTime',
		'orderToDeliveryAvgOrderToDeliveryTimeOverTimeTable',
		'orderToDeliveryTimeDistributionTable',
		'orderToDeliveryAvgProcessingTimeOverTimeTable',
		'orderToDeliveryProcessingTimeDistributionTable',
		'orderToDeliveryAvgPickupTimeOverTimeTable',
		'orderToDeliveryPickupTimeDistributionTable',
		'orderToDeliveryAvgTransitTimeOverTimeTable',
		'orderToDeliveryTransitTimeDistributionTable',
	],
	trackingPagePicker: [
		'trackingPages',
		'trackingPagesPerformanceOverTimeTable',
		'trackingPagesAttributedOrdersOverTimeTable',
		'trackingPagesAttributedRevenueOverTimeTable',
		'trackingPagesMarketingClicksBySectionTable',
		'trackingPagesTopMarketingAssetsTable',
		'trackingPagesTopProductRecommendationsTable',

		'benchmarks',
		'trackingPageClickThroughRateTable',
		'trackingPagesPerformanceOverTimeTable',
		'trackingPagesAttributedOrdersOverTimeTable',
		'trackingPagesAttributedRevenueOverTimeTable',
		'trackingPagesMarketingClicksBySectionTable',
		'trackingPagesTopMarketingAssetsTable',
		'trackingPagesTopProductRecommendationsTable',
	],
	shipmentReviewsPicker: [
		'shipmentReviews',
		'shipmentReviewsRatingByCarrierTable',
		'shipmentReviewsRatingOverTimeTable',
	],
	benchmarkTimePicker: ['benchmarks', 'trackingPageClickThroughRateTable'],
	benchmarkIndustryPicker: [
		'benchmarks',
		'trackingPageClickThroughRateTable',
	],
};

export const dashboardNameKeyMap: Record<string, string> = {
	exceptionRateOverTimeTable: 'Exception rate over time',
	exceptions: 'Exceptions',
	highestExceptionRateTable: 'Highest exception rate',
	highestShippingVolumeTable: 'Highest shipping volume by carrier',
	notifications: 'Notifications',
	onTimeShipmentByLaneTable: 'On-time shipments by lane',
	onTimeShipments: 'On-time shipments',
	onTimeShipmentsByCarrierServiceTable:
		'On-time shipments by carrier service',
	shipmentReviews: 'Shipment reviews',
	shipments: 'Shipments',
	shipmentsOverTimeTable: 'Shipments over time',
	successfulDeliveryAttemptsByCarrierTable:
		'Successful delivery attempts by carrier',
	successfulDeliveryAttemptsOverTimeTable:
		'Successful delivery attempts over time',
	transitTimeByCarrierServiceTable: 'Transit time by carrier service',
	transitTimeByLaneTable: 'Transit time by lane',
	transitTimeAvgOverTimeTable: 'Average transit time over time (days)',
	transitTimePercentileOverTimeTable:
		'Percentile transit times over time (days)',
	transitTimes: 'Transit time',
	transitTimeDistributionTable: 'Transit time distribution',
	orderToDeliveryTime: 'Order-to-delivery time',
	orderToDeliveryAvgOrderToDeliveryTimeOverTimeTable:
		'Order-to-delivery time over time',
	orderToDeliveryTimeDistributionTable: 'Order-to-delivery time distribution',
	orderToDeliveryAvgProcessingTimeOverTimeTable: 'Processing time over time',
	orderToDeliveryProcessingTimeDistributionTable:
		'Processing time distribution',
	orderToDeliveryAvgPickupTimeOverTimeTable: 'Pickup time over time',
	orderToDeliveryPickupTimeDistributionTable: 'Pickup time distribution',
	orderToDeliveryAvgTransitTimeOverTimeTable: 'Transit time over time',
	orderToDeliveryTransitTimeDistributionTable: 'Transit time distribution',
	trackingPages: 'Tracking pages',
	trackingPagesPerformanceOverTimeTable:
		'Tracking pages performance over time',
	trackingPagesAttributedOrdersOverTimeTable: 'Attributed orders over time',
	trackingPagesAttributedRevenueOverTimeTable: 'Attributed revenue over time',
	trackingPagesMarketingClicksBySectionTable: 'Marketing clicks by section',
	trackingPagesTopMarketingAssetsTable: 'Top marketing assets by clicks',
	trackingPagesTopProductRecommendationsTable:
		'Top product recommendations by clicks',
	benchmarks: 'Benchmarks',
	trackingPageClickThroughRateTable: 'Tracking page click through rate (CTR)',
	highestShippingVolumeByLaneTable: 'Highest shipping volume by lane',
	averageTransitTimeByDestination: 'Average transit time by destination',
	shipmentsByDestinationTable: 'Shipments by destination',
	shipmentReviewsRatingByCarrierTable: 'Average rating by carrier',
	shipmentReviewsRatingOverTimeTable: 'Average rating over time',
};
