import {Modal} from '@shopify/polaris';
import React, {useMemo} from 'react';
import {useTranslation} from 'react-i18next';

interface Props {
	isRecurring: boolean;
	isOpen: boolean;
	onClose: () => void;
	onSubmit: () => void;
}

export default function ExternalRecipientCheckModal({
	isRecurring,
	isOpen,
	onClose,
	onSubmit,
}: Props) {
	const {t} = useTranslation();

	const title = useMemo(() => {
		return isRecurring
			? t('SCHEDULE_E_f3f40', 'Schedule email for external recipients?')
			: t('SEND_EMAIL_e9a7f', 'Send email to external recipients?');
	}, [isRecurring, t]);

	const submitText = useMemo(() => {
		return isRecurring
			? t('SCHEDULE_E_fb2a9', 'Schedule email')
			: t('SEND_EMAIL_fa27c', 'Send Email');
	}, [isRecurring, t]);

	return (
		<Modal
			title={title}
			open={isOpen}
			onClose={onClose}
			primaryAction={{
				content: submitText,
				onAction: onSubmit,
			}}
			secondaryActions={[
				{
					content: t('CANCEL_ea478', 'Cancel'),
					onAction: onClose,
				},
			]}
		>
			<Modal.Section>
				{t(
					'YOU_WILL_B_02574',
					'You will be sharing this data externally. Make sure that you have permission to do this, and that the recipient emails are correct.'
				)}
			</Modal.Section>
		</Modal>
	);
}
