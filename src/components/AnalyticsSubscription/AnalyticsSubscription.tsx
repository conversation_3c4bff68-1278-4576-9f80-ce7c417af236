/* eslint-disable max-lines */
import {useAuth} from '@aftership/automizely-product-auth';
import {
	Button,
	Icon,
	Scrollable,
	Sheet,
	Spinner,
	Stack,
	Select as PolarisSelect,
	TextStyle,
} from '@shopify/polaris';
import {
	ChevronRightMinor,
	CodeMajor,
	FilterMajor,
	MobileCancelMajor,
} from '@shopify/polaris-icons';
import {Formik, FormikProps} from 'formik';
import {omit} from 'lodash';
import {stringify} from 'query-string';
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';
import * as yup from 'yup';

import {
	Email_Recipient_Type,
	ExportType,
	Schedule_Type,
	Shipment_Analytics_Subscription_Types,
	useGetShipmentAnalyticsSubscriptionQuery,
	useGetShipmentAnalyticsSubscriptionsQuery,
	useTrackingPagesQuery,
	Email_Recipient_Group,
} from '@graphql/generated';
import {addBanner} from 'actions/banners';
import {addToast} from 'actions/toast';
import CustomFieldsSheet from 'components/CustomFieldsSheet';
import MoreFiltersSheet from 'components/MoreFiltersSheet';
import {PAGE_BANNER} from 'constants/BannerNames';
import {DATE_WITH_DASH} from 'constants/Date';
import WarnAboutUnsavedChanges from 'email_editor/main/components/WarnAboutUnsavedChanges';
import useIsDisableExport from 'hooks/useIsDisableExport';
import {BenchmarkCompareTypeFilter} from 'pages/Dashboard/BenchmarkDashboard/filters/BenchmarkCompareTypeFilter';
import {BenchmarkTimeRangeFilter} from 'pages/Dashboard/BenchmarkDashboard/filters/BenchmarkTimeRangeFilter';
import {
	CarrierFilter,
	LocationFilter,
} from 'pages/Dashboard/components/FilterHeader/filters';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {filtersToFeAdvancedFilter} from 'pages/Shipments/utils/formatFilterV2';
import {Select} from 'pages/TrackingPageEditor/components/FastSelectField';
import {
	ActionGroup,
	DeliverySchedule,
	DiscardChangeModal,
	EmailContent,
	UnableSendEmailModal,
} from 'pages/components/EmailSubscription';
import {Checkbox} from 'pages/components/FormikedPolaris';
import {toOrgMoment} from 'utils/day';
import {queryClient} from 'utils/network';

import AnalyticsRecipientSection from './AnalyticsRecipientSection';
import {checkIsInternalOrExternalRecipientEnabled} from './AnalyticsRecipientSection/utils';
import ExternalRecipientCheckModal from './ExternalRecipientCheckModal/ExternalRecipientCheckModal';
import {DateFilter} from './SubcriptionFilters/DateFilter/DateFilter';
import {
	getComparePeriodRange,
	getStartTimeAndEndTime,
	TIME_RANGE,
} from './SubcriptionFilters/DateFilter/utils';
import ReviewsFilters from './SubcriptionFilters/ReviewsFilters/ReviewsFilters';
import {filterPermissionKeyMap, subDashboardKey} from './constants';
import {
	useAnalyticsSubscriptionAction,
	useSendAnalyticsEmail,
} from './hooks/useAnalyticsSubscriptionAction';
import style from './index.module.scss';
import {
	convertValues,
	decodeCountries,
	decodeSlugs,
	FormValueProps,
} from './utils';

interface Props {
	open: boolean;
	onClose: () => void;
	type: 'edit' | 'create' | 'default';
	analyticsDashboardName?: any;
	subscriptionId?: string;
	analyticsDashboardKey: Shipment_Analytics_Subscription_Types;
	onSuccess?: () => void;
}

const AnalyticsSubscription = ({
	open,
	onClose,
	type: emailType,
	subscriptionId,
	analyticsDashboardName,
	analyticsDashboardKey,
	onSuccess,
}: Props) => {
	const {t} = useTranslation();
	const formikRef = useRef<FormikProps<FormValueProps>>(null);
	const dispatch = useDispatch();
	// TODO
	const [discardModalVisible, setDiscardVisible] = useState(false);
	const [
		externalRecipientCheckModalVisible,
		setExternalRecipientCheckModalVisible,
	] = useState(false);
	const [{user, organization}] = useAuth();
	const accountId = user?.account_id || '';
	const [isOpenCustomFieldConfig, setIsOpenCustomFieldConfig] =
		useState(false);
	const timezone = organization?.timezone_identifier || '';

	const handleClose = (dirty?: boolean) => {
		if (dirty) {
			setDiscardVisible(true);
		} else {
			onClose();
		}
	};

	const {minDate} = useDatePickerRangeOptions();
	const isDisableExport = useIsDisableExport();

	const divRef = useRef<HTMLDivElement>(null);

	const {
		sendEmail,
		sendEmailLoading,
		retryModalVisible,
		setRetryModalVisible,
	} = useSendAnalyticsEmail();
	const [isOpenMoreFiltersSheet, setIsOpenMoreFiltersSheet] = useState(false);

	const title = useMemo(() => {
		let title = '';
		switch (emailType) {
			case 'edit':
				title = t('EDITRECURR_32d42');
				break;
			case 'create':
				title = t(
					subDashboardKey.includes(
						analyticsDashboardKey || 'shipments'
					)
						? 'EMAILREPOR_e4aff'
						: 'EMAILDASHB_5ecaf'
				);
				break;
			default:
				title = t(
					subDashboardKey.includes(
						analyticsDashboardKey || 'shipments'
					)
						? 'EMAILREPOR_e4aff'
						: 'EMAILDASHB_5ecaf'
				);
		}
		return title;
	}, [emailType, t, analyticsDashboardKey]);

	const {data, isLoading, refetch} = useGetShipmentAnalyticsSubscriptionQuery(
		{id: subscriptionId || ''},
		{enabled: emailType === 'edit' && Boolean(subscriptionId)}
	);
	const {
		createAnalyticsSubscription,
		updateAnalyticsSubscription,
		createLoading,
		updateLoading,
		subscriptionsLength,
	} = useAnalyticsSubscriptionAction({
		onSuccess: () => {
			onClose();
			onSuccess?.();
			subscriptionId && refetch();
			queryClient.invalidateQueries(
				useGetShipmentAnalyticsSubscriptionsQuery.getKey({
					type: analyticsDashboardKey,
				})
			);
		},
		analyticsDashboardKey: analyticsDashboardKey || 'shipments',
	});

	const onSendEmail = (values: FormValueProps, isTest?: boolean) => {
		const adminPageQuery = stringify({
			'start-date': values.originFilter?.startDate,
			'end-date': values.originFilter?.endDate,
			'start-time': toOrgMoment(values.originFilter?.startDate).format(),
			'end-time': toOrgMoment(values.originFilter?.endDate)
				.endOf('day')
				.format(),
			'date-type': values.originFilter?.dateType,
		});
		const request = convertValues(values, accountId);
		const sendEmailInput = omit(request, [
			'name',
			'schedule',
			'sendEmptyResult',
		]);
		const testEmailInput = {
			...sendEmailInput,
			emailRecipientType: Email_Recipient_Type.Me,
			emailRecipientGroup: Email_Recipient_Group.Internal,
			emailRecipients: [accountId],
			type: (analyticsDashboardKey ||
				'') as Shipment_Analytics_Subscription_Types,
			sendEmptyResult: true,
			accountId: user?.account_id || '',
			adminPageQuery: adminPageQuery,
			timezone: timezone || 'Asia/Shanghai',
		};

		sendEmail(
			{
				input: isTest
					? testEmailInput
					: {
							...sendEmailInput,
							type: (analyticsDashboardKey ||
								'') as Shipment_Analytics_Subscription_Types,
							sendEmptyResult: true,
							accountId: user?.account_id || '',
							adminPageQuery: adminPageQuery,
							timezone: timezone || 'Asia/Shanghai',
					  },
			},
			{
				onSuccess: () => {
					if (sendEmailInput.exportFileType) {
						dispatch(
							addBanner({
								id: PAGE_BANNER,
								status: 'info',
								title: t('PROCESSING_24092'),
								message: t('GENERATING_61fb3'),
								ensureInView: true,
							})
						);
					} else {
						dispatch(
							addToast({
								message: isTest
									? t('EMAIL_SENT_TO_dfl234', {
											email: user?.email,
									  })
									: t('EMAIL_SENT_SDfk2342'),
							})
						);
					}
					!isTest && onClose();
				},
			}
		);
	};

	const subscription = data?.shipmentAnalyticsSubscription;
	const initialValues = useMemo(() => {
		const {
			name,
			emailRecipientType,
			emailRecipients,
			emailSubject,
			emailText,
			schedule,
			sendEmptyResult,
			exportFileType,
			originFilter,
			type,
			emailRecipientGroup,
			emailExternalRecipients,
		} = subscription || {};

		const {start, end} = getStartTimeAndEndTime(
			originFilter?.timeRange as TIME_RANGE
		);
		return {
			isRecurring: ['true'],
			name: name || '',
			emailRecipientType: [
				emailRecipientType || Email_Recipient_Type.SpecificMembers,
			],
			emailRecipientGroup: emailRecipientGroup as Email_Recipient_Group,
			emailExternalRecipients: emailExternalRecipients || [],
			type: type || analyticsDashboardKey || '',
			emailRecipients: emailRecipients || [],
			emailSubject: emailSubject || analyticsDashboardName || '',
			emailText: emailText || '',
			schedule: {
				type: schedule?.type || Schedule_Type.Weekly,
				sendAt: schedule?.sendAt || '09:00',
				timezone: timezone || 'Asia/Shanghai',
				weeklyRule: schedule?.weeklyRule?.map(String) || [],
				dayInMonthRule: schedule?.dayInMonthRule?.map(String) || [],
				weekdayInMonthRule: {
					index: String(schedule?.weekdayInMonthRule?.index || 1),
					weekday:
						schedule?.weekdayInMonthRule?.weekday === 0
							? '0'
							: String(
									schedule?.weekdayInMonthRule?.weekday || 1
							  ),
				},
			},
			sendEmptyResult:
				sendEmptyResult !== undefined ? sendEmptyResult : true,
			exportFileType: exportFileType || '',
			originFilter:
				emailType === 'create' || emailType === 'default'
					? {
							...originFilter,
							startTime: toOrgMoment()
								.subtract(29, 'days')
								.toDate(),
							endTime: toOrgMoment().toDate(),
							startDate: toOrgMoment()
								.subtract(29, 'days')
								.format(DATE_WITH_DASH),
							endDate: toOrgMoment().format(DATE_WITH_DASH),
							compareStartTime: getComparePeriodRange({
								startTime: toOrgMoment()
									.subtract(29, 'days')
									.toDate(),
								endTime: toOrgMoment().toDate(),
								compareType: 'previousPeriod',
							}).start,
							compareEndTime: getComparePeriodRange({
								startTime: toOrgMoment()
									.subtract(29, 'days')
									.toDate(),
								endTime: toOrgMoment().toDate(),
								compareType: 'previousPeriod',
							}).end,
							dateType: 'created-at',
							timeRange: 'last 30 days',
							compareTimeRange: 'previousPeriod',
							trackingPageIds: null,
							carriers: null,
							ratings: null,
							keywords: null,
							benchmarkIndustry: 'allIndustries',
							benchmarkTrackingPageIds: ['all'],
							benchmarkTimeRange: {
								benchmarkTrackingStartMonth: toOrgMoment()
									.subtract(3, 'month')
									.startOf('M')
									.format('YYYY-MM-DD'),
								benchmarkTrackingEndMonth: toOrgMoment()
									.subtract(1, 'month')
									.startOf('M')
									.format('YYYY-MM-DD'),
								benchmarkTimeRangeType: 'last3Months',
							},
					  }
					: {
							...originFilter,
							startTime: start || originFilter?.startTime,
							endTime: end || originFilter?.endTime,
							advancedFilters: {
								filterV2: filtersToFeAdvancedFilter(
									originFilter?.advancedFilters as any
								),
							},
					  },
		};
	}, [
		subscription,
		analyticsDashboardKey,
		analyticsDashboardName,
		timezone,
		emailType,
	]);

	const {data: trackingPageOptions = []} = useTrackingPagesQuery(undefined, {
		enabled: filterPermissionKeyMap.trackingPagePicker.includes(
			analyticsDashboardKey || initialValues.type
		),
		select: res => {
			const pages = res.trackingPages.trackingPages;

			return [
				{
					label: 'All tracking pages',
					value: 'all',
				},
				...pages.map(trackingPage => ({
					label: trackingPage.pageName || '',
					value: trackingPage.id,
				})),
			];
		},
	});

	const handleScheduleOrInstantSend = (values: FormValueProps) => {
		const adminPageQuery = stringify({
			'start-date': values.originFilter?.startDate,
			'end-date': values.originFilter?.endDate,
			'start-time': toOrgMoment(
				values.originFilter?.startDate || ''
			).format(),
			'end-time': toOrgMoment(values.originFilter?.endDate || '')
				.endOf('day')
				.format(),
			'date-type': values.originFilter?.dateType,
		});
		const request = convertValues(values, accountId);
		if (emailType === 'edit') {
			updateAnalyticsSubscription({
				id: subscription?.id || '',
				input: {
					...request,
					active: subscription?.active,
					adminPageQuery: adminPageQuery,
				},
			});
			return;
		}
		if (values.isRecurring[0] === 'true') {
			createAnalyticsSubscription({
				input: {
					...request,
					active: true,
					type:
						analyticsDashboardKey ||
						Shipment_Analytics_Subscription_Types.Shipments,
					adminPageQuery: adminPageQuery,
				},
			});
		} else {
			onSendEmail(values, false);
		}
	};

	const handleSubmit = (values: FormValueProps) => {
		if (
			checkIsInternalOrExternalRecipientEnabled(
				values.emailRecipientGroup,
				false
			)
		) {
			setExternalRecipientCheckModalVisible(true);
		} else {
			handleScheduleOrInstantSend(values);
		}
	};

	const handleSendTestEmail = (values: FormValueProps) => {
		const formik = formikRef.current;
		formik?.validateForm(values)?.then(errors => {
			// Remove the fields that are not required for test email
			const requiredErrors = omit(errors, [
				'name',
				'emailRecipientType',
				'emailRecipients',
				'emailRecipientGroup',
				'emailExternalRecipients',
				'schedule',
			]);
			const requiredErrorsKeys = Object.keys(requiredErrors);

			if (requiredErrorsKeys.length === 0) {
				onSendEmail(values, true);
			} else {
				formik?.setErrors(requiredErrors);
				const touched = requiredErrorsKeys.reduce(
					(acc: Record<string, boolean>, key) => {
						acc[key] = true;
						return acc;
					},
					{}
				);
				formik?.setTouched(touched, true);
			}
		});
	};

	useEffect(() => {
		if (emailType === 'default') {
			document.body.style.overflow = 'hidden';
		}
		return () => {
			document.body.style.overflow = 'scroll';
		};
	}, [emailType]);

	if ((emailType === 'edit' && isLoading) || !timezone) {
		return (
			<Sheet
				open={open}
				onClose={onClose}
				accessibilityLabel="shipmentDetailsSheet"
			>
				<div
					style={{
						height: '100%',
						display: 'flex',
						justifyContent: 'center',
						alignItems: 'center',
					}}
				>
					<Spinner />
				</div>
			</Sheet>
		);
	}

	return (
		<Formik
			initialValues={initialValues}
			enableReinitialize
			innerRef={formikRef}
			onSubmit={handleSubmit}
			validationSchema={yup.object().shape({
				isRecurring: yup.array().required(),
				name: yup.string().when('isRecurring', {
					is: value => value[0] === 'true',
					then: yup.string().required(t('THIS_NAME_k231ss7')),
				}),
				emailSubject: yup.string().required(t('THIS_SUBJECT_h2349lz')),
				emailRecipientGroup: yup
					.mixed<Email_Recipient_Group>()
					.required(t('THIS_NAME_k231ss7')),
				emailRecipients: yup
					.array()
					.when(['emailRecipientGroup', 'emailRecipientType'], {
						is: (
							emailRecipientGroup: Email_Recipient_Group,
							emailRecipientType: Email_Recipient_Type[]
						) => {
							const isInternalRecipientEnabled =
								checkIsInternalOrExternalRecipientEnabled(
									emailRecipientGroup
								);
							return (
								isInternalRecipientEnabled &&
								emailRecipientType[0] ===
									Email_Recipient_Type.SpecificMembers
							);
						},
						then: yup.array().min(1, t('SELECT_MEMBER_asl234')),
					}),
				emailExternalRecipients: yup
					.array()
					.when('emailRecipientGroup', {
						is: (value: Email_Recipient_Group) =>
							checkIsInternalOrExternalRecipientEnabled(
								value,
								false
							),
						then: yup
							.array()
							.min(
								1,
								t('ENTER_AN_E_60e13', 'Enter an email address')
							),
					}),
				schedule: yup.object().when('isRecurring', {
					is: value => value[0] === 'true',
					then: yup.object().shape({
						weeklyRule: yup.array().when('type', {
							is: Schedule_Type.Weekly,
							then: yup.array().min(1, t('SELECT_A_DAY_sp2341q')),
						}),
						dayInMonthRule: yup.array().when('type', {
							is: Schedule_Type.DayInMonth,
							then: yup.array().min(1, t('SELECT_A_DAY_sp2341q')),
						}),
					}),
				}),
			})}
		>
			{({values, setFieldValue, dirty}) => {
				return (
					<>
						<Sheet
							open={open}
							onClose={() => handleClose(dirty)}
							accessibilityLabel="shipmentAnalyticsDetailsSheet"
						>
							<WarnAboutUnsavedChanges dirty />
							<div
								ref={divRef}
								style={{
									alignItems: 'center',
									borderBottom: '1px solid #E1E3E5',
									display: 'flex',
									justifyContent: 'space-between',
									padding: '16px 20px',
									fontSize: '16px',
								}}
							>
								<TextStyle variation="strong">
									{title}
								</TextStyle>
								<Button
									accessibilityLabel="Cancel"
									icon={MobileCancelMajor}
									onClick={() => handleClose(dirty)}
									plain
								/>
							</div>
							<div className={style.scrollable}>
								<Scrollable
									style={{
										padding: '16px 20px',
										height: '100%',
									}}
								>
									<EmailContent
										type={emailType}
										subscriptionsLength={
											subscriptionsLength || 0
										}
										analyticsDashboardKey={
											analyticsDashboardKey
										}
									/>
									<div className={style.divider} />
									<AnalyticsRecipientSection
										externalAccounts={
											subscription?.externalAccounts
										}
									/>
									<div className={style.divider} />
									{values.isRecurring[0] === 'true' && (
										<>
											<DeliverySchedule />
											<div className={style.divider} />
										</>
									)}

									{/* filter */}
									<div
										style={{
											fontWeight: 600,
											fontSize: '16px',
											marginBottom: '10px',
										}}
									>
										{t('FILTERS_9cd7c', 'Filters')}
									</div>
									{filterPermissionKeyMap.datePicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div style={{marginBottom: '8px'}}>
											<DateFilter
												analyticsDashboardKey={
													analyticsDashboardKey
												}
											/>
										</div>
									)}
									{filterPermissionKeyMap.compareDatePicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div style={{marginBottom: '8px'}}>
											<DateFilter isCompare />
										</div>
									)}
									{filterPermissionKeyMap.locationPicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div style={{marginBottom: '8px'}}>
											<LocationFilter
												textAlign="left"
												hideValue
												fullWidth
												onChange={data => {
													setFieldValue(
														'originFilter',
														{
															...values.originFilter,

															origins: data
																.origins?.length
																? decodeCountries(
																		JSON.stringify(
																			data.origins
																		)
																  )
																: undefined,

															destinations: data
																.destinations
																?.length
																? decodeCountries(
																		JSON.stringify(
																			data.destinations
																		)
																  )
																: undefined,
															originsV2: data
																.origins.length
																? data.origins
																: undefined,

															destinationsV2: data
																.destinations
																.length
																? data.destinations
																: undefined,
														}
													);
												}}
												filter={{
													startTime: toOrgMoment(
														minDate
													)
														.add('1', 'day')
														.startOf('day')
														.format(),
													endTime: toOrgMoment()
														.endOf('day')
														.format(),
													// ...locationsFilter,
													origins:
														values.originFilter
															?.origins,
													destinations:
														values.originFilter
															?.destinations,
												}}
											/>
										</div>
									)}

									{filterPermissionKeyMap.carrierPicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div style={{marginBottom: '8px'}}>
											<CarrierFilter
												textAlign="left"
												hideValue
												fullWidth
												onChange={data => {
													setFieldValue(
														'originFilter',
														{
															...values.originFilter,

															originSlugs: data
																.originSlugs
																?.length
																? decodeSlugs(
																		JSON.stringify(
																			data.originSlugs
																		)
																  )
																: undefined,

															destinationSlugs:
																data
																	.destinationSlugs
																	?.length
																	? decodeSlugs(
																			JSON.stringify(
																				data.destinationSlugs
																			)
																	  )
																	: undefined,
															originSlugsV2: data
																.originSlugs
																.length
																? data.originSlugs
																: undefined,

															destinationSlugsV2:
																data
																	.destinationSlugs
																	.length
																	? data.destinationSlugs
																	: undefined,
														}
													);

													// 用V2字段储存原始值以初始化
												}}
												filter={{
													startTime: toOrgMoment(
														minDate
													)
														.add('1', 'day')
														.startOf('day')
														.format(),
													endTime: toOrgMoment()
														.endOf('day')
														.format(),
													// ...carriersFilter,
													originSlugs:
														values.originFilter
															?.originSlugs,
													destinationSlugs:
														values.originFilter
															?.destinationSlugs,
												}}
											/>
										</div>
									)}

									{filterPermissionKeyMap.customFieldPicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<Button
											textAlign="left"
											fullWidth
											onClick={() => {
												setIsOpenCustomFieldConfig(
													true
												);
											}}
										>
											{
												(
													<div
														style={{
															display: 'flex',
															justifyContent:
																'space-between',
															alignItems:
																'center',
															height: '20px',
															width: '308px',
														}}
													>
														<div
															style={{
																display: 'flex',
																lineHeight:
																	'20px',
															}}
														>
															<div
																style={{
																	marginRight:
																		'4px',
																}}
															>
																<Icon
																	source={
																		CodeMajor
																	}
																/>
															</div>
															<TextStyle>
																{`${t(
																	'CUSTOMFIEL_2c82e',
																	'Custom fields'
																)} ${
																	(
																		values
																			?.originFilter
																			.advancedFilters as any
																	)?.filterV2
																		?.customFilter
																		?.length
																		? `(${
																				(
																					values
																						?.originFilter
																						.advancedFilters as any
																				)?.filterV2?.customFilter?.filter(
																					(
																						item: any
																					) => {
																						return item
																							?.values
																							?.length;
																					}
																				)
																					?.length ||
																				0
																		  })`
																		: ''
																}`}
															</TextStyle>
														</div>
														<div>
															<Icon
																source={
																	ChevronRightMinor
																}
															/>
														</div>
													</div>
												) as any
											}
										</Button>
									)}

									{filterPermissionKeyMap.moreFiltersPicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div style={{marginTop: '8px'}}>
											<Button
												textAlign="left"
												fullWidth
												onClick={() => {
													setIsOpenMoreFiltersSheet(
														true
													);
												}}
											>
												{
													(
														<div
															style={{
																display: 'flex',
																justifyContent:
																	'space-between',
																alignItems:
																	'center',
																height: '20px',
																width: '308px',
															}}
														>
															<div
																style={{
																	display:
																		'flex',
																	lineHeight:
																		'20px',
																}}
															>
																<div
																	style={{
																		marginRight:
																			'4px',
																	}}
																>
																	<Icon
																		source={
																			FilterMajor
																		}
																	/>
																</div>
																<TextStyle>
																	{t(
																		'MOREFILTER_17fa7',
																		'More filters'
																	)}
																</TextStyle>
															</div>
															<div>
																<Icon
																	source={
																		ChevronRightMinor
																	}
																/>
															</div>
														</div>
													) as any
												}
											</Button>
										</div>
									)}

									{filterPermissionKeyMap.shipmentReviewsPicker.includes(
										analyticsDashboardKey || values.type
									) && <ReviewsFilters />}

									{filterPermissionKeyMap.benchmarkTimePicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div style={{marginBottom: '8px'}}>
											<BenchmarkTimeRangeFilter
												rendererValues={values}
												setBenchmarkTimeRange={
													setFieldValue
												}
											/>
										</div>
									)}
									{filterPermissionKeyMap.benchmarkIndustryPicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div style={{marginBottom: '8px'}}>
											<BenchmarkCompareTypeFilter
												rendererValues={values}
												setBenchmarkIndustry={
													setFieldValue
												}
											/>
										</div>
									)}
									{filterPermissionKeyMap.trackingPagePicker.includes(
										analyticsDashboardKey || values.type
									) && (
										<div>
											<PolarisSelect
												onChange={id => {
													setFieldValue(
														analyticsDashboardKey ===
															Shipment_Analytics_Subscription_Types.Benchmarks ||
															analyticsDashboardKey ===
																Shipment_Analytics_Subscription_Types.TrackingPageClickThroughRateTable
															? 'originFilter.benchmarkTrackingPageIds'
															: 'originFilter.trackingPageIds',
														[id]
													);
												}}
												label=""
												labelHidden
												value={
													analyticsDashboardKey ===
														Shipment_Analytics_Subscription_Types.Benchmarks ||
													analyticsDashboardKey ===
														Shipment_Analytics_Subscription_Types.TrackingPageClickThroughRateTable
														? values?.originFilter
																?.benchmarkTrackingPageIds?.[0]
														: values?.originFilter
																?.trackingPageIds?.[0]
												}
												options={trackingPageOptions}
											/>
										</div>
									)}

									<div className={style.divider} />

									{/* ----------------- */}

									<Stack vertical spacing="tight">
										<div
											style={{
												fontWeight: 600,
												fontSize: '16px',
											}}
										>
											{t('ADDITIONAL_f567b')}
										</div>
										<Select
											label={t(
												subDashboardKey.includes(
													analyticsDashboardKey
												)
													? 'ATTACHREPO_4dda1'
													: 'ATTACHDASH_0b15a'
											)}
											options={[
												{
													label: t(
														'NO_ATTACH_SFle2a'
													),
													value: '',
												},
												...(analyticsDashboardKey ===
												Shipment_Analytics_Subscription_Types.ShipmentReviews
													? [
															{
																label: 'CSV',
																value: ExportType.Csv,
																disabled:
																	isDisableExport,
															},
															{
																label: 'PDF',
																value: ExportType.Pdf,
																disabled:
																	isDisableExport,
															},
													  ]
													: [
															subDashboardKey.includes(
																analyticsDashboardKey
															)
																? {
																		label: 'CSV',
																		value: ExportType.Csv,
																		disabled:
																			isDisableExport,
																  }
																: {
																		label: 'PDF',
																		value: ExportType.Pdf,
																		disabled:
																			isDisableExport,
																  },
													  ]),
											]}
											value={values.exportFileType}
											onChange={value => {
												setFieldValue(
													'exportFileType',
													value
												);
											}}
											fixed={false}
										/>
										{values.isRecurring[0] === 'true' && (
											<Checkbox
												label={t('SENDEMAILE_9d746')}
												name="sendEmptyResult"
											/>
										)}
									</Stack>
								</Scrollable>
							</div>
							<ActionGroup
								type={emailType}
								email={user?.email || ''}
								loading={
									createLoading ||
									sendEmailLoading ||
									updateLoading
								}
								onDiscard={handleClose}
								onTestEmail={() => {
									handleSendTestEmail(values);
								}}
								testEmailLoading={sendEmailLoading}
								subscriptionsLength={subscriptionsLength || 0}
							/>

							{discardModalVisible && (
								<DiscardChangeModal
									open={discardModalVisible}
									onClose={() => setDiscardVisible(false)}
									onDiscard={onClose}
								/>
							)}
							{retryModalVisible && (
								<UnableSendEmailModal
									open={retryModalVisible}
									onClose={() => setRetryModalVisible(false)}
									onRetry={() => {
										onSendEmail(values, false);
									}}
								/>
							)}
						</Sheet>
						<CustomFieldsSheet
							isSubscription
							isShipment
							query={values.originFilter.advancedFilters}
							onChangeQuery={(val: any) => {
								setFieldValue(
									'originFilter.advancedFilters',
									val
								);
							}}
							title={t('CUSTOMFIEL_2c82e', 'Custom fields')}
							open={isOpenCustomFieldConfig}
							onClose={() => {
								setIsOpenCustomFieldConfig(false);
							}}
						/>
						<MoreFiltersSheet
							isSubscription
							open={isOpenMoreFiltersSheet}
							title={t('MOREFILTER_17fa7', 'More filters')}
							onClose={() => {
								setIsOpenMoreFiltersSheet(false);
							}}
							query={values.originFilter.advancedFilters}
							onChangeQuery={(val: any) => {
								setFieldValue(
									'originFilter.advancedFilters',
									val
								);
							}}
							filter={undefined}
						/>
						<ExternalRecipientCheckModal
							isRecurring={values.isRecurring[0] === 'true'}
							isOpen={externalRecipientCheckModalVisible}
							onClose={() =>
								setExternalRecipientCheckModalVisible(false)
							}
							onSubmit={() => {
								handleScheduleOrInstantSend(values);
								setExternalRecipientCheckModalVisible(false);
							}}
						/>
					</>
				);
			}}
		</Formik>
	);
};

export default AnalyticsSubscription;
