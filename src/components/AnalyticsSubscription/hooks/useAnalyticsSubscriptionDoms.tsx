import {<PERSON>List, Popover, Stack} from '@shopify/polaris';
import {InviteMinor, LockMinor} from '@shopify/polaris-icons';
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useHistory} from 'react-router';

import {
	useGetShipmentAnalyticsSubscriptionsQuery,
	Shipment_Analytics_Subscription_Types,
} from '@graphql/generated';
import {PAGE_BANNER} from 'constants/BannerNames';
import {RBAC_ACTION, RBAC_RESOURCE} from 'constants/RbacAction';
import {ANALYTICS_SCHEDULE_EMAILS} from 'constants/billings/features';
import {useFeatureValidator} from 'hooks/billings';
import {useHandleError} from 'hooks/useHandleError';
import {useCan, useCanEdit} from 'hooks/useRBAC';
import isCompany from 'utils/isCompany';

import AnalyticsSubscription from '../AnalyticsSubscription';
import {dashboardNameKeyMap, subDashboardKey} from '../constants';

export const useAnalyticsSubscriptionDoms = ({
	analyticsDashboardKey,
}: {
	analyticsDashboardKey: Shipment_Analytics_Subscription_Types;
}) => {
	const history = useHistory();
	const {t} = useTranslation();
	const {addErrorBanner} = useHandleError();
	const {can: canEdit} = useCanEdit(RBAC_RESOURCE.ANALYTICS_SUBSCRIPTION);
	const {can: canView} = useCan(
		RBAC_ACTION.VIEW,
		RBAC_RESOURCE.ANALYTICS_SUBSCRIPTION
	);

	const [openSubscriptionPopover, setOpenSubscriptionPopover] =
		useState(false);
	const {data: analyticsSubscriptionLength = []} =
		useGetShipmentAnalyticsSubscriptionsQuery(
			{
				type: analyticsDashboardKey,
			},
			{
				enabled: !isCompany(),
				select: data => data.shipmentAnalyticsSubscriptions?.length,
			}
		);
	const [openAnalyticsSubscription, setOpenAnalyticsSubscription] =
		useState(false);
	const [onUpgrade, hasAnalyticsFeature] = useFeatureValidator(
		ANALYTICS_SCHEDULE_EMAILS,
		() => {
			setOpenSubscriptionPopover(true);
		}
	);

	const analyticsActionConfig = isCompany()
		? {}
		: {
				// Change has subscribe permission
				icon: hasAnalyticsFeature ? InviteMinor : LockMinor,
				content: (
					<Popover
						active={openSubscriptionPopover}
						activator={
							<div id="csv-import-btn">
								{t(
									subDashboardKey.includes(
										analyticsDashboardKey
									)
										? 'EMAILREPOR_e4aff'
										: 'EMAILDASHB_5ecaf',
									'Email dashboard'
								)}
							</div>
						}
						onClose={() => {
							setOpenSubscriptionPopover(false);
						}}
						preferredAlignment="center"
					>
						<ActionList
							items={[
								{
									content: (
										<Stack>
											<div>
												{t(
													subDashboardKey.includes(
														analyticsDashboardKey
													)
														? 'EMAILTHISR_d0cf0'
														: 'EMAILTHISD_c1380',
													'Email this dashboard'
												)}
											</div>
										</Stack>
									) as unknown as string,
									onAction: () => {
										if (!canEdit) {
											addErrorBanner(PAGE_BANNER, {
												code: 40399,
												message: t('YOUDONTHAV_f913e', {
													resource: `"${RBAC_RESOURCE.ANALYTICS_SUBSCRIPTION}"`,
													interpolation: {
														escapeValue: false,
													},
												}),
											});
										} else {
											setOpenAnalyticsSubscription(true);
										}
									},
								},
								{
									content: `${t(
										'MANAGERECU_616e3',
										'Manage recurring emails'
									)} (${analyticsSubscriptionLength})`,
									onAction: () => {
										if (!canView) {
											addErrorBanner(PAGE_BANNER, {
												code: 40399,
												message: t('YOUDONTHAV_f913e', {
													resource: `"${RBAC_RESOURCE.ANALYTICS_SUBSCRIPTION}"`,
													interpolation: {
														escapeValue: false,
													},
												}),
											});
										} else {
											history.push(
												`/dashboard/subscriptions/${analyticsDashboardKey}`
											);
										}
									},
								},
							].slice(0, analyticsSubscriptionLength ? 2 : 1)}
						/>
					</Popover>
				) as unknown as string,
				onAction: onUpgrade,
		  };

	const analyticsSubscriptionDom = openAnalyticsSubscription ? (
		<AnalyticsSubscription
			open={openAnalyticsSubscription}
			onClose={() => {
				setOpenAnalyticsSubscription(false);
			}}
			type="default"
			analyticsDashboardKey={analyticsDashboardKey}
			analyticsDashboardName={dashboardNameKeyMap[analyticsDashboardKey]}
		/>
	) : (
		<></>
	);

	return [analyticsActionConfig, analyticsSubscriptionDom];
};
