import {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';

import {
	useCreateShipmentAnalyticsSubscriptionMutation,
	useGetShipmentAnalyticsSubscriptionsQuery,
	useInstantSendAnalyticsEmailMutation,
	useUpdateShipmentAnalyticsSubscriptionMutation,
} from '@graphql/generated';
import {addToast} from 'actions/toast';
import {useHandleError} from 'hooks/useHandleError';
import isCompany from 'utils/isCompany';

export const useAnalyticsSubscriptionAction = ({
	onSuccess,
	analyticsDashboardKey,
}: {
	onSuccess?: () => void;
	analyticsDashboardKey: string;
}) => {
	const {t} = useTranslation();
	const dispatch = useDispatch();
	const {addErrorToast} = useHandleError();
	const {mutate: createAnalyticsSubscription, isLoading: createLoading} =
		useCreateShipmentAnalyticsSubscriptionMutation({
			onSuccess: () => {
				dispatch(addToast({message: t('EMAIL_SCHEDULE_n21d3l1')}));
				onSuccess?.();
			},
			onError: error => {
				let {message} = error;
				switch (error.code) {
					case 40903:
						message = t(
							'ANEMAILFOR_30e7f',
							'An email for this dashboard and frequency has already been scheduled'
						);
						break;
					case 42216:
						message = t(
							'AMAXOF_10_EM_bb9cb',
							'A max. of 10 emails can be scheduled for each dashboard'
						);
						break;
					default:
				}
				addErrorToast({...error, message});
			},
		});

	const {mutate: updateAnalyticsSubscription, isLoading: updateLoading} =
		useUpdateShipmentAnalyticsSubscriptionMutation({
			onSuccess: () => {
				dispatch(addToast({message: t('RECURRING_EMAIL_g231ql1')}));
				onSuccess?.();
			},
			onError: error => {
				if (error.code === 40902) {
					dispatch(
						addToast({
							message: t('AN_EMAIL_FOR_d1l3ca'),
							error: true,
						})
					);
				} else if (error.code === 40903) {
					dispatch(
						addToast({
							message: t('ANEMAILFOR_30e7f'),
							error: true,
						})
					);
				} else {
					addErrorToast(error);
				}
			},
		});

	const {data: subscriptions = []} =
		useGetShipmentAnalyticsSubscriptionsQuery(
			{
				type: analyticsDashboardKey,
			},
			{
				enabled: !isCompany(),
				select: data => data.shipmentAnalyticsSubscriptions,
			}
		);
	return {
		createAnalyticsSubscription,
		updateAnalyticsSubscription,
		createLoading,
		updateLoading,
		subscriptionsLength: subscriptions?.length,
	};
};

export const useSendAnalyticsEmail = () => {
	const [retryModalVisible, setRetryModalVisible] = useState(false);
	const {addErrorToast} = useHandleError();

	const {mutate: sendEmail, isLoading: sendEmailLoading} =
		useInstantSendAnalyticsEmailMutation({
			onError: error => {
				if (error.code === 40901) {
					setRetryModalVisible(true);
					return;
				}
				addErrorToast(error);
			},
		});

	return {
		sendEmail,
		sendEmailLoading,
		retryModalVisible,
		setRetryModalVisible,
	};
};
