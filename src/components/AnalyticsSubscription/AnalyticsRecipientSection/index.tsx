import {Stack, Icon} from '@shopify/polaris';
import {InfoMinor} from '@shopify/polaris-icons';
import {useFormikContext} from 'formik';
import React from 'react';
import {useTranslation} from 'react-i18next';

import {Account, Email_Recipient_Group, Maybe} from '@graphql/generated';

import ExternalRecipient from './components/ExternalRecipient/ExternalRecipient';
import InternalRecipient from './components/InternalRecipient/InternalRecipient';
import styles from './index.module.scss';

export default function AnalyticsRecipientSection({
	externalAccounts,
}: {
	externalAccounts?: Maybe<Account[]>;
}) {
	const {t} = useTranslation();
	const {errors, touched} = useFormikContext<{
		emailRecipientGroup: Email_Recipient_Group;
	}>();

	return (
		<Stack vertical spacing="tight">
			<div className={styles.subTitleWrapper}>
				{t('RECIPIENTS_b8c59')}
			</div>
			<InternalRecipient externalAccounts={externalAccounts} />
			<ExternalRecipient />
			{touched.emailRecipientGroup && errors.emailRecipientGroup && (
				<div className={styles.errorWrapper}>
					<span className={styles.errorIcon}>
						<Icon source={InfoMinor} color="critical" />
					</span>
					{errors.emailRecipientGroup}
				</div>
			)}
		</Stack>
	);
}
