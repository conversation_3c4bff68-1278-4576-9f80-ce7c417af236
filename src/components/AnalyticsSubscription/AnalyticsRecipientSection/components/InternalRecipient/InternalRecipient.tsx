import {ChoiceList, Checkbox} from '@shopify/polaris';
import {useFormikContext} from 'formik';
import React from 'react';
import {useTranslation} from 'react-i18next';

import {
	Email_Recipient_Group,
	Account,
	Email_Recipient_Type,
	Maybe,
} from '@graphql/generated';

import styles from '../../index.module.scss';
import {checkIsInternalOrExternalRecipientEnabled} from '../../utils';

import SpecificInternalRecipient from './SpecificInternalRecipient';

export default function InternalRecipient({
	externalAccounts,
}: {
	externalAccounts?: Maybe<Account[]>;
}) {
	const {t} = useTranslation();
	const {values, setFieldValue, setFieldTouched} = useFormikContext<{
		emailRecipientType: string[];
		emailRecipients: string[];
		emailRecipientGroup: Email_Recipient_Group;
	}>();
	const {emailRecipientGroup} = values;
	const isInternalRecipientEnabled =
		checkIsInternalOrExternalRecipientEnabled(emailRecipientGroup);
	const isExternalRecipientEnabled =
		checkIsInternalOrExternalRecipientEnabled(emailRecipientGroup, false);

	return (
		<>
			<Checkbox
				label={t('INTERNAL_M_10dec', 'Internal members')}
				name="emailRecipientGroup"
				checked={isInternalRecipientEnabled}
				onChange={value => {
					if (value) {
						setFieldValue(
							'emailRecipientGroup',
							isExternalRecipientEnabled
								? Email_Recipient_Group.All
								: Email_Recipient_Group.Internal
						);
					} else {
						setFieldValue(
							'emailRecipientGroup',
							isExternalRecipientEnabled
								? Email_Recipient_Group.External
								: undefined
						);
					}
					setTimeout(() => {
						setFieldTouched('emailRecipientGroup', true);
					}, 100);
				}}
			/>
			{isInternalRecipientEnabled && (
				<div className={styles.subSectionWrapper}>
					<ChoiceList
						titleHidden
						title=""
						name="emailRecipientType"
						selected={values.emailRecipientType}
						choices={[
							{
								label: t('ALLMEMBERS_ec03a'),
								value: Email_Recipient_Type.AllMembers,
							},
							{
								label: t('SPECIFICME_4da5f'),
								value: Email_Recipient_Type.SpecificMembers,
							},
						]}
						onChange={value => {
							setFieldValue('emailRecipientType', value);
						}}
					/>
					{values.emailRecipientType[0] ===
						Email_Recipient_Type.SpecificMembers && (
						<SpecificInternalRecipient
							externalAccounts={externalAccounts}
						/>
					)}
				</div>
			)}
		</>
	);
}
