import {useAuth} from '@aftership/automizely-product-auth';
import {
	Popover,
	TextField,
	Icon,
	OptionList,
	Stack,
	Tooltip,
	Tag,
} from '@shopify/polaris';
import {AlertMinor, CirclePlusMinor, SearchMinor} from '@shopify/polaris-icons';
import {useFormikContext} from 'formik';
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';

import {
	Account,
	Maybe,
	useMembershipsQuery,
	Email_Recipient_Group,
} from '@graphql/generated';
import {classnames} from 'utils/classnames';

import styles from '../../index.module.scss';

interface Props {
	externalAccounts?: Maybe<Account[]>;
}

export default function SpecificInternalRecipient({externalAccounts}: Props) {
	const {t} = useTranslation();
	const [{user, organization}] = useAuth();

	const [memberPopoverActive, setMemberPopoverActive] = useState(false);
	const [memberInput, setMemberInput] = useState('');

	const {values, setFieldValue, errors, touched, setFieldTouched} =
		useFormikContext<{
			emailRecipientType: string[];
			emailRecipients: string[];
			emailRecipientGroup: Email_Recipient_Group;
		}>();

	const {data: memberships = []} = useMembershipsQuery(undefined, {
		select: data => {
			return data.memberships
				.map(membership => ({
					accountId: membership.account.id,
					name:
						membership.account.firstName +
						' ' +
						membership.account.lastName,
					email: membership.account.email,
				}))
				.sort((a, b) => a.name.localeCompare(b.name));
		},
	});

	const memberList = memberships
		.filter(item => {
			const query = memberInput.toUpperCase();
			return (
				item.name.toUpperCase().includes(query) ||
				item.email.toUpperCase().includes(query)
			);
		})
		.map(item => ({
			label: (
				<Stack vertical spacing="extraTight">
					<div>
						{item.name || t('UNKNOWN_Sfkq3e1')}
						{user?.email === item.email && ' (me)'}
					</div>
					<div
						style={{
							color: '#6D7175',
						}}
					>
						{item.email}
					</div>
				</Stack>
			),
			value: item.accountId,
		}));
	return (
		<>
			<div
				style={{
					marginTop: 8,
				}}
			>
				<Popover
					autofocusTarget="none"
					active={memberPopoverActive}
					activator={
						<TextField
							autoComplete={false}
							labelHidden
							label=""
							value={memberInput}
							onChange={value => setMemberInput(value)}
							placeholder={t('SEARCHBYNA_42c93')}
							onFocus={() => setMemberPopoverActive(true)}
							prefix={<Icon source={SearchMinor} />}
							error={
								touched.emailRecipients &&
								errors.emailRecipients
							}
							onBlur={() =>
								setTimeout(
									() =>
										setFieldTouched(
											'emailRecipients',
											true
										),
									500
								)
							}
						/>
					}
					preferredAlignment="right"
					onClose={() => setMemberPopoverActive(false)}
				>
					<div style={{width: '290px'}}>
						<div
							role="button"
							tabIndex={-1}
							style={{padding: '10px 8px 0px'}}
						>
							<button
								type="button"
								className="Polaris-ActionList__Item"
								onClick={e => {
									e.preventDefault();
									window.open(
										`${process.env.ACCOUNTS_AFTERSHIP}/members?organization_id=${organization?.id}`,
										'_blank'
									);
								}}
							>
								<Stack spacing="tight">
									<Icon
										source={CirclePlusMinor}
										color="base"
									/>
									<div
										style={{
											fontWeight: 500,
										}}
									>
										{t('ADDMEMBERS_bd1ba')}
									</div>
								</Stack>
							</button>
						</div>
						{memberList.length ? (
							<OptionList
								options={memberList}
								selected={values.emailRecipients}
								onChange={(selected: string[]) =>
									setFieldValue('emailRecipients', selected)
								}
								allowMultiple
							/>
						) : (
							<div
								style={{
									color: '#8C9196',
									padding: 16,
								}}
							>
								{t('NOMATCHEDM_1200c')}
							</div>
						)}
					</div>
				</Popover>
				<div style={{marginTop: 8}}>
					<Stack spacing="tight">
						{values.emailRecipients.map(accountId => {
							const member = memberships.find(
								member => member.accountId === accountId
							);
							const externalMember = externalAccounts?.find(
								item => item.id === accountId
							);
							const externalMemberName =
								(externalMember?.firstName || '') +
								' ' +
								(externalMember?.lastName || '');
							return (
								<Tooltip
									key={accountId}
									content={
										<div
											style={{
												maxWidth: 250,
											}}
										>
											{member
												? member.email
												: t('THIS_PERSON_g13lm8')}
										</div>
									}
								>
									<div
										className={classnames(
											styles.tagWrapper,
											!member
												? styles.tagError
												: undefined
										)}
									>
										<Tag
											onRemove={() =>
												setFieldValue(
													'emailRecipients',
													values.emailRecipients.filter(
														item =>
															item !== accountId
													)
												)
											}
										>
											{
												(
													<Stack
														wrap={false}
														spacing="extraTight"
													>
														<div
															className={
																styles.tagContent
															}
														>
															{!member && (
																<Icon
																	source={
																		AlertMinor
																	}
																	color="critical"
																/>
															)}
															<span
																className={
																	styles.tagText
																}
															>
																{member?.name ||
																	member?.email ||
																	externalMemberName}
															</span>
														</div>
													</Stack>
												) as unknown as string
											}
										</Tag>
									</div>
								</Tooltip>
							);
						})}
					</Stack>
				</div>
			</div>
		</>
	);
}
