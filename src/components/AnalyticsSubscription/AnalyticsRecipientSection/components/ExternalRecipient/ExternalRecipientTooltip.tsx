import {Icon, Tooltip} from '@shopify/polaris';
import {InfoMinor} from '@shopify/polaris-icons';
import {useTranslation} from 'react-i18next';

export default function ExternalRecipientTooltip() {
	const {t} = useTranslation();
	const HINT = t(
		'BY_USING_T_ca830',
		`By using this Email dashboard feature, you confirm that you are using AfterShip's services to store and transmit the data that you are about to send in manner is compliant with all applicable laws and regulations including data privacy laws, and that such use does not infringe any third party's intellectual property and privacy rights, and that you are in possession of all necessary consents`
	);
	return (
		<Tooltip
			content={
				<div
					style={{
						width: '300px',
					}}
				>
					{HINT}
				</div>
			}
		>
			<Icon color="subdued" source={InfoMinor} />
		</Tooltip>
	);
}
