import {Checkbox, Stack} from '@shopify/polaris';
import {useFormikContext} from 'formik';
import React from 'react';
import {useTranslation} from 'react-i18next';
import validator from 'validator';

import {Email_Recipient_Group} from '@graphql/generated';

import styles from '../../index.module.scss';
import {checkIsInternalOrExternalRecipientEnabled} from '../../utils';
import InputWithSelect from '../InputWithSelect';

import ExternalRecipientTooltip from './ExternalRecipientTooltip';

export default function ExternalRecipient() {
	const {t} = useTranslation();
	const {values, setFieldTouched, setFieldValue, touched, errors} =
		useFormikContext<{
			emailRecipientGroup: Email_Recipient_Group;
			emailExternalRecipients: string[];
		}>();
	const {emailRecipientGroup} = values;
	const isInternalRecipientEnabled =
		checkIsInternalOrExternalRecipientEnabled(emailRecipientGroup);
	const isExternalRecipientEnabled =
		checkIsInternalOrExternalRecipientEnabled(emailRecipientGroup, false);
	return (
		<Stack vertical spacing="tight">
			<Checkbox
				label={
					<div
						style={{
							display: 'flex',
						}}
					>
						{t('EXTERNAL_R_ed2e2', 'External recipients')}
						<span
							style={{
								marginLeft: '4px',
							}}
						>
							<ExternalRecipientTooltip />
						</span>
					</div>
				}
				name="emailRecipientGroup"
				checked={isExternalRecipientEnabled}
				onChange={value => {
					if (value) {
						setFieldValue(
							'emailRecipientGroup',
							isInternalRecipientEnabled
								? Email_Recipient_Group.All
								: Email_Recipient_Group.External
						);
					} else {
						setFieldValue(
							'emailRecipientGroup',
							isInternalRecipientEnabled
								? Email_Recipient_Group.Internal
								: undefined
						);
					}
					setTimeout(() => {
						setFieldTouched('emailRecipientGroup', true);
					}, 100);
				}}
			/>
			{isExternalRecipientEnabled && (
				<div className={styles.subSectionWrapper}>
					<InputWithSelect
						onChange={values => {
							setFieldValue('emailExternalRecipients', values);
						}}
						defaultValue={values.emailExternalRecipients}
						placeholder={t(
							t('ENTER_EMAI_0a7e6', 'Enter email address')
						)}
						max={50}
						error={
							touched.emailExternalRecipients
								? errors.emailExternalRecipients
								: undefined
						}
						exceedMaxMessage={t(
							'YOU_CAN_ON_d6dbd',
							'You can only add a maximum of 50 email addresses'
						)}
						onBlur={() =>
							setTimeout(
								() =>
									setFieldTouched(
										'emailExternalRecipients',
										true
									),
								100
							)
						}
						inputValidator={(value: string) => {
							if (!value) return '';
							const invalidEmails = !validator.isEmail(value);
							return invalidEmails
								? t(
										'ENTER_A_VA_cffa7',
										'Enter a valid email address'
								  )
								: '';
						}}
					/>
				</div>
			)}
		</Stack>
	);
}
