import {ActionList, Popover, Tag, TextField, Tooltip} from '@shopify/polaris';
import {ReturnMinor} from '@shopify/polaris-icons';
import {uniq} from 'lodash';
import {useState, useCallback, useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import styled from 'styled-components';

const PopoverContent = styled.div`
	.Polaris-ActionList__Prefix {
		margin-right: 8px;
	}

	.Polaris-OptionList-Option--select {
		background-color: white;
	}
`;

export interface Props {
	label?: React.ReactNode;
	onChange?: (value: string[]) => void;
	defaultValue?: string[];
	placeholder?: string;
	disabled?: boolean;
	error?: string | string[];
	max?: number;
	onBlur?: () => void;
	inputValidator?: (value: string) => string;
	exceedMaxMessage?: string;
}

export default function InputWithSelect({
	label,
	onChange,
	defaultValue,
	placeholder,
	disabled: _disabled,
	error: _error,
	max,
	inputValidator,
	onBlur,
	exceedMaxMessage,
}: Props) {
	const {t} = useTranslation();

	const [input, setInput] = useState<string>('');
	const [popoverActive, setPopoverActive] = useState(false);
	const [selectedTags, setSelectedTags] = useState<string[]>(
		defaultValue || []
	);
	const [inputValidateError, setInputValidateError] = useState<string>('');

	const meaningfulInput = input.trim().length > 0 ? input : undefined;

	const error = !popoverActive && (inputValidateError || _error);
	const exceededMax = Boolean(max && selectedTags.length >= max);

	const disabled = _disabled || exceededMax;

	const onTextChange = useCallback((value: string) => {
		setInput(value);
		setInputValidateError('');
	}, []);

	const deleteTag = useCallback(
		(tag: string) => {
			const newTags = selectedTags.filter(v => v !== tag);
			setSelectedTags(newTags);

			onChange && onChange(newTags);
		},
		[selectedTags, onChange]
	);

	const appendTag = useCallback(() => {
		setPopoverActive(false);
		const inputValidatorError =
			inputValidator && meaningfulInput
				? inputValidator(meaningfulInput)
				: '';
		if (inputValidatorError) {
			setInputValidateError(inputValidatorError);
			return;
		}

		setInput('');

		if (!meaningfulInput) return;

		const newTags = uniq([...selectedTags, meaningfulInput]);
		setSelectedTags(newTags);

		onChange && onChange(newTags);
	}, [selectedTags, meaningfulInput, onChange, inputValidator]);

	const showPopover = useCallback(() => {
		meaningfulInput && setPopoverActive(true);
	}, [meaningfulInput]);

	const handleBlur = useCallback(() => {
		setPopoverActive(false);
		onBlur?.();
	}, [onBlur]);

	useEffect(() => {
		showPopover();
	}, [showPopover]);

	return (
		<Popover
			active={popoverActive}
			onClose={() => {
				setPopoverActive(f => !f);
			}}
			fullWidth
			autofocusTarget="none"
			activator={
				<div
					role="button"
					tabIndex={0}
					onKeyDown={(event: React.KeyboardEvent<HTMLElement>) => {
						const enter = event.keyCode === 13;
						if (enter) {
							appendTag();
						}
					}}
				>
					{exceededMax ? (
						<Tooltip
							active
							content={
								exceedMaxMessage ?? (
									<div
										style={{
											width: '240px',
										}}
									>
										{t(
											'YOU_CAN_ON_d6dbd',
											'You can only add a maximum of 50 email addresses'
										)}
									</div>
								)
							}
							preferredPosition="above"
						>
							<TextField
								onChange={onTextChange}
								label={label}
								value={input}
								placeholder={placeholder}
								autoComplete="off"
								// @ts-ignore
								error={error}
								disabled={disabled}
								onBlur={handleBlur}
							/>
						</Tooltip>
					) : (
						<TextField
							onChange={onTextChange}
							label={label}
							value={input}
							placeholder={placeholder}
							autoComplete="off"
							// @ts-ignore
							error={error}
							disabled={disabled}
							onBlur={handleBlur}
							onFocus={showPopover}
						/>
					)}

					{/* show array value with Tags */}
					<div
						style={{
							display: 'flex',
							flexWrap: 'wrap',
							marginTop: '10px',
							gap: '5px',
						}}
					>
						{selectedTags?.map(v => (
							<Tag
								key={v}
								onRemove={() => {
									deleteTag(v);
								}}
							>
								{v}
							</Tag>
						))}
					</div>
				</div>
			}
		>
			<PopoverContent>
				{Boolean(meaningfulInput) && !inputValidateError && (
					<ActionList
						items={[
							{
								content: `Enter "${meaningfulInput}"`,
								icon: ReturnMinor,
								onAction: appendTag,
							},
						]}
					/>
				)}
			</PopoverContent>
		</Popover>
	);
}
