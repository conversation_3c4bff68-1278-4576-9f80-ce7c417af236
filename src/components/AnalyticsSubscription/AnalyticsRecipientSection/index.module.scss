.tagWrapper {
	&.tagError {
		:global(.Polaris-Tag) {
			color: #d72c0d;
			background: #fed3d1;
			:global(.Polaris-Icon__Svg) {
				fill: #d72c0d;
			}
		}
	}
}

.tagContent {
	display: flex;
	align-items: center;
	.tagText {
		max-width: 240px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

.RecipientGroupWrapper {
	.Polaris-Stack__Item {
		margin-top: 8px;
	}
}

.subSectionWrapper {
	margin-left: 24px;
}

.errorWrapper {
	color: #d72c0d;
	display: flex;
	align-items: center;
	.errorIcon {
		margin-right: 0.6rem;
	}
}

.subTitleWrapper {
	font-weight: 600;
	font-size: 16px;
}