import {Button, Popover} from '@shopify/polaris';
import {useFormikContext} from 'formik';
import React, {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {Reviews_Sort, useReviewAnalyticsFiltersQuery} from '@graphql/generated';
import TreeSelector from 'components/TreeSelector';
import {TreeChoiceOption} from 'components/TreeSelector/interface';
import IntroduceDialog from 'pages/Dashboard/ShipmentReviewDashboard/components/IntroduceDialog';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {toOrgMoment} from 'utils/day';

export const ReviewsCarrierFilter = () => {
	const {t} = useTranslation(); // Shipment review carrier
	const {minDate} = useDatePickerRangeOptions();

	const {values, setFieldValue} = useFormikContext<any>();
	const [carrierFilterActive, setCarrierFilterActive] = useState(false);
	const [carriers, setCarriers] = useState<TreeChoiceOption[]>([]);

	const {data: reviewsCarrier, isLoading: reviewsLoading} =
		useReviewAnalyticsFiltersQuery(
			{
				input: {
					startDate: toOrgMoment(minDate)
						.add('1', 'day')
						.startOf('day')
						.format('YYYY-MM-DD'),
					endDate: toOrgMoment().endOf('day').format('YYYY-MM-DD'),

					sortBy: 'CREATED_AT_DESC' as Reviews_Sort,
					limit: 100,
					page: 1,
				},
			},
			{
				select: data => data.reviewAnalyticsV2.filters,
				// onError: error => {
				// 	showErrorBanner(PAGE_BANNER, error.message);
				// },
				// keepPreviousData: true,
			}
		);
	useEffect(() => {
		const selectedSlugs = values?.originFilter?.carriers;
		setCarriers(
			reviewsCarrier?.carriers?.map(carrier => ({
				key: carrier.slug,
				label: carrier.slug,
				value: carrier.slug,
				text: '',
				checked: (selectedSlugs || []).includes(carrier.slug),
				hasChildren: false,
			})) || []
		);
	}, [reviewsCarrier, values?.originFilter?.carriers]);
	const isEmpty = useMemo(() => {
		return !carriers.some(carrier => carrier.checked);
	}, [carriers]);
	const onClear = () => {
		setFieldValue('originFilter.carriers', null);
	};

	const handleSelectCarriers = (carriers: TreeChoiceOption[]) => {
		setFieldValue(
			'originFilter.carriers',
			carriers.filter(carrier => carrier.checked).map(({value}) => value)
		);
	};

	return (
		<Popover
			fullWidth
			sectioned
			active={carrierFilterActive}
			onClose={() => setCarrierFilterActive(false)}
			activator={
				<div style={{position: 'relative'}}>
					<Button
						fullWidth
						disclosure
						textAlign="left"
						onClick={() =>
							setCarrierFilterActive(!carrierFilterActive)
						}
					>
						{isEmpty
							? t('CARRIER_7f3fa')
							: t('CARRIER_7f3fa', {
									totalCount: carriers.filter(c => c.checked)
										.length,
							  })}
					</Button>
					<div
						style={{
							position: 'absolute',
							top: -8,
							left: 'calc(100% + 25px)',
						}}
					>
						{!reviewsLoading && <IntroduceDialog />}
					</div>
				</div>
			}
		>
			<TreeSelector
				withSearchField
				treeData={carriers}
				onChangeTreeData={handleSelectCarriers}
				renderChoice={choice => <div>{choice.label}</div>}
			/>
			<Button plain onClick={onClear} disabled={isEmpty}>
				{t('CLEAR_a76e7')}
			</Button>
		</Popover>
	);
};
