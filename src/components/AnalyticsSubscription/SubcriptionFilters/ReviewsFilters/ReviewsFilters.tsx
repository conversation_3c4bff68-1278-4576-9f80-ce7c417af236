import {useFormikContext} from 'formik';
import React from 'react';

import {useReviewAnalyticsFiltersQuery, Reviews_Sort} from '@graphql/generated';
import CarrierFilter from 'pages/Dashboard/ShipmentReviewDashboard/components/ReviewsFilters/CarrierFilter';
import RatingFilter from 'pages/Dashboard/ShipmentReviewDashboard/components/ReviewsFilters/RatingFilter';
import TagFilter from 'pages/Dashboard/ShipmentReviewDashboard/components/ReviewsFilters/TagFilter';
import {TreeFilterSelectedValue} from 'pages/Dashboard/ShipmentReviewDashboard/components/TreeFilter';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {toOrgMoment} from 'utils/day';

export default function ReviewsFilters() {
	const {minDate} = useDatePickerRangeOptions();
	const {values, setFieldValue} = useFormikContext<any>();
	const {
		data: filters,
		isLoading,
		isFetching,
	} = useReviewAnalyticsFiltersQuery(
		{
			input: {
				startDate: toOrgMoment(minDate)
					.add('1', 'day')
					.startOf('day')
					.format('YYYY-MM-DD'),
				endDate: toOrgMoment().endOf('day').format('YYYY-MM-DD'),

				sortBy: Reviews_Sort.CreatedAtDesc,
				limit: 100,
				page: 1,
			},
		},
		{
			select: data => data?.reviewAnalyticsV2?.filters,
		}
	);
	const {
		carriers: carriersFiltersData,
		ratings: ratingsFiltersData,
		keywords: tagsFiltersData,
	} = filters || {};
	const needLoading = isFetching || isLoading;
	const selectedCarriers = values?.originFilter?.carriers || [];
	const selectedRatings = values?.originFilter?.ratings || [];
	const selectedKeywords = values?.originFilter?.keywords || [];

	const handleFilterChange = (
		queryKey: string,
		value: TreeFilterSelectedValue | null
	) => {
		setFieldValue('originFilter.' + queryKey, value);
	};
	return (
		<>
			<div
				style={{
					marginBottom: '8px',
				}}
			>
				<CarrierFilter
					fullWidth
					selectedCarriers={selectedCarriers}
					onChange={handleFilterChange}
					carriersFiltersData={carriersFiltersData}
					isLoading={needLoading}
				/>
			</div>
			<div
				style={{
					marginBottom: '8px',
				}}
			>
				<RatingFilter
					fullWidth
					selectedRatings={selectedRatings}
					onChange={handleFilterChange}
					isLoading={needLoading}
					ratingsFiltersData={ratingsFiltersData}
				/>
			</div>

			<TagFilter
				fullWidth
				selectedKeywords={selectedKeywords}
				onChange={handleFilterChange}
				isLoading={needLoading}
				tagsFiltersData={tagsFiltersData}
			/>
		</>
	);
}
