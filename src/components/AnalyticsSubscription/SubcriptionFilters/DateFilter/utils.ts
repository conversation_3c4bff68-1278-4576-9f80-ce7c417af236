import {upperFirst} from 'lodash';
import moment from 'moment-timezone';

import {DATE_SELECT, DATE_SELECT_CONTAIN_YEAR} from 'constants/Date';
import {
	generateDateRangeOption,
	generateDateRangeOptionByYear,
	generateFullMonthDateRangeOption,
	generateFullQuarterDateRangeOption,
	generateFullYearDateRangeOption,
	getMachedDateRangeOption,
} from 'utils/day';

export const getDateComparisonActivatorText = ({
	compareOption,
	startDate,
	endDate,
}: {
	compareOption?: string;
	startDate?: Date;
	endDate?: Date;
}) => {
	let formattedResult;

	if (compareOption) {
		formattedResult = compareOption;
		return `${formattedResult}`;
	}
	const start = moment(startDate);
	const end = moment(endDate);

	if (start.year() === end.year()) {
		formattedResult = `${start.format(DATE_SELECT)}-${end.format(
			DATE_SELECT
		)}, ${start.format('YYYY')} `;
	} else {
		formattedResult = `${start.format(
			DATE_SELECT_CONTAIN_YEAR
		)}-${end.format(DATE_SELECT_CONTAIN_YEAR)}`;
	}
	return `Compare: ${formattedResult}`;
};

export const getDateActivatorText = ({
	dateType,
	dateOption,
	startDate,
	endDate,
	isHideDateType,
}: {
	isOption: boolean;
	dateType: string;
	dateOption?: string;
	startDate?: Date;
	endDate?: Date;
	isHideDateType: boolean;
}) => {
	if (dateOption) {
		if (isHideDateType) {
			return upperFirst(dateOption);
		}
		return `${dateType}: ${upperFirst(dateOption)}`;
	}
	let formattedResult;
	const start = moment(startDate);
	const end = moment(endDate);
	if (start.year() === end.year()) {
		formattedResult = `${start.format(DATE_SELECT)} - ${end.format(
			DATE_SELECT
		)}, ${start.format('YYYY')} `;
	} else {
		formattedResult = `${start.format(
			DATE_SELECT_CONTAIN_YEAR
		)} - ${end.format(DATE_SELECT_CONTAIN_YEAR)}`;
	}

	if (isHideDateType) {
		return formattedResult;
	}
	return `${dateType}: ${formattedResult}`;
};

export enum TIME_RANGE {
	TODAY = 'today',
	YESTERDAY = 'yesterday',
	LAST_7_DAYS = 'last 7 days',
	LAST_30_DAYS = 'last 30 days',
	LAST_60_DAYS = 'last 60 days',
	LAST_90_DAYS = 'last 90 days',
	LAST_120_DAYS = 'last 120 days',
	LAST_MONTH = 'last month',
	LAST_QUARTER = 'last quarter',
	LAST_YEAR = 'last year',
	LAST_1_YEAR = 'last 1 year',
	LAST_2_YEARS = 'last 2 years',
	LAST_3_YEARS = 'last 3 years',
}

export const getComparePeriodRange = ({
	startTime,
	endTime,
	compareType,
}: {
	startTime: Date;
	endTime: Date;
	compareType: string;
}) => {
	const start = moment(startTime);
	const end = moment(endTime);
	const diffDays = end.diff(start, 'day');

	if (compareType === 'previousYear') {
		return {
			start: new Date(start.subtract(1, 'year').format()),
			end: new Date(end.subtract(1, 'year').format()),
		};
	}
	// if (comparisonType.includes('noComparison')) {

	// }
	if (compareType === 'previousPeriod') {
		const matchedOption = getMachedDateRangeOption(startTime, endTime);
		if (matchedOption) {
			switch (matchedOption.value) {
				case TIME_RANGE.LAST_MONTH:
					return {
						start: start.subtract(1, 'month').toDate(),
						end: end.subtract(1, 'month').toDate(),
					};
				case TIME_RANGE.LAST_QUARTER:
					return {
						start: start.subtract(3, 'month').toDate(),
						end: end.subtract(3, 'month').toDate(),
					};
				case TIME_RANGE.LAST_YEAR:
					return {
						start: start.subtract(1, 'year').toDate(),
						end: end.subtract(1, 'year').toDate(),
					};
				default:
					break;
			}
		}

		return {
			start: start.subtract(diffDays + 1, 'days').toDate(),
			end: end.subtract(diffDays + 1, 'days').toDate(),
		};
	}
	return {
		start: start.toDate(),
		end: end.toDate(),
	};
};

export const getStartTimeAndEndTime = (timeRange?: TIME_RANGE) => {
	if (!timeRange) {
		return {start: '', end: ''};
	}
	switch (timeRange) {
		case TIME_RANGE.TODAY:
			return generateDateRangeOption(0);
		case TIME_RANGE.YESTERDAY:
			return generateDateRangeOption(1, {includeToday: false});
		case TIME_RANGE.LAST_7_DAYS:
			return generateDateRangeOption(6);
		case TIME_RANGE.LAST_30_DAYS:
			return generateDateRangeOption(29);
		case TIME_RANGE.LAST_60_DAYS:
			return generateDateRangeOption(59);
		case TIME_RANGE.LAST_90_DAYS:
			return generateDateRangeOption(89);
		case TIME_RANGE.LAST_120_DAYS:
			return generateDateRangeOption(119);
		case TIME_RANGE.LAST_MONTH:
			return generateFullMonthDateRangeOption(1);
		case TIME_RANGE.LAST_QUARTER:
			return generateFullQuarterDateRangeOption(1);
		case TIME_RANGE.LAST_YEAR:
			return generateFullYearDateRangeOption(1);
		case TIME_RANGE.LAST_1_YEAR:
			return generateDateRangeOptionByYear(1);
		case TIME_RANGE.LAST_2_YEARS:
			return generateDateRangeOptionByYear(2);
		case TIME_RANGE.LAST_3_YEARS:
			return generateDateRangeOptionByYear(3);
		default:
			return {start: '', end: ''};
	}
};
