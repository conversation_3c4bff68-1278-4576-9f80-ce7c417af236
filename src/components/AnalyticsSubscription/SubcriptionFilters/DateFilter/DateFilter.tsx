/* eslint-disable max-lines */
import {
	<PERSON><PERSON>,
	DatePicker,
	OptionList,
	Popover,
	Select,
	Stack,
	Tooltip,
} from '@shopify/polaris';
import {CalendarMinor} from '@shopify/polaris-icons';
import {useFormikContext} from 'formik';
import moment from 'moment-timezone';
import React, {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {Shipment_Analytics_Subscription_Types} from '@graphql/generated';
import {DateOptionList} from 'components/DateRangeSelector/components/DateOptionList';
import DatePickerTextField from 'components/DateRangeSelector/components/DatePickerTextField';
import {DATE_WITH_DASH} from 'constants/Date';
import {DATE_TYPE_OPTIONS} from 'constants/FilterOptions';
import {getIsPreviousPeriodRangeOutOfMinDate} from 'pages/Dashboard/components/FilterHeader/utils';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {useCustomPopupClassNameRef} from 'pages/Insurance/hooks/useCustomPopupClassNameRef';
import {toOrgMoment} from 'utils/day';

import styles from './index.module.scss';
import {
	getComparePeriodRange,
	getDateActivatorText,
	getDateComparisonActivatorText,
} from './utils';

interface Props {
	isCompare?: boolean;
	analyticsDashboardKey?: Shipment_Analytics_Subscription_Types;
}

export const DateFilter = ({isCompare, analyticsDashboardKey}: Props) => {
	const {t} = useTranslation();
	const isHideDateType = [
		Shipment_Analytics_Subscription_Types.Notifications,
		Shipment_Analytics_Subscription_Types.TrackingPages,
		Shipment_Analytics_Subscription_Types.ShipmentReviews,
		Shipment_Analytics_Subscription_Types.TrackingPagesAttributedOrdersOverTimeTable,
		Shipment_Analytics_Subscription_Types.TrackingPagesAttributedRevenueOverTimeTable,
		Shipment_Analytics_Subscription_Types.TrackingPagesMarketingClicksBySectionTable,
		Shipment_Analytics_Subscription_Types.TrackingPagesPerformanceOverTimeTable,
		Shipment_Analytics_Subscription_Types.TrackingPagesTopMarketingAssetsTable,
		Shipment_Analytics_Subscription_Types.TrackingPagesTopProductRecommendationsTable,
	].includes(analyticsDashboardKey as Shipment_Analytics_Subscription_Types);

	// 初始值逻辑=====================
	const {values, setFieldValue} = useFormikContext<any>();

	const [start, end, originRange] = useMemo(() => {
		const originStartDate = toOrgMoment(
			values.originFilter.startTime
		).toDate();
		const originEndDate = toOrgMoment(values.originFilter.endTime).toDate();

		const originRange = {
			start: originStartDate,
			end: originEndDate,
		};

		return !isCompare
			? [originStartDate, originEndDate, originRange]
			: [
					getComparePeriodRange({
						startTime: originStartDate,
						endTime: originEndDate,
						compareType: values.originFilter.compareTimeRange,
					}).start,
					getComparePeriodRange({
						startTime: originStartDate,
						endTime: originEndDate,
						compareType: values.originFilter.compareTimeRange,
					}).end,
					originRange,
			  ];
	}, [
		isCompare,
		values.originFilter.startTime,
		values.originFilter.endTime,
		values.originFilter.compareTimeRange,
	]);
	const [popoverActive, setPopoverActive] = useState(false);

	const dateType = values.originFilter?.dateType || 'created-at';
	const [latestOperation, setLatestOperation] = useState<'option' | 'date'>(
		(!isCompare && values.originFilter.timeRange) ||
			(isCompare && values.originFilter.compareOption)
			? 'option'
			: 'date'
	);

	const [selectedRangeOptions, setSelectedRangeOptions] = useState<string>(
		values.originFilter.timeRange
	);
	const [selectedComparison, setSelectedComparison] = useState<string[]>([
		values.originFilter.compareTimeRange,
	]);

	const [selectedDate, setSelectedDate] = useState({start, end});

	const [{month, year}, setDate] = useState({
		month: start?.getMonth(),
		year: start?.getFullYear(),
	});

	const divRef = useCustomPopupClassNameRef<HTMLDivElement>(
		styles['date-range-menu']
	);

	const {minDate, rangeOptions} = useDatePickerRangeOptions();
	const [errorMessage, setErrorMessage] = useState('');
	// 常量定义=============
	const DateTypeSlugKeyMap: Record<string, string> = {
		'created-at': t('CREATED_6dea5', 'Created'),
		'ordered-at': t('ORDERED_531c5', 'Ordered'),
		'picked-up-at': t('PICKEDUP_f535d', 'Picked up'),
	};
	const ComparisonSlugKeyMap: Record<string, string> = {
		noComparison: 'No comparison',
		previousPeriod: 'Compare: Previous period',
		previousYear: 'Compare: Previous year',
	};

	// 初始值赋值=====================
	const [activatorText, setActivatorText] = useState(
		getDateActivatorText({
			isOption: latestOperation === 'option',
			dateType: DateTypeSlugKeyMap[dateType],
			dateOption: selectedRangeOptions,
			startDate: selectedDate.start,
			endDate: selectedDate.end,
			isHideDateType,
		})
	);
	const [compareActivatorText, setCompareActivatorText] = useState(
		getDateComparisonActivatorText({
			compareOption: ComparisonSlugKeyMap[selectedComparison[0]],
			startDate: selectedDate.start,
			endDate: selectedDate.end,
		})
	);

	const activatorDom = (
		<div>
			<Button
				fullWidth
				textAlign="left"
				icon={CalendarMinor}
				onClick={() => {
					setPopoverActive(true);
					// setIsOpenSelectDate(!isOpenSelectDate);
				}}
			>
				{isCompare ? compareActivatorText : activatorText}
			</Button>
		</div>
	);
	useEffect(() => {
		isCompare &&
			setSelectedDate({
				start: getComparePeriodRange({
					startTime: toOrgMoment(
						values.originFilter.startTime
					).toDate(),
					endTime: toOrgMoment(values.originFilter.endTime).toDate(),
					compareType: values.originFilter.compareTimeRange,
				}).start,
				end: getComparePeriodRange({
					startTime: toOrgMoment(
						values.originFilter.startTime
					).toDate(),
					endTime: toOrgMoment(values.originFilter.endTime).toDate(),
					compareType: values.originFilter.compareTimeRange,
				}).end,
			});
	}, [
		values.originFilter.startTime,
		values.originFilter.endTime,
		values.originFilter.compareTimeRange,
		isCompare,
	]);
	return (
		<div ref={divRef} className={styles['date-range-menu']}>
			<Popover
				active={popoverActive}
				fullHeight
				onClose={() => {
					setPopoverActive(false);
				}}
				activator={activatorDom}
				fixed
			>
				<Popover.Pane>
					<div className={styles['date-filter-container']}>
						<div className={styles['date-filter']}>
							{!isCompare ? (
								<div>
									{!isHideDateType && (
										<div style={{padding: '20px 8px 0'}}>
											<Select
												options={DATE_TYPE_OPTIONS.map(
													item => {
														return {
															label: t(
																item.labelI18nKey
															),
															value: item.value,
														};
													}
												)}
												onChange={value => {
													setFieldValue(
														'originFilter.dateType',
														value
													);
												}}
												value={dateType}
												label={undefined}
											/>
										</div>
									)}
									<div
										style={{
											height: '331px',
											overflow: 'scroll',
										}}
									>
										<DateOptionList
											selectedDate={selectedDate}
											setSelectedDate={setSelectedDate}
											setDate={setDate}
											rangeOptions={rangeOptions.map(
												item => {
													return {
														label: item.label,
														value: item.value,
														range: {
															start: moment(
																item.range.start
															).format(
																DATE_WITH_DASH
															),
															end: moment(
																item.range.end
															).format(
																DATE_WITH_DASH
															),
														},
													};
												}
											)}
											setSelectedRangeOptions={
												setSelectedRangeOptions
											}
											otherChangeFunc={() => {
												setLatestOperation('option');
											}}
										/>
									</div>
								</div>
							) : (
								<div>
									<OptionList
										onChange={val => {
											setSelectedComparison(val);
											setLatestOperation('option');
											const startTime =
												getComparePeriodRange({
													startTime:
														values.originFilter
															.startTime,
													endTime:
														values.originFilter
															.endTime,
													compareType: val[0],
												}).start;

											const endTime =
												getComparePeriodRange({
													startTime:
														values.originFilter
															.startTime,
													endTime:
														values.originFilter
															.endTime,
													compareType: val[0],
												}).end;
											setSelectedDate({
												start: startTime,
												end: endTime,
											});
											setDate({
												year: startTime.getFullYear(),
												month: startTime.getMonth(),
											});
										}}
										options={[
											{
												value: 'previousPeriod',
												label: getIsPreviousPeriodRangeOutOfMinDate(
													originRange,
													minDate,
													selectedComparison[0]
												) ? (
													<Tooltip
														content={
															<div
																style={{
																	maxWidth:
																		'20rem',
																}}
															>
																{t(
																	'INCLUDESDA_3f3c2',
																	'Includes dates outside of your data retention period'
																)}
															</div>
														}
														preferredPosition="below"
													>
														<div
															style={{
																width: '183px',
															}}
														>
															{t(
																'PREVIOUSPE_36455',
																'Previous period'
															)}
														</div>
													</Tooltip>
												) : (
													t(
														'PREVIOUSPE_36455',
														'Previous period'
													)
												),
												disabled:
													getIsPreviousPeriodRangeOutOfMinDate(
														originRange,
														minDate,
														'previousPeriod'
													),
											},
											{
												value: 'previousYear',

												label: getIsPreviousPeriodRangeOutOfMinDate(
													originRange,
													minDate,
													'previousYear'
												) ? (
													<Tooltip
														content={
															<div
																style={{
																	maxWidth:
																		'20rem',
																}}
															>
																{t(
																	'INCLUDESDA_3f3c2',
																	'Includes dates outside of your data retention period'
																)}
															</div>
														}
														preferredPosition="below"
													>
														<div
															style={{
																width: '183px',
															}}
														>
															{t(
																'PREVIOUSYE_1c23e',
																'Previous year'
															)}
														</div>
													</Tooltip>
												) : (
													t(
														'PREVIOUSYE_1c23e',
														'Previous year'
													)
												),

												disabled:
													getIsPreviousPeriodRangeOutOfMinDate(
														originRange,
														minDate,
														'previousYear'
													),
											},
										]}
										selected={selectedComparison}
									/>
									<div
										style={{
											position: 'absolute',
											bottom: '13px',
											left: '20px',
										}}
									>
										<Button
											onClick={() => {
												setSelectedComparison([
													'noComparison',
												]);
												setFieldValue(
													'originFilter.compareTimeRange',
													'noComparison'
												);
												setPopoverActive(false);
												setCompareActivatorText(
													t(
														'NOCOMPARIS_ce08d',
														'No comparison'
													)
												);
											}}
										>
											{t(
												'NOCOMPARIS_ce08d',
												'No comparison'
											)}
										</Button>
									</div>
								</div>
							)}
						</div>
						<div style={{width: '580px', padding: '16px'}}>
							<div style={{marginBottom: '16px'}}>
								<DatePickerTextField
									selectedDate={selectedDate}
									setSelectedDate={setSelectedDate}
									setDate={setDate}
									errorMessage={errorMessage}
									setErrorMessage={setErrorMessage}
									disableDatesBefore={minDate}
									disableDatesAfter={toOrgMoment().toDate()}
								/>
							</div>
							<DatePicker
								month={month}
								year={year}
								selected={selectedDate}
								onChange={value => {
									setSelectedDate(value);
									!isCompare && setSelectedRangeOptions('');
									isCompare && setSelectedComparison([]);
									setLatestOperation('date');
								}}
								onMonthChange={(month, year) => {
									setDate({month, year});
								}}
								disableDatesBefore={minDate}
								disableDatesAfter={toOrgMoment().toDate()}
								allowRange
								multiMonth
							/>
						</div>
					</div>
				</Popover.Pane>
				<Popover.Pane fixed>
					<div style={{padding: 20}}>
						<Stack distribution="trailing">
							<Button
								onClick={() => {
									setPopoverActive(false);
								}}
							>
								{t('CANCEL_4db5e')}
							</Button>

							<Button
								onClick={() => {
									if (isCompare) {
										setCompareActivatorText(
											getDateComparisonActivatorText({
												compareOption:
													ComparisonSlugKeyMap[
														selectedComparison[0]
													],
												startDate: selectedDate.start,
												endDate: selectedDate.end,
											})
										);

										setFieldValue('originFilter', {
											...values.originFilter,

											compareStartTime:
												getComparePeriodRange({
													startTime:
														selectedComparison[0]
															? values
																	.originFilter
																	.startTime
															: selectedDate.start,
													endTime:
														selectedComparison[0]
															? values
																	.originFilter
																	.endTime
															: selectedDate.end,
													compareType:
														selectedComparison[0],
												}).start,
											compareEndTime:
												getComparePeriodRange({
													startTime:
														selectedComparison[0]
															? values
																	.originFilter
																	.startTime
															: selectedDate.start,
													endTime:
														selectedComparison[0]
															? values
																	.originFilter
																	.endTime
															: selectedDate.end,
													compareType:
														selectedComparison[0],
												}).end,
											dateType: dateType,

											compareTimeRange:
												latestOperation === 'date'
													? ''
													: selectedComparison[0],
										});
									} else {
										setActivatorText(
											getDateActivatorText({
												isOption:
													latestOperation ===
													'option',

												dateType:
													DateTypeSlugKeyMap[
														dateType
													],
												dateOption:
													selectedRangeOptions,
												startDate: selectedDate.start,
												endDate: selectedDate.end,
												isHideDateType,
											})
										);

										setFieldValue('originFilter', {
											...values.originFilter,
											startTime: selectedDate.start,
											endTime: selectedDate.end,
											startDate: toOrgMoment(
												selectedDate.start
											).format(DATE_WITH_DASH),
											endDate: toOrgMoment(
												selectedDate.end
											).format(DATE_WITH_DASH),
											dateType: dateType,
											timeRange: selectedRangeOptions,
										});
									}
									setPopoverActive(false);
								}}
								primary
								disabled={false}
							>
								{t('APPLY_ff7ba')}
							</Button>
						</Stack>
					</div>
				</Popover.Pane>
			</Popover>
		</div>
	);
};
