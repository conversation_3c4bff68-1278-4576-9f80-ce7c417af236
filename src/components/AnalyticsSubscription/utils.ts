import {isString, pick} from 'lodash';

import {
	EmailSchedule,
	Email_Recipient_Group,
	Email_Recipient_Type,
	ExportType,
	Maybe,
	Schedule_Type,
	Shipment_Analytics_Subscription_Types,
	TrackingCountry,
} from '@graphql/generated';
import {getFormattedQueryFilter} from 'components/MoreFiltersSheet/utils/analyticsUtils';
import {CarrierWithService} from 'pages/Dashboard/hooks/variables/interface';
import {formatFilterV2} from 'pages/Shipments/utils/formatFilterV2';
import {toOrgMoment} from 'utils/day';

export const decodeCountries = (str: string) => {
	if (!str) {
		return undefined;
	}
	try {
		return (JSON.parse(str) as TrackingCountry[])
			.map(({country, states}) => {
				return {
					country: isString(country) ? country : '',
					states: states
						?.filter(({state}) => isString(state) && state !== '')
						.map(({state, cities}) => ({
							state,
							cities: cities?.filter(
								city => isString(city) && city !== ''
							),
						})),
				};
			})
			.filter(({country}) => country !== '');
	} catch (error) {
		return undefined;
	}
};

export const decodeSlugs = (str: string) => {
	if (!str) {
		return undefined;
	}
	try {
		return (JSON.parse(str) as CarrierWithService[])
			.map(({slug, serviceTypes}) => {
				return {
					slug: isString(slug) ? slug : '',
					serviceTypes: serviceTypes?.filter(
						serviceType =>
							isString(serviceType) && serviceType !== ''
					),
				};
			})
			.filter(({slug}) => slug !== '');
	} catch (error) {
		return undefined;
	}
};

export interface FormValueProps {
	isRecurring: string[];
	name: string;
	type: Shipment_Analytics_Subscription_Types;
	emailRecipientType: Email_Recipient_Type[];
	emailRecipients: Maybe<string[]>;
	emailRecipientGroup: Email_Recipient_Group;
	emailExternalRecipients: Maybe<string[]>;
	emailSubject: string;
	emailText: string;
	schedule: {
		type: Schedule_Type;
		sendAt: string;
		timezone: string;
		weeklyRule: string[];
		dayInMonthRule: string[];
		weekdayInMonthRule: {
			index: string;
			weekday: string;
		};
	};
	sendEmptyResult: boolean;
	exportFileType: string;
	originFilter: any;
}

export const convertValues = (values: FormValueProps, accountId: string) => {
	const {
		emailRecipients: _emailRecipients,
		emailExternalRecipients,
		emailRecipientGroup,
		exportFileType,
		emailRecipientType: _emailRecipientType,
	} = values;

	const [emailRecipientType] = _emailRecipientType;

	let emailRecipients;

	switch (emailRecipientType) {
		case Email_Recipient_Type.Me:
			emailRecipients = [accountId || ''];
			break;
		case Email_Recipient_Type.SpecificMembers:
			emailRecipients = _emailRecipients || [];
			break;
		case Email_Recipient_Type.AllMembers:
			emailRecipients = null;
			break;
		default:
	}
	const schedule: EmailSchedule = {
		...pick(values.schedule, ['type', 'sendAt', 'timezone']),
	};
	const scheduleType = values.schedule.type;

	switch (scheduleType) {
		case Schedule_Type.Weekly:
			schedule.weeklyRule = values.schedule.weeklyRule?.map(Number);
			break;
		case Schedule_Type.DayInMonth:
			schedule.dayInMonthRule =
				values.schedule.dayInMonthRule?.map(Number);
			break;
		case Schedule_Type.WeekdayInMonth:
			schedule.weekdayInMonthRule = {
				index: Number(values.schedule.weekdayInMonthRule?.index),
				weekday: Number(values.schedule.weekdayInMonthRule?.weekday),
			};
			break;
		default:
	}

	return {
		...pick(values, [
			'name',
			'emailSubject',
			'emailText',
			'sendEmptyResult',
		]),
		emailRecipientType,
		exportFileType: (exportFileType as ExportType) || null,
		emailRecipients,
		emailRecipientGroup:
			emailRecipientGroup || Email_Recipient_Group.Internal,
		emailExternalRecipients,
		schedule,
		originFilter: {
			...values.originFilter,
			startTime: toOrgMoment(values.originFilter?.startTime).format(),
			endTime: toOrgMoment(values.originFilter?.endTime)
				.endOf('day')
				.format(),
			compareStartTime: toOrgMoment(
				values.originFilter?.compareStartTime
			).format(),
			compareEndTime: toOrgMoment(values.originFilter?.compareEndTime)
				.endOf('day')
				.format(),
			advancedFilters: formatFilterV2(
				getFormattedQueryFilter(
					(values.originFilter?.advancedFilters as any)?.filterV2
				)
			),
		},
	};
};
