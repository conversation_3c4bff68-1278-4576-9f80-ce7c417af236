import React, {ComponentType, useEffect} from 'react';

interface IDelayRenderHOCProps {
	delay?: number;
	fallback?: React.ReactNode;
}

function withDelayRender<Props = Record<string, unknown>>(
	Comp: ComponentType<Props>
) {
	const DelayRenderer = (props: Props & IDelayRenderHOCProps) => {
		const {delay = 500, fallback = null} = props;
		const [isRender, setIsRender] = React.useState(false);
		useEffect(() => {
			setTimeout(() => {
				setIsRender(true);
			}, delay);
		}, []);
		return isRender ? <Comp {...props} /> : <>{fallback}</>;
	};
	return DelayRenderer;
}

export default withDelayRender;
