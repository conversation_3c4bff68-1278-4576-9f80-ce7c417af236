import {capture} from '@aftership/datacat';
import {<PERSON><PERSON>, Card} from '@shopify/polaris';
import React, {useEffect} from 'react';

import styles from './EmptyStateCard.module.scss';

interface Props {
	Icon: React.FunctionComponent<
		React.SVGProps<SVGSVGElement> & {
			title?: string | undefined;
		}
	>;
	header: string;
	desc: string;
	btnGroup: {
		primaryAction: {
			onClick: () => void;
			content: string;
		};
		secondaryAction?: {
			onClick: () => void;
			content: string;
		};
	};
}
const EmptyStateCard: React.FC<Props> = ({Icon, header, desc, btnGroup}) => {
	useEffect(() => {
		capture('impression', {
			target: 'growth_empty_page',
			entrance: header,
		});
	}, []);
	return (
		<Card>
			<div>
				<div className={styles.container}>
					<Icon width={200} height={200} />
					<div className={styles.header}>{header}</div>
					<div className={styles.desc}>{desc}</div>
					<div className={styles.btnGroup}>
						<Button
							primary
							onClick={() => {
								btnGroup.primaryAction.onClick();
								capture('click', {
									target: 'growth_empty_page',
									entrance: btnGroup.primaryAction.content,
								});
							}}
						>
							{btnGroup.primaryAction.content}
						</Button>
						{btnGroup.secondaryAction && (
							<div className={styles.secondaryBtn}>
								<Button
									onClick={() => {
										btnGroup.secondaryAction?.onClick();
										if (btnGroup.secondaryAction?.content) {
											capture('click', {
												target: 'growth_empty_page',
												entrance:
													btnGroup.secondaryAction
														?.content,
											});
										}
									}}
								>
									{btnGroup.secondaryAction.content}
								</Button>
							</div>
						)}
					</div>
				</div>
			</div>
		</Card>
	);
};

export default EmptyStateCard;
