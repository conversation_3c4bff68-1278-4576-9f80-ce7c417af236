.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 24px;

	.header {
		font-weight: 400;
		font-size: 26px;
		line-height: 32px;
		color: #000000;
	}

	.desc {
		margin-top: 16px;
		font-weight: 400;
		font-size: 14px;
		line-height: 20px;
		color: #000000;
		max-width: 500px;
		text-align: center;
	}

	.btnGroup {
		margin: 36px 0;
		display: flex;
		align-items: center;
		justify-content: center;

		.secondaryBtn {
			margin-left: 14px;
		}
	}
}