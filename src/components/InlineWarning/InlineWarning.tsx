import {Icon, TextStyle} from '@shopify/polaris';
import type {InlineErrorProps} from '@shopify/polaris';
import {InfoMinor} from '@shopify/polaris-icons';
import React from 'react';

interface Props
	extends Omit<InlineErrorProps, 'fieldID'>,
		React.HTMLProps<HTMLDivElement> {
	fieldID?: string;
}

export default function InlineWarning(props: Props) {
	return (
		<div
			{...props}
			style={{
				display: 'flex',
				flexWrap: 'nowrap',
				alignItems: 'flex-start',
				...props.style,
			}}
		>
			<div
				id={props.fieldID}
				style={{fontSize: 14, lineHeight: 20, marginRight: '0.4em'}}
			>
				<Icon color="warning" source={InfoMinor} />
			</div>
			{/* old version Polaris don't support pass color prop */}
			<div style={{color: 'var(--p-icon-warning)'}}>
				<TextStyle>{props.message}</TextStyle>
			</div>
		</div>
	);
}
