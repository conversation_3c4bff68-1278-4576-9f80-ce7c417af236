import React from 'react';
import {connect} from 'react-redux';
import {withRouter} from 'react-router';
import {compose} from 'redux';
import {TRIGGERS} from 'constants/Resources';
import {updateRequest} from 'actions/resources';
import EnableNotificationList from './EnableNotificationList';
import './EnableNotificationList.css';

const tabToIndex = {
	'to-customers': 0,
	'to-subscribers': 1,
	'to-yourself': 2,
};

const connectStateAndDispatch = connect(
	(state, ownProps) => {
		return {
			tab: ownProps.match.params.tab,
			tabIndex: tabToIndex[ownProps.match.params.tab],
		};
	},
	dispatch => ({
		updateTriggers: action => {
			return dispatch(updateRequest(TRIGGERS)(action));
		},
	})
);

const enhance = compose(withRouter, connectStateAndDispatch);
const EnableNotificationListWithRouter = enhance(EnableNotificationList);

export default props => {
	return <EnableNotificationListWithRouter {...props} />;
};
