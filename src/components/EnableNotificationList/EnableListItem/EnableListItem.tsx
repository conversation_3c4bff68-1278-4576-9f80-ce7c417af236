// @ts-nocheck
import {Card, Stack, Link} from '@shopify/polaris';
import {LockMinor} from '@shopify/polaris-icons';
import {get, kebabCase} from 'lodash';
import PropTypes from 'prop-types';
import React, {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {useHistory} from 'react-router';

import CardSubHeading from 'components/CardSubHeading';
import {
	NOTIFICATION_ALLOW_TYPES,
	NotificationType,
} from 'constants/billings/features';
import {TARGET_TO_RECEIVER_TYPE_MAP} from 'email_editor/constants/route';
import {useFeatureValidator} from 'hooks/billings';
import {useFreeSMS} from 'hooks/useFreeSMS';
import SettingToggleField from 'pages/components/Form/SettingToggleField';
import {gaClickFn} from 'utils/gtag';

import FacebookToggle from '../FacebookToggle';

function EnableListItem(props) {
	const {t} = useTranslation();
	const history = useHistory();

	const {title, target, loading, isUpdating, tag, isTriggerUpdating} = props;

	const [upgradeEmail, canChangeEmail] = useFeatureValidator(
		feature =>
			feature.code === NOTIFICATION_ALLOW_TYPES &&
			Boolean(get(feature, `options.${NotificationType.email}`)),
		undefined,
		{code: `${NOTIFICATION_ALLOW_TYPES}_${NotificationType.email}`}
	);

	const [upgradeSms, canChangeSms] = useFeatureValidator(
		feature =>
			feature.code === NOTIFICATION_ALLOW_TYPES &&
			Boolean(get(feature, `options.${NotificationType.sms}`)),
		undefined,
		{code: `${NOTIFICATION_ALLOW_TYPES}_${NotificationType.sms}`}
	);

	const {canTurnOnFreeSMS, isToCustomer} = useFreeSMS({
		tag,
		target,
	});

	// facebook messenger trigger is only for to_subscriber
	const isTargetSubscriber = target.includes('subscriber');

	const shouldFieldChangeSMS = () => {
		if (canTurnOnFreeSMS) {
			return true;
		}
		if (!canChangeSms) {
			upgradeSms();
			return false;
		}
		return true;
	};
	return (
		<div id={tag}>
			<Card.Section>
				<CardSubHeading>{title}</CardSubHeading>

				<Stack vertical spacing="tight">
					<SettingToggleField
						name={`email_to_${target}.${tag}`}
						text={{pre: t('EMAIL_15cfa'), suf: t('EMAIL_b12ce')}}
						loading={loading}
						updating={isTriggerUpdating(
							`email_to_${target}-${tag}`
						)}
						shouldFieldChange={
							!canChangeEmail
								? () => {
										upgradeEmail();
										return false;
								  }
								: () => true
						}
						icon={!canChangeEmail ? LockMinor : undefined}
						prefix={
							<Stack>
								<Link
									url={`/notifications/setting/${
										TARGET_TO_RECEIVER_TYPE_MAP[target]
									}/${kebabCase(tag)}/template/email/edit`}
									onClick={gaClickFn('E10020')}
								>
									{t('EDIT_47fc5')}
								</Link>
								<Stack.Item />
							</Stack>
						}
					/>
					<SettingToggleField
						name={`sms_to_${target}.${tag}`}
						text={{pre: t('SMS_d596e'), suf: t('SMS_d596e')}}
						loading={loading}
						updating={isUpdating(`sms_to_${target}-${tag}`)}
						shouldFieldChange={shouldFieldChangeSMS}
						icon={
							!canChangeSms && !(canTurnOnFreeSMS && isToCustomer)
								? LockMinor
								: undefined
						}
						prefix={
							<Stack>
								<Link
									onClick={() => {
										gaClickFn('E10021');
										history.push({
											pathname: `/notifications/setting/${kebabCase(
												tag
											)}/template/sms/edit`,
											search: `?target=${target}`,
										});
									}}
								>
									{t('EDIT_95a23')}
								</Link>
								<Stack.Item />
							</Stack>
						}
					/>

					{/* condition for showing the messenger trigger */}
					{isTargetSubscriber && (
						<FacebookToggle
							name={`messenger_to_${target}.${tag}`}
							status={status}
							loading={loading}
							updating={isUpdating(
								`messenger_to_${target}-${tag}`
							)}
						/>
					)}
				</Stack>
			</Card.Section>
		</div>
	);
}

EnableListItem.propTypes = {
	isUpdating: PropTypes.func.isRequired,
	isTriggerUpdating: PropTypes.func.isRequired,
	title: PropTypes.string.isRequired,
	tag: PropTypes.string.isRequired,
	target: PropTypes.string.isRequired,
	loading: PropTypes.bool,
};

EnableListItem.defaultProps = {
	loading: true,
};

export default memo(EnableListItem);
