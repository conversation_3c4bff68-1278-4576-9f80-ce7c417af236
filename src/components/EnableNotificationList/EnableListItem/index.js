import {connect} from 'react-redux';
import {TRIGGERS} from 'constants/Resources';

import isUpdating, {getUpdating} from 'selectors/isUpdating';

import EnableListItem from './EnableListItem';

const isTriggerUpdating = state => {
	return key => {
		const triggerKeys = getUpdating(state, TRIGGERS);
		return triggerKeys[key];
	};
};

const mapStateToProps = state => ({
	isUpdating: isUpdating(state, TRIGGERS),
	isTriggerUpdating: isTriggerUpdating(state),
});

export default connect(mapStateToProps)(EnableListItem);
