import React, {PureComponent, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {reduxForm} from 'redux-form';
import {withTranslation} from 'react-i18next';
import {differenceObject} from 'utils/object';
import {NOTIFICATION_SETTINGS} from 'constants/FormNames';
import {useUserNotificationTriggers} from 'hooks/user/useUserNotificationTriggers';
import EnableListItem from './EnableListItem';

// need https://secure.aftership.io/json/bootstrap add OutForDelivery
const Items = [
	{tag: 'InfoReceived', title: 'INFO-RECEIVED_C'},
	{tag: 'InTransit', title: 'IN-TRANSIT_C'},
	{tag: 'OutForDelivery', title: 'OUT-FOR-DELIVERY_C'},
	{tag: 'AvailableForPickup', title: 'AVAILABLEFORPICKUP_C'},
	{tag: 'Delivered', title: 'DELIVERED_C'},
	{tag: 'Exception', title: 'EXCEPTION_C'},
	{tag: 'AttemptFail', title: 'ATTEMPT-FAIL_C'},
];

class EnableNotificationList extends PureComponent {
	static propTypes = {
		updateTriggers: PropTypes.func.isRequired,
		target: PropTypes.string,
		isLoading: PropTypes.bool,
		t: PropTypes.func.isRequired,
	};

	static defaultProps = {
		isLoading: true,
		target: 'buyer',
	};

	render() {
		const {target, isLoading} = this.props;

		return (
			<div className="EnableNotificationList">
				{Items.map(item => (
					<EnableListItem
						{...this.props}
						key={item.tag}
						tag={item.tag}
						title={this.props.t(item.title)}
						target={target}
						loading={isLoading}
					/>
				))}
			</div>
		);
	}
}

const NotificationForm = reduxForm({
	form: NOTIFICATION_SETTINGS,
	enableReinitialize: true,
	onChange: (values, dispatch, props, preValues) => {
		if (props.dirty) {
			props.updateTriggers({
				diff: differenceObject(values, preValues),
				...values,
			});
		}
	},
})(withTranslation()(EnableNotificationList));

/**
 * 如果连续切换的两个页面的 form 是同一个，form 的值会丢失，必须完全去掉此 form 才能解决 （unmount 没有用）
 * FIXME: remove redux form
 */
export default props => {
	const [inited, setInited] = useState(false);

	const {data: initialValues, isLoading} = useUserNotificationTriggers();

	useEffect(() => {
		setInited(true);
	}, []);

	if (!inited) return null;

	return (
		<NotificationForm
			{...props}
			initialValues={initialValues}
			isLoading={isLoading}
		/>
	);
};
