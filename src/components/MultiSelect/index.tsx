import {<PERSON><PERSON>, <PERSON><PERSON>, OptionList, Popover, TextField} from '@shopify/polaris';
import {SearchMinor} from '@shopify/polaris-icons';
import {OptionDescriptor} from '@shopify/polaris/dist/types/latest/src/components/OptionList';
import React, {useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';

import styles from './index.module.scss';

interface MultiSelectProps {
	selected: string[];
	options: OptionDescriptor[];
	onChange: (value: string[]) => void;
	label?: React.ReactNode;
	helpText?: React.ReactNode;
	disabled?: boolean;
	placeholder?: string;
	searchPlaceholder?: string;
}

export function MultiSelect({
	label,
	options,
	selected,
	helpText,
	disabled,
	placeholder,
	searchPlaceholder,
	onChange,
}: MultiSelectProps) {
	const {t} = useTranslation();
	const [popoverActive, setPopoverActive] = useState(false);
	const [input, setInput] = useState('');

	const filterOptions = useMemo(() => {
		const query = input.toLocaleLowerCase();
		return options.filter(option =>
			option.value.toLocaleLowerCase().includes(query)
		);
	}, [input, options]);

	return (
		<div className={styles.selectContainer}>
			<Popover
				autofocusTarget="none"
				active={popoverActive}
				activator={
					<div className={styles.select}>
						{label && (
							<div className="Polaris-Labelled__LabelWrapper">
								<div className="Polaris-Label">
									<p className="Polaris-Label__Text">
										{label}
									</p>
								</div>
							</div>
						)}

						<Button
							fullWidth
							disabled={disabled}
							onClick={() => setPopoverActive(val => !val)}
							disclosure="select"
						>
							{placeholder}
						</Button>
					</div>
				}
				preferredAlignment="right"
				preferredPosition="below"
				onClose={() => setPopoverActive(false)}
				fullWidth
			>
				<Popover.Pane fixed>
					<div className={styles.searchInput}>
						<TextField
							label=""
							value={input}
							onChange={setInput}
							placeholder={searchPlaceholder}
							prefix={<Icon source={SearchMinor} />}
						/>
					</div>
					<div className={styles.optionList}>
						{filterOptions.length ? (
							<OptionList
								options={filterOptions}
								selected={selected}
								onChange={onChange}
								allowMultiple
							/>
						) : (
							<div
								style={{
									color: '#8C9196',
									padding: 16,
								}}
							>
								{t('NOMATCHEDR_4d71e')}
							</div>
						)}
					</div>
				</Popover.Pane>
			</Popover>
			{helpText && <div className={styles.helpText}>{helpText}</div>}
		</div>
	);
}
