.selectContainer {
	.select {
		:global(.<PERSON>is-Button__Text) {
			width: 100%;
			font-weight: 400;
			line-height: 20px;
			text-align: left;
			color: var(--text-subdued, #6d7175);
		}
		:global(.Polaris-Button--fullWidth) {
			padding-left: 10px;
		}
	}
	.helpText {
		color: var(--text-subdued, #6d7175);
		margin-top: 4px;
	}
}

.searchInput {
	padding: 16px 10px 4px;
	width: 100%;
	background: #fff;
}
.optionList {
	max-height: 250px;
	overflow: scroll;
	:global(.Polaris-OptionList) {
		padding-top: 0px;
	}
}
