import {Banner, BannerProps} from '@shopify/polaris';
import React from 'react';
import styled from 'styled-components';

const BannerContainer = styled.div<{
	hasMarginTop?: boolean;
	hasMarginBottom?: boolean;
}>`
	margin-top: ${({hasMarginTop}) => (hasMarginTop ? '16px' : 0)};
	margin-bottom: ${({hasMarginBottom}) => (hasMarginBottom ? '16px' : 0)};
	.Polaris-Link {
		color: #2c6ecb !important;
	}
`;

export const BannerWrapper = ({
	children,
	hasMarginTop,
	hasMarginBottom,
	...rest
}: React.PropsWithChildren<
	BannerProps & {
		hasMarginTop?: boolean;
		hasMarginBottom?: boolean;
	}
>) => {
	return (
		<BannerContainer
			hasMarginTop={hasMarginTop}
			hasMarginBottom={hasMarginBottom}
		>
			<Banner {...rest}>
				<p>{children}</p>
			</Banner>
		</BannerContainer>
	);
};
