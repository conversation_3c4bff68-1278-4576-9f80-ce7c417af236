import React, {PureComponent} from 'react';
import {DataTable as PolarisDataTable} from '@shopify/polaris';

export default class DataTable extends PureComponent {
	/**
	 * a hack because Polaris DataTable need to use a resize event to re-calculate the height of cells
	 * TODO: trigger resize it only when absolutely necessary
	 */
	componentDidUpdate() {
		window.dispatchEvent(new Event('resize'));
	}

	render() {
		return <PolarisDataTable {...this.props} />;
	}
}
