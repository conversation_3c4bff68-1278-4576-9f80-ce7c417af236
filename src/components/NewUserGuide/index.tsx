import './index.css';

interface GuideOptions {
	steps: Array<{
		ele: HTMLElement | string;
		content?: HTMLElement;
	}>;
}

export default class NewUserGuide {
	options: GuideOptions;
	container: HTMLElement;
	stepIndex: number = -1;
	constructor(options: GuideOptions) {
		this.options = options;
		document.body.appendChild(
			getElementFromText(`
						<div id="newUserGuide">
							<div id="newUserGuideContainer"></div>
							<div id="newUserGuideWrapper"></div>
						</div>
						`)
		);
		this.container = document.getElementById('newUserGuideContainer')!;
		window.addEventListener('scroll', this.setPosition);
		document.documentElement.addEventListener('scroll', this.setPosition);
	}

	private get currentEle(): HTMLElement | null {
		if (!this.currentStep) return null;
		let target;
		if (typeof this.currentStep.ele === 'string') {
			target = document.querySelector<HTMLElement>(this.currentStep.ele);
		} else {
			target = this.currentStep.ele;
		}
		return target;
	}

	private get currentStep() {
		return this.options.steps[this.stepIndex];
	}
	private get root() {
		return document.getElementById('newUserGuide');
	}

	private runStep() {
		if (!this.currentStep || !this.currentEle) return;
		if (this.currentStep.content) {
			if (this.container.firstChild) {
				this.container.replaceChild(
					this.currentStep.content,
					this.container.firstChild
				);
			} else {
				this.container.appendChild(this.currentStep.content);
			}
		}

		this.setPosition();
		this.currentEle.scrollIntoView({block: 'center'});
	}

	public get isEnd() {
		return this.stepIndex >= this.options.steps.length - 1;
	}

	public setStep(step: number) {
		this.stepIndex = step;
	}

	public setOptions(options: GuideOptions) {
		this.options = {
			...this.options,
			...options,
		};
	}

	public nextStep() {
		this.stepIndex += 1;
		this.runStep();
	}

	public prevStep() {
		this.stepIndex -= 1;
		this.runStep();
	}

	public async destory() {
		this.container.classList.add('newUserGuideHide');
		await new Promise(resolve => setTimeout(resolve, 250)); // await for animation

		if (this.root) {
			this.root.parentNode?.removeChild(this.root);
		}

		window.removeEventListener('scroll', this.setPosition);
		document.documentElement.removeEventListener(
			'scroll',
			this.setPosition
		);
	}

	private setPosition = () => {
		const target = this.currentEle;
		if (!target || !this.root) return;
		this.root.style.display = 'block';
		const container = this.container;
		container.style.left =
			target.getBoundingClientRect().left +
			document.documentElement.scrollLeft +
			'px';
		container.style.top =
			target.getBoundingClientRect().top +
			document.documentElement.scrollTop +
			'px';
		container.style.width = target.clientWidth + 'px';
		container.style.height = target.clientHeight + 'px';
	};
}

function getElementFromText(text: string) {
	const temp = document.createElement('div');
	temp.innerHTML = text;
	return temp;
}
