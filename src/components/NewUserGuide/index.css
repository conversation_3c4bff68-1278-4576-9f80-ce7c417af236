#newUserGuideWrapper {
	width: 100vw;
	height: 100vh;
	position: fixed;
	z-index: 9999;
	left: 0;
	top: 0;
	display: block;
	content: '';
}
#newUserGuideContainer {
	position: absolute;
	width: 0;
	height: 0;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	border: 0 solid #000;
	z-index: 10000;
	transition: all 0.25s;
	outline: 2000px solid rgba(0, 0, 0, 0.3);
	overflow: visible;
	display: block;
}
#newUserGuide {
	display: none;
}
.newUserGuideHide {
	opacity: 0;
}
