import {Popover, Button, DatePicker, TextStyle} from '@shopify/polaris';
import {CalendarMinor} from '@shopify/polaris-icons';
import moment from 'moment-timezone';
import React, {useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';
import styled from 'styled-components';

const ButtonContainer = styled.div`
	.Polaris-Button__Content {
		justify-content: flex-start;
		font-weight: normal;
	}
`;

const DateSingleSelector = ({
	start,
	end,
	onDateChange,
	isSelected,
	placeholder,
	disableDatesBefore,
	disableDatesAfter,
	format,
	disabled,
}: {
	start: Date;
	end: Date;
	onDateChange: ({start, end}: {start: Date; end: Date}) => void;
	isSelected?: boolean;
	placeholder?: string;
	disableDatesBefore?: Date;
	disableDatesAfter?: Date;
	format?: string;
	disabled?: boolean;
}) => {
	const {t} = useTranslation();
	const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
	const [{month, year}, setDate] = useState({
		month: start.getMonth(),
		year: start.getFullYear(),
	});
	const [selectedDate, setSelectedDate] = useState({start, end});

	const togglePopoverActive = useCallback(() => {
		if (disabled) {
			return;
		}
		setIsDatePickerOpen(!isDatePickerOpen);
	}, [isDatePickerOpen, disabled]);

	const ref = useCallback(node => {
		if (node) {
			const container = node.closest(
				'.Polaris-PositionedOverlay'
			) as HTMLDivElement;
			container.style.zIndex = '521';
		}
	}, []);

	return (
		<Popover
			active={isDatePickerOpen}
			onClose={togglePopoverActive}
			activator={
				<ButtonContainer>
					<Button
						fullWidth
						onClick={togglePopoverActive}
						icon={CalendarMinor}
						disabled={disabled}
					>
						{isSelected
							? moment(start).format(format || 'MMM D, YYYY')
							: ((
									<span>
										{placeholder ? (
											<TextStyle variation="subdued">
												{placeholder}
											</TextStyle>
										) : (
											t('Select a date')
										)}
									</span>
							  ) as unknown as string)}
					</Button>
				</ButtonContainer>
			}
			preferredPosition="below"
		>
			<div
				role="button"
				tabIndex={0}
				ref={ref}
				onClick={e => e.stopPropagation()}
				style={{width: 290, height: 284, padding: '20px 16px'}}
			>
				<DatePicker
					month={month}
					year={year}
					selected={selectedDate}
					onChange={value => {
						setSelectedDate(value);
						onDateChange(value);
						togglePopoverActive();
					}}
					onMonthChange={(month, year) => setDate({month, year})}
					disableDatesBefore={disableDatesBefore}
					disableDatesAfter={disableDatesAfter}
				/>
			</div>
		</Popover>
	);
};

export default DateSingleSelector;
