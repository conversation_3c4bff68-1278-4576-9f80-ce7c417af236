import React from 'react';
import PropTypes from 'prop-types';
import {TextContainer} from '@shopify/polaris';
import {useTranslation} from 'react-i18next';
import Modal from 'components/Modal';

export default function ConfirmModal({
	isOpen,
	closeModal,
	callback,
	title,
	message,
	destructive,
	actionName,
	cancelName,
	loading,
	disableAutoClose,
}) {
	const {t} = useTranslation();
	const onCancel = () => {
		callback(false);
		closeModal();
	};

	const onConfirm = () => {
		callback(true);
		if (disableAutoClose) return;
		closeModal();
	};

	return (
		<Modal
			open={isOpen}
			onClose={onCancel}
			title={title}
			primaryAction={{
				content: actionName,
				destructive,
				loading,
				onAction: onConfirm,
			}}
			secondaryActions={[
				{
					content: cancelName || t('CANCEL_12fu1'),
					onAction: onCancel,
				},
			]}
		>
			<Modal.Section>
				<TextContainer>
					<p>{message}</p>
				</TextContainer>
			</Modal.Section>
		</Modal>
	);
}

ConfirmModal.propTypes = {
	isOpen: PropTypes.bool.isRequired,
	closeModal: PropTypes.func.isRequired,
	callback: PropTypes.func.isRequired,
	message: PropTypes.string.isRequired,
	destructive: PropTypes.bool.isRequired,
	title: PropTypes.string.isRequired,
	actionName: PropTypes.string.isRequired,
	cancelName: PropTypes.string,
	loading: PropTypes.bool.isRequired,
	disableAutoClose: PropTypes.bool.isRequired,
};

ConfirmModal.defaultProps = {
	cancelName: '',
};
