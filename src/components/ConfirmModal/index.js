import {connect} from 'react-redux';
import getConfirmModalState from 'selectors/getConfirmModalState';
import {hideConfirmModal} from 'actions/confirmModal';

import ConfirmModal from './ConfirmModal';

const mapStateToProps = state => {
	const confirmModalState = getConfirmModalState(state);
	return {
		isOpen: confirmModalState.open,
		title: confirmModalState.title,
		message: confirmModalState.message,
		destructive: confirmModalState.destructive,
		actionName: confirmModalState.actionName,
		callback: confirmModalState.callback,
		cancelName: confirmModalState.cancelName,
		loading: confirmModalState.loading,
		disableAutoClose: confirmModalState.disableAutoClose,
	};
};

const mapDispatchToProps = {
	closeModal: hideConfirmModal,
};

export default connect(mapStateToProps, mapDispatchToProps)(ConfirmModal);
