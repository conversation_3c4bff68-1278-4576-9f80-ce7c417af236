:global(.Polaris-Modal-Dialog__Modal):has(.connectStoreModalContainer) {
	max-width: 600px;
	:global(.Polaris-Modal-CloseButton) {
		--p-icon: #8c9196;
		margin: 10px 2px 0 0;
		:global(.Polaris-Icon) {
			width: 16px;
			height: 16px;
		}
	}
}

.connectStoreModalContainer {
	position: relative;
	:global(.Polaris-Modal-Section) {
		padding: 32px;
	}
	.modalTitle {
		font-size: 26px;
		text-align: center;
		margin-bottom: 32px;
	}
	.buttonGroup {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		gap: 16px;
		:global(.Polaris-Button) {
			width: 168px;
			padding: 20px;
			:global(.Polaris-Button__Text) {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				gap: 8px;
			}
		}
	}
	.otherPlatform {
		text-align: center;
		margin-top: 16px;
	}
}
