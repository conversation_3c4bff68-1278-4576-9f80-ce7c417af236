import {<PERSON><PERSON>, <PERSON>, Modal} from '@shopify/polaris';
import React from 'react';
import {useTranslation} from 'react-i18next';

import {StoreIcon} from 'components/StoreIcon';
import {CONNECT_STORE_MODAL} from 'constants/Modals';
import {useModal} from 'hooks';

import styles from './index.module.scss';

const platforms = [
	{
		key: 'shopify',
		name: 'Shopify',
		url: 'https://www.aftership.com/integrations/shopify',
	},
	{
		key: 'bigcommerce',
		name: 'BigCommerce',
		url: 'https://www.aftership.com/integrations/bigcommerce',
	},
	{
		key: 'woocommerce',
		name: 'WooCommerce',
		url: 'https://www.aftership.com/integrations/woocommerce',
	},
	{
		key: 'magento-2',
		name: 'Magento 2',
		url: 'https://www.aftership.com/integrations/magento-2',
	},
	{
		key: 'wix',
		name: 'Wix',
		url: 'https://www.aftership.com/integrations/wix',
	},
	{
		key: 'sfcc',
		name: 'Salesforce',
		url: 'https://www.aftership.com/integrations/sfcc',
	},
];

export function ConnectStoreModal() {
	const {t} = useTranslation();
	const {opened, close} = useModal(CONNECT_STORE_MODAL);

	return (
		<Modal
			titleHidden
			title=""
			open={opened}
			onClose={() => {
				close();
			}}
		>
			<div className={styles.connectStoreModalContainer}>
				<Modal.Section>
					<div className={styles.modalTitle}>
						{t('SELECTYOUR_7cd64')}
					</div>
					<div
						style={{
							display: 'flex',
							justifyContent: 'space-between',
							flexWrap: 'wrap',
						}}
						className={styles.buttonGroup}
					>
						{platforms.map(platform => {
							return (
								<Button
									key={platform.key}
									onClick={() => {
										window.open(platform.url, '_blank');
									}}
								>
									{
										(
											<>
												<StoreIcon
													platform={platform.key}
													width="40px"
													height="40px"
												/>
												<div>{platform.name}</div>
											</>
										) as unknown as string
									}
								</Button>
							);
						})}
					</div>
					<div className={styles.otherPlatform}>
						<Link
							external
							url="https://www.aftership.com/integrations/tracking"
						>
							{t('OTHERCUSTO_6b32e')}
						</Link>
					</div>
				</Modal.Section>
			</div>
		</Modal>
	);
}
