import {Popover, OptionList, Button, Icon, Stack} from '@shopify/polaris';
import {CaretDownMinor} from '@shopify/polaris-icons';
import React, {useState} from 'react';

import {Operator} from 'pages/Shipments/interfaces/Filter';
import {gaClick} from 'utils/gtag';
import isCompany from 'utils/isCompany';

export const OperatorSelector = ({
	selected,
	options,
	onChange,
}: {
	selected: Operator[];
	options: {label: string; value: Operator}[];
	onChange: (selected: Operator[]) => void;
}) => {
	const [active, setActive] = useState(false);
	const operator =
		options.find(option => option.value === selected[0])?.label ||
		options[0]?.label;
	return (
		<Popover
			active={active}
			activator={
				<Button plain onClick={() => setActive(!active)}>
					{
						(
							<Stack spacing="none" alignment="center">
								<div>{operator.replace(' X days', '')}</div>
								<Icon source={CaretDownMinor} />
							</Stack>
						) as unknown as string
					}
				</Button>
			}
			onClose={() => setActive(false)}
		>
			<div
				role="button"
				tabIndex={0}
				onClick={e => e.stopPropagation()}
				style={{minWidth: 100}}
			>
				<OptionList
					selected={selected}
					options={options}
					onChange={(value: Operator[]) => {
						onChange(value);
						setActive(false);
						!isCompany() &&
							gaClick('E10232', {
								extraParams: {
									operator: value[0],
								},
							});
					}}
				/>
			</div>
		</Popover>
	);
};
