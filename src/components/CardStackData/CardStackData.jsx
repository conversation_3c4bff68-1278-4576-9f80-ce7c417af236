/* eslint-disable react/no-array-index-key */
/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import {Stack, SkeletonBodyText} from '@shopify/polaris';
import {SHIPMENTS_REPORT} from 'constants/billings/features';
import {useIsAvailableFeature} from 'hooks/billings';

CardStackData.propTypes = {
	isLoading: PropTypes.bool,
	title: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
	values: PropTypes.arrayOf(
		PropTypes.oneOfType([PropTypes.number, PropTypes.string])
	),
};

CardStackData.defaultProps = {
	isLoading: false,
	title: '',
	values: ['-'],
};

function CardStackData(props) {
	const showSkeletonTitle = props.isLoading && !props.title;
	const Skeleton = () => (
		<div style={{width: 60}}>
			<SkeletonBodyText lines={1} />
		</div>
	);
	return (
		<Stack alignment="center" distribution="equalSpacing" wrap={false}>
			<Stack.Item fill>
				{showSkeletonTitle ? <Skeleton /> : <p>{props.title}</p>}
			</Stack.Item>
			{props.isLoading ? (
				<Skeleton />
			) : (
				props.values.map((value, index) => {
					return (
						<Stack.Item key={index}>
							<p
								style={{
									minWidth: '55px',
									textAlign: 'right',
									marginLeft: '1.6rem',
								}}
							>
								{value}
							</p>
						</Stack.Item>
					);
				})
			)}
		</Stack>
	);
}

export const ShipmentCardStackData = props => {
	const [hasCode] = useIsAvailableFeature(SHIPMENTS_REPORT);
	const title = props.hideTitle ? '-' : props.title;
	return !hasCode ? (
		<CardStackData
			{...props}
			values={props.values ? props.values.map(() => '-') : []}
			title={title}
		/>
	) : (
		<CardStackData {...props} />
	);
};

export default CardStackData;
