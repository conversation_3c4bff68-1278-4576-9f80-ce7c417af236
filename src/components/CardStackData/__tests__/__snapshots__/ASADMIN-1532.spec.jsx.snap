// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`render <CardStackData />. 1`] = `
<Memo(Stack)
  alignment="center"
  distribution="equalSpacing"
  wrap={false}
>
  <Item$2
    fill={true}
  >
    <p />
  </Item$2>
  <Item$2
    key="0"
  >
    <p
      style={
        Object {
          "marginLeft": "1.6rem",
          "minWidth": "55px",
          "textAlign": "right",
        }
      }
    >
      0
    </p>
  </Item$2>
  <Item$2
    key="1"
  >
    <p
      style={
        Object {
          "marginLeft": "1.6rem",
          "minWidth": "55px",
          "textAlign": "right",
        }
      }
    >
      0
    </p>
  </Item$2>
</Memo(Stack)>
`;
