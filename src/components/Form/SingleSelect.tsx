import {Autocomplete, Icon, TextContainer} from '@shopify/polaris';
import {SearchMinor} from '@shopify/polaris-icons';
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';

export interface SingleSelectOption {
	value: string;
	label: string;
	disabled?: boolean;
	prefix?: React.ReactNode;
}
interface SingleSelectProps {
	selected: string;
	options?: SingleSelectOption[];
	maxTagCount?: number;
	onChange(value: string): void;
	isLoading?: boolean;
	placeholder?: string;
}

export function SingleSelect({
	options,
	maxTagCount = 20,
	onChange,
	selected,
	isLoading,
	placeholder,
}: SingleSelectProps) {
	const {t} = useTranslation();
	const [keyword, setKeyword] = useState('');

	const ref = useRef({
		keyword,
		selected,
		onInput: (value: string) => {
			setKeyword(value);
			onChange('');
		},
		onBlur: () => {
			setTimeout(() => {
				const {keyword, selected} = ref.current;
				if (keyword !== selected) {
					setKeyword('');
					onChange('');
				}
			}, 100);
		},
		onSelect: (value: string[]) => {
			onChange(value[0]);
			setKeyword(value[0]);
		},
	});

	ref.current.keyword = keyword;
	ref.current.selected = selected;

	const {matchOptions, singleSelected} = useMemo(() => {
		const tKeyword = keyword.trim().toLowerCase();
		return {
			matchOptions:
				options
					?.filter(({label}) =>
						label.toLowerCase().startsWith(tKeyword)
					)
					.slice(0, maxTagCount)
					.map(option => ({
						...option,
						active: option.label === selected,
					})) || [],
			singleSelected: [selected],
		};
	}, [keyword, maxTagCount, options, selected]);

	const {onInput, onBlur, onSelect} = useMemo(
		() => ({
			onInput: (value: string) => ref.current.onInput(value),
			onBlur: () => ref.current.onBlur(),
			onSelect: (value: string[]) => ref.current.onSelect(value),
		}),
		[]
	);

	useEffect(() => {
		if (ref.current.keyword !== selected) {
			setKeyword(selected);
		}
	}, [selected]);
	return (
		<Autocomplete
			options={matchOptions}
			selected={singleSelected}
			onSelect={onSelect}
			textField={
				<Autocomplete.TextField
					label=""
					value={keyword}
					onChange={onInput}
					onBlur={onBlur}
					prefix={<Icon source={SearchMinor} />}
					placeholder={placeholder}
					autoFocus
				/>
			}
			emptyState={
				<React.Fragment>
					<div style={{textAlign: 'left', padding: '1rem 0'}}>
						<TextContainer>{t('NO_RESULTS_jf1la')}</TextContainer>
					</div>
				</React.Fragment>
			}
			loading={isLoading}
			preferredPosition="below"
			allowMultiple={false}
		/>
	);
}
