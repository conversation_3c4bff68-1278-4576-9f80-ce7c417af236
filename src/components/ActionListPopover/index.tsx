import {ActionList, ActionListItemDescriptor, Popover} from '@shopify/polaris';
import React, {useState} from 'react';

export const ActionListPopover = ({
	activator,
	items,
	preferredAlignment,
	preferredPosition,
}: {
	activator: React.ReactNode;
	items: ActionListItemDescriptor[];
	preferredAlignment?: 'left' | 'center' | 'right';
	preferredPosition?: 'above' | 'below' | 'mostSpace';
}) => {
	const [popoverActive, setPopoverActive] = useState(false);

	return (
		<Popover
			active={popoverActive}
			activator={
				<div
					role="button"
					tabIndex={0}
					onClick={() => setPopoverActive(!popoverActive)}
					style={{cursor: 'pointer'}}
				>
					{activator}
				</div>
			}
			onClose={() => {
				setPopoverActive(false);
			}}
			preferredAlignment={preferredAlignment || 'center'}
			preferredPosition={preferredPosition || 'below'}
		>
			<ActionList
				items={items.map(item => ({
					...item,
					onAction: () => {
						item?.onAction?.();
						setPopoverActive(false);
					},
				}))}
			/>
		</Popover>
	);
};
