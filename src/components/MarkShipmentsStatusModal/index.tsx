import {
	Card,
	Checkbox,
	DatePicker,
	Icon,
	Modal,
	Popover,
	Stack,
	TextField,
	TextStyle,
} from '@shopify/polaris';
import {CalendarMinor, ClockMajor} from '@shopify/polaris-icons';
import moment from 'moment-timezone';
import React, {useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';

import {
	LostReason,
	Tracking,
	useTrackingDetailUpdateByReasonMutation,
} from '@graphql/generated';
import {removeBanner} from 'actions/banners';
import {addToast} from 'actions/toast';
import {MODAL_BANNER} from 'constants/BannerNames';
import {DATE_WITH_DASH} from 'constants/Date';
import {useHandleError} from 'hooks/useHandleError';
import {Select} from 'pages/TrackingPageEditor/components/FastSelectField';
import {ModalBanner} from 'pages/components/Banner';

interface Props {
	isOpen: boolean;
	setIsOpen: (val: boolean) => void;
	markReason: LostReason;
	trackingNumber?: string;
	trackingId?: string;
	onSuccess?: () => void;
	isMultiple?: boolean;
	actionListTrackingUpdateArgs?: {
		param: any;
		onTrackingsUpdate: any;
		loading: boolean;
	};
	selectedItemsLength?: string;
	trackingDetail?: Tracking;
}
export const MarkShipmentsStatusModal = ({
	isOpen,
	setIsOpen,
	markReason,
	trackingNumber,
	trackingId = '',
	onSuccess,
	isMultiple = false,
	selectedItemsLength = '0',
	actionListTrackingUpdateArgs,
	trackingDetail,
}: Props) => {
	const {t} = useTranslation();

	const dispatch = useDispatch();
	const {addErrorBanner} = useHandleError();
	const [deliveryTime, setDeliveryTime] = useState('notSpecified');
	const [hasDeliveryTimeStamp, setHasDeliveryTimeStamp] = useState(false);
	const [isShowDatePicker, setIsShowDatePicker] = useState(false);

	const [selectedDate, setSelectedDate] = useState(new Date());

	const [{month, year}, setDate] = useState({
		month: selectedDate.getMonth(),
		year: selectedDate.getFullYear(),
	});

	const isEdit = isMultiple
		? false
		: trackingDetail?.userMarkedReason === markReason;
	const eventTime = trackingDetail?.userMarkedAsCompletedEventTime;

	useEffect(() => {
		if (isOpen && isEdit) {
			const userMarkedAsCompletedEventTime =
				Boolean(eventTime?.date) && Boolean(eventTime?.time)
					? `${moment(eventTime?.date).format(
							DATE_WITH_DASH
					  )}T${moment(eventTime?.time, 'h:mma').format('HH:mm:ss')}`
					: null;

			if (userMarkedAsCompletedEventTime) {
				setDeliveryTime(
					eventTime?.time === '23:59:59'
						? 'notSpecified'
						: moment(
								userMarkedAsCompletedEventTime?.slice(0, 19)
						  ).format('h:mm a')
				);
				setHasDeliveryTimeStamp(
					Boolean(userMarkedAsCompletedEventTime)
				);
				setSelectedDate(
					new Date(
						moment(
							userMarkedAsCompletedEventTime?.slice(0, 19)
						).format(DATE_WITH_DASH)
					)
				);
			}
		} else {
			setDeliveryTime('notSpecified');
			setHasDeliveryTimeStamp(false);
			setSelectedDate(new Date());
		}
	}, [isOpen, isEdit, eventTime?.date, eventTime?.time]);

	const handleMonthChange = (month: number, year: number) => {
		setDate({month, year});
	};
	const handleDateSelection = ({end}: {end: Date}) => {
		setSelectedDate(end);
		setIsShowDatePicker(false);
	};
	useEffect(() => {
		if (selectedDate) {
			setDate({
				month: selectedDate.getMonth(),
				year: selectedDate.getFullYear(),
			});
		}
	}, [selectedDate]);
	const {mutate: markMutate} = useTrackingDetailUpdateByReasonMutation({
		onError: error => {
			addErrorBanner(MODAL_BANNER, error);
		},
	});
	const actionMessage = useMemo(
		() => markReason.replace(/_/g, ' ').toLowerCase(),
		[markReason]
	);

	const onClose = () => {
		setIsOpen(false);
		dispatch(removeBanner(MODAL_BANNER));
	};

	const modalTitleAndDescriptionKeyMap = {
		DELIVERED: {
			title: isEdit ? t('EDIT_DATE_40481') : t('EDITDELIVE_9883d'),
			description: isMultiple
				? t('THISACTION_81bbf')
				: t('THISACTION_279f1'),
			button: t('MARKASDELI_4389c'),
			multipleTitle: t(
				selectedItemsLength === '1'
					? 'MARKXSHIPM_97778'
					: 'MARKXSHIPM_7edcc',
				{
					x: selectedItemsLength,
				}
			),
			checkboxLabel: t('ADD_DATE_A_e02ad'),
			dateTimeDescription: t('ENTERTHEDA_7bb62'),
		},
		LOST: {
			title: isEdit ? t('EDIT_DATE_961b2') : t('MARKSHIPME_c6498'),
			description: isMultiple
				? t('MARK_AFTER_d2760')
				: t('MARK_AFTER_4ff6f'),
			button: t('MARKASLOST_5a8d6'),
			multipleTitle: t(
				selectedItemsLength === '1'
					? 'MARKXSHIPM_cc326'
					: 'MARKXSHIPM_89d2c',
				{
					x: selectedItemsLength,
				}
			),
			checkboxLabel: t('ADD_DATE_A_010c0'),
			dateTimeDescription: t('SELECT_THE_11dc3'),
		},
		RETURNED_TO_SENDER: {
			title: isEdit ? t('EDIT_DATE_ca895') : t('MARKSHIPME_69574'),
			description: isMultiple
				? t('MARK_AFTER_23fa6')
				: t('MARK_AFTER_00d1b'),

			button: t('MARK_AS_RE_4a697'),
			multipleTitle: t(
				selectedItemsLength === '1'
					? 'MARKXSHIPM_579ef'
					: 'MARKXSHIPM_fb56b',
				{
					x: selectedItemsLength,
				}
			),
			checkboxLabel: t('ADD_DATE_A_39699'),
			dateTimeDescription: t('SELECT_THE_38b12'),
		},
	};

	const getTimeOptions = () => {
		const periods = ['am', 'pm'];
		const hours = new Array(12).fill(null).map((_, i) => i);
		const minutes = ['00', '30'];

		const timeArray = periods.flatMap(period => {
			return hours.flatMap(hour => {
				const hour12 = hour % 12 || 12;
				return minutes.map(minute => {
					return {
						label:
							hour12 === 12 && minute === '00' && period === 'am'
								? '12:00 am (start of day)'
								: `${hour12}:${minute} ${period}`,
						value: `${hour12}:${minute} ${period}`,
					};
				});
			});
		});

		return [{label: 'Not specified', value: 'notSpecified'}, ...timeArray];
	};

	return (
		<Modal
			open={isOpen}
			title={
				isMultiple
					? modalTitleAndDescriptionKeyMap?.[markReason]
							?.multipleTitle || ''
					: modalTitleAndDescriptionKeyMap?.[markReason]?.title || ''
			}
			onClose={onClose}
			primaryAction={{
				content: isEdit
					? 'Save'
					: modalTitleAndDescriptionKeyMap?.[markReason].button,
				loading: isMultiple
					? actionListTrackingUpdateArgs?.loading
					: undefined,
				onAction: () => {
					if (isMultiple && markReason) {
						actionListTrackingUpdateArgs?.onTrackingsUpdate(
							{
								input: {
									...actionListTrackingUpdateArgs?.param,
									updatedData: {
										userMarkedAsCompleted: markReason,
										userMarkedAsCompletedEventTime:
											hasDeliveryTimeStamp
												? `${moment(
														selectedDate
												  ).format(DATE_WITH_DASH)}T${
														deliveryTime ===
														'notSpecified'
															? '23:59:59'
															: moment(
																	deliveryTime,
																	'h:mma'
															  ).format(
																	'HH:mm:ss'
															  )
												  }`
												: null,
									},
								},
							},
							{
								onSuccess: () => {
									onClose();
								},
								onError: () => {
									onClose();
								},
							}
						);
					} else if (trackingId && markReason) {
						markMutate(
							{
								id: trackingId,
								reason: markReason,
								userMarkedAsCompletedEventTime:
									hasDeliveryTimeStamp
										? {
												date: moment(
													selectedDate
												).format(DATE_WITH_DASH),
												time:
													deliveryTime ===
													'notSpecified'
														? '23:59:59'
														: moment(
																deliveryTime,
																'h:mma'
														  ).format('HH:mm:ss'),
										  }
										: null,
							},
							{
								onSuccess: () => {
									dispatch(
										addToast({
											message: `${t('SHIPMENT_5254d', {
												trackingNumber,
												actionMessage,
											})}`,
										})
									);

									onSuccess && onSuccess();

									onClose();
								},
							}
						);
					}
				},
			}}
			secondaryActions={[
				{
					content: t('CANCEL_660bc'),
					onAction: onClose,
				},
			]}
		>
			<Modal.Section>
				<ModalBanner hasMarginBottom />
				<div style={{marginBottom: '16px'}}>
					{modalTitleAndDescriptionKeyMap?.[markReason].description}
				</div>
				<>
					<div
						style={{
							marginBottom: hasDeliveryTimeStamp ? '16px' : '0',
						}}
					>
						<Checkbox
							label={
								modalTitleAndDescriptionKeyMap?.[markReason]
									?.checkboxLabel
							}
							checked={hasDeliveryTimeStamp}
							onChange={setHasDeliveryTimeStamp}
						/>
					</div>

					{hasDeliveryTimeStamp ? (
						<>
							<div style={{marginBottom: '16px'}}>
								<Stack distribution="fillEvenly">
									<Stack.Item>
										<Popover
											active={isShowDatePicker}
											autofocusTarget="none"
											preferredAlignment="left"
											fullWidth
											preferInputActivator={false}
											preferredPosition="below"
											onClose={() => {
												setIsShowDatePicker(false);
											}}
											fixed
											activator={
												<TextField
													role="combobox"
													label="Date"
													prefix={
														<Icon
															source={
																CalendarMinor
															}
														/>
													}
													value={moment(
														selectedDate
													).format(DATE_WITH_DASH)}
													onFocus={() =>
														setIsShowDatePicker(
															true
														)
													}
													onChange={() => {}}
													autoComplete="off"
												/>
											}
										>
											<Card>
												<Card.Section>
													<DatePicker
														month={month}
														year={year}
														selected={selectedDate}
														onMonthChange={
															handleMonthChange
														}
														onChange={
															handleDateSelection
														}
													/>
												</Card.Section>
											</Card>
										</Popover>
									</Stack.Item>
									<Stack.Item>
										<Select
											label="Time"
											options={getTimeOptions()}
											value={deliveryTime}
											onChange={setDeliveryTime}
											helpText=""
											icon={
												<Icon
													source={ClockMajor}
													color="subdued"
												/>
											}
										/>
									</Stack.Item>
								</Stack>
							</div>
							<div>
								<TextStyle variation="subdued">
									{modalTitleAndDescriptionKeyMap?.[
										markReason
									]?.dateTimeDescription || ''}
								</TextStyle>
							</div>
						</>
					) : null}
				</>
			</Modal.Section>
		</Modal>
	);
};
