import {Icon, Stack} from '@shopify/polaris';
import {LockMinor} from '@shopify/polaris-icons';
import React from 'react';

import ModalWithoutCloseButton from 'components/ModalWithoutCloseButton';

import styles from './LockModal.module.scss';

interface Props {
	open: boolean;
	title: string;
}
const LockModal: React.FC<Props> = props => {
	return (
		<ModalWithoutCloseButton
			title=""
			open={props.open}
			onClose={() => {}}
			titleHidden
			sectioned
			extraSmall
		>
			<div className={styles.modal}>
				<Stack vertical spacing="loose">
					<Stack.Item>
						<div className={styles.icon}>
							<Icon source={LockMinor} color="primary" />
						</div>
					</Stack.Item>
					<Stack.Item>
						<h1
							style={{
								fontSize: 26,
								fontWeight: 600,
								lineHeight: '32px',
							}}
						>
							{props.title}
						</h1>
					</Stack.Item>
					<Stack.Item>{props.children}</Stack.Item>
				</Stack>
			</div>
		</ModalWithoutCloseButton>
	);
};

export default LockModal;
