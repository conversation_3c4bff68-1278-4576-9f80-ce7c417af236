import {BadgeProps, Icon, IconProps} from '@shopify/polaris';
import {LockMinor} from '@shopify/polaris-icons';
import {isNull} from 'lodash';
import React from 'react';

import type {FeatureValidator} from 'hooks/billings';

import BaseBadge from './BaseBadge';
import usePlanConfig from './usePlanConfig';

interface Props {
	isExtraPermission?: boolean; // 更高优先级, 临时处理，给 on-time report 这种有额外权限来源的 feature 使用
	featureCode: FeatureValidator;
	size?: BadgeProps['size'];
	color?: IconProps['color'];
	onlyIcon?: boolean;
	onClickUpgrade?: () => void;
}

export default function FeatureStatusBadge(props: Props) {
	const {
		featureCode,
		color,
		isExtraPermission,
		onClickUpgrade,
		size,
		onlyIcon,
	} = props;
	const config = usePlanConfig({
		featureCode,
		size,
		onClickUpgrade,
		isExtraPermission,
	});

	if (isNull(config)) {
		return null;
	}

	if (config) {
		return (
			<BaseBadge
				hideBadgeLockIcon={config.hideBadgeLockIcon}
				badgeText={config.badgeText}
				badgeSize={config.badgeSize}
				popoverText={config.popoverText}
				buttonText={config.buttonText}
				onButtonClick={config.onButtonClick}
				onlyIcon={onlyIcon}
			/>
		);
	}

	return <Icon source={LockMinor} color={color || 'base'} />;
}
