import {Badge, BadgeProps, Icon, Popover, Button} from '@shopify/polaris';
import {LockMinor} from '@shopify/polaris-icons';
import React, {useState} from 'react';
import type {FC} from 'react';

import {PopoverContentBox} from './styles';

export interface PopoverContentProps {
	onMouseEnter?: () => void;
	onMouseLeave?: () => void;
	onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
	buttonText: string;
	onButtonClick?: () => void;
	popoverText: React.ReactNode | string;
	className?: string;
}

export const PopoverContent: FC<PopoverContentProps> = props => {
	const {
		onMouseEnter,
		onMouseLeave,
		onClick,
		buttonText,
		onButtonClick,
		popoverText,
		className,
	} = props;

	return (
		<PopoverContentBox
			onMouseEnter={onMouseEnter}
			onMouseLeave={onMouseLeave}
			onClick={onClick}
			role="button"
			tabIndex={-1}
			className={className}
		>
			<div>
				<span>{popoverText}</span>
			</div>
			<div style={{marginTop: '8px'}}>
				<Button primary onClick={onButtonClick}>
					{buttonText}
				</Button>
			</div>
		</PopoverContentBox>
	);
};

export interface BaseBadgeProps {
	badgeSize?: BadgeProps['size'];
	badgeText: React.ReactNode;
	hideBadgeLockIcon?: boolean;
	popoverText: React.ReactNode;
	buttonText: string;
	onlyIcon?: boolean;
	onButtonClick: () => void;
}

const BaseBadge: FC<BaseBadgeProps> = props => {
	const {
		badgeSize,
		badgeText,
		hideBadgeLockIcon,
		popoverText,
		buttonText,
		onButtonClick,
		onlyIcon,
	} = props;

	const [hoverBadge, setHoverBadge] = useState(false);
	const [hoverPopover, setHoverPopover] = useState(false);

	const icon = (
		<>
			{!hideBadgeLockIcon && (
				<div style={{width: 16, height: 16}}>
					<Icon source={LockMinor} color="base" />
				</div>
			)}
		</>
	);

	return (
		<Popover
			active={hoverPopover || hoverBadge}
			activator={
				<div
					onMouseEnter={() => setHoverPopover(true)}
					onMouseLeave={() => setHoverPopover(false)}
					style={{
						cursor: 'pointer',
						height: '20px',
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						position: 'relative',
					}}
				>
					<div
						style={{
							position: 'absolute',
							width: '100%',
							height: '30px',
							marginTop: '10px',
						}}
					/>

					{onlyIcon ? (
						icon
					) : (
						<Badge status="info" size={badgeSize}>
							{
								(
									<div
										style={{
											display: 'flex',
											alignItems: 'center',
											gap: 4,
										}}
									>
										{icon}
										<span
											style={{
												fontSize: '11px',
											}}
										>
											{badgeText}
										</span>
									</div>
								) as unknown as string
							}
						</Badge>
					)}
				</div>
			}
			onClose={() => setHoverPopover(false)}
		>
			<PopoverContent
				onMouseEnter={() => setHoverBadge(true)}
				onMouseLeave={() => setHoverBadge(false)}
				onClick={e => e.stopPropagation()}
				buttonText={buttonText}
				onButtonClick={onButtonClick}
				popoverText={popoverText}
			/>
		</Popover>
	);
};

export default BaseBadge;
