import {useGetPlanTrial, PlanGroupLevelEnum} from 'aftershipBillingUi';
import {BadgeProps} from '@shopify/polaris';
import {useTranslation} from 'react-i18next';

import {planGroupMapping} from 'constants/billings/billingConfig';
import {
	TrialingFeature,
	useFeatures,
	useFeatureValidator,
	useTargetUpgradePlan,
} from 'hooks/billings';
import type {FeatureValidator} from 'hooks/billings';
import useBillingSubscribeActions from 'hooks/billings/useBillingSubscribeActions';
import {useIsEnterprise} from 'hooks/billings/useChoosePlan';
import {usePlanTrialAvailable} from 'hooks/billings/usePlanTrialAvailable';
import {useTrialSubscription} from 'hooks/billings/useSubscription';
import {toOrgMoment} from 'utils/day';
import isCompany from 'utils/isCompany';

export interface ParamsType {
	featureCode: FeatureValidator;
	isExtraPermission?: boolean; // 更高优先级, 临时处理，给 on-time report 这种有额外权限来源的 feature 使用
	size?: BadgeProps['size'];
	onClickUpgrade?: () => void;
}

/**
 *
 * @description 用于获取 plan config
 */
const usePlanConfig = (params: ParamsType) => {
	const {featureCode, size, onClickUpgrade, isExtraPermission} = params;

	const {t} = useTranslation();
	const isEnterprise = useIsEnterprise();

	const {unlockFeature} = useBillingSubscribeActions();
	const [{available, isFeatureTrial, isPlanTrial, availableFeature}] =
		useFeatures(featureCode);
	const [action] = useFeatureValidator(
		featureCode,
		undefined,
		undefined,
		true
	);
	const trialFeatureLeftDays =
		isFeatureTrial &&
		toOrgMoment((availableFeature as TrialingFeature).endAt).diff(
			toOrgMoment(),
			'days'
		);

	const canUsePlanTrial = usePlanTrialAvailable();
	const {activePlanTrial, planTrials} = useGetPlanTrial();
	const planTrialLeftDays = activePlanTrial
		? Math.round(
				toOrgMoment(activePlanTrial.endAt).diff(
					toOrgMoment(),
					'days',
					true
				)
		  )
		: 0;
	const isAutoUpgradeDisable =
		activePlanTrial && !activePlanTrial.autoUpgrade;

	const lowestLevelPlan = useTargetUpgradePlan(featureCode);
	const planGroupLevel = String(
		lowestLevelPlan?.group.level || '10000'
	) as keyof typeof planGroupMapping;

	const {subscription: trialSubscription} = useTrialSubscription();
	const isFreeTrialPlan = Boolean(trialSubscription);
	const freeTrialPlanLeftDays = isFreeTrialPlan
		? Math.round(
				toOrgMoment(trialSubscription?.currentPeriod.endAt).diff(
					toOrgMoment(),
					'days',
					true
				)
		  )
		: 0;

	// Company console
	if (isCompany()) {
		return null;
	}

	// feature available === true
	if (available) {
		// normal plan && retaining features
		if (!isFreeTrialPlan && !isPlanTrial && !isFeatureTrial) {
			return null;
		}
		// free trial plan
		if (isFreeTrialPlan) {
			return freeTrialPlanLeftDays > 0
				? {
						badgeText: planGroupMapping[planGroupLevel],
						badgeSize: size,
						popoverText: t('YOU_ONLY_ja81b', {
							count: freeTrialPlanLeftDays,
							level: planGroupMapping[planGroupLevel],
						}),
						hideBadgeLockIcon: true,
						buttonText: 'Upgrade and unlock',
						onButtonClick: () => {
							unlockFeature({
								featureCode: availableFeature?.code || '',
								promotion: {
									id: availableFeature?.code || '',
								},
							});
						},
				  }
				: null;
		}

		// plan trial
		if (isPlanTrial) {
			return Number(planGroupLevel) >= 50000
				? {
						badgeText: planGroupMapping[planGroupLevel],
						badgeSize: size,
						popoverText: isAutoUpgradeDisable
							? t(
									'YOUONLYHAV_0430a',
									'⏰ You only have {{x}} days left to access this feature. Upgrade to Premium or higher now.',
									{x: planTrialLeftDays}
							  )
							: t(
									'XDAYSLEFTO_70a75',
									'⏰ {{x}} days left on your free trial.',
									{
										x: planTrialLeftDays,
									}
							  ),
						hideBadgeLockIcon: true,
						buttonText: 'Upgrade now',
						onButtonClick: () => {
							onClickUpgrade?.();
							unlockFeature({
								featureCode: availableFeature?.code || '',
								promotion: {
									id: availableFeature?.code || '',
								},
							});
						},
				  }
				: null;
		}
		// feature trial
		if (isFeatureTrial || isExtraPermission) {
			if (trialFeatureLeftDays && trialFeatureLeftDays < 365) {
				return trialFeatureLeftDays
					? {
							badgeText: planGroupMapping[planGroupLevel],
							badgeSize: size,
							popoverText: t('YOU_ONLY_ja81b', {
								count: trialFeatureLeftDays,
								level: planGroupMapping[planGroupLevel],
							}),
							buttonText: 'Upgrade and unlock',
							onButtonClick: () => {
								onClickUpgrade && onClickUpgrade();
								action();
							},
					  }
					: null;
			}
		} else {
			// feature trial >= 365d consider as normal plan
			return null;
		}
	}

	// feature available === false

	// 已经是 Enterprise Standard 的情况下，统一显示升级 Enterprise Advanced
	if (isEnterprise) {
		return {
			badgeText: t('ADVANCED_d0a68', 'Advanced'),
			badgeSize: size,
			popoverText: t('UPGRADE_YO_ae81e'),
			buttonText: t('CONTACT_SA_799c9'),
			onButtonClick: () => {
				onClickUpgrade?.();
				action();
			},
		};
	}

	// haven‘t create plan trial & not use shopify payment
	if (
		(planTrials || []).length <= 0 &&
		canUsePlanTrial &&
		Number(planGroupLevel) <= PlanGroupLevelEnum.Premium
	) {
		return {
			badgeText: 'Try for free',
			badgeSize: size,
			popoverText: t(
				'GETACCESST_f17ed',
				'Get access to all Premium features free for {{x}} days.',
				{x: 7}
			),
			buttonText: t('STARTFREET_32c5e', 'Start free trial'),
			onButtonClick: () => {
				onClickUpgrade?.();
				action();
			},
		};
	}

	// feature required plan level greater than Enterprise
	if (Number(planGroupLevel) >= 100000) {
		return {
			badgeText: planGroupMapping[planGroupLevel],
			badgeSize: size,
			popoverText: t('UPGRADE_YO_6228e'),
			buttonText: 'Upgrade and unlock',
			onButtonClick: () => {
				onClickUpgrade && onClickUpgrade();
				action();
			},
		};
	}

	// feature required plan level greater than Premium
	if (Number(planGroupLevel) >= 50000) {
		return {
			badgeText: planGroupMapping[planGroupLevel],
			badgeSize: size,
			popoverText: t('UPGRADE_jv81v'),
			buttonText: 'Upgrade and unlock',
			onButtonClick: () => {
				onClickUpgrade && onClickUpgrade();
				action();
			},
		};
	}

	// feature required plan level greater than Pro
	if (Number(planGroupLevel) >= 40000) {
		return {
			badgeText: planGroupMapping[planGroupLevel],
			badgeSize: size,
			popoverText: t('UPGRADE_YO_7f185'),
			buttonText: 'Upgrade and unlock',
			onButtonClick: () => {
				onClickUpgrade && onClickUpgrade();
				action();
			},
		};
	}

	// feature required plan level greater than Essentials should output badge, otherwise only output lock icon
	if (Number(planGroupLevel) >= 30000) {
		return {
			badgeText: planGroupMapping[planGroupLevel],
			badgeSize: size,
			popoverText: t('UPGRADE_YO_3baa9'),
			buttonText: 'Upgrade and unlock',
			onButtonClick: () => {
				onClickUpgrade && onClickUpgrade();
				action();
			},
		};
	}

	// Returning undefined indicates no plan rules match
	return undefined;
};

export default usePlanConfig;
