import {
	Stack,
	TextStyle,
	DisplayText,
	SkeletonDisplayText,
	SkeletonBodyText,
} from '@shopify/polaris';
import React from 'react';

export interface AnalyticsCardProps {
	data: {title: React.ReactNode; desc: React.ReactNode}[];
	loading?: boolean;
}
export default function AnalyticsCard({
	data,
	loading,
}: AnalyticsCardProps): JSX.Element {
	return (
		<div
			style={{
				background: 'var(--p-surface)',
				borderRadius: 'var(--p-border-radius-wide, 3px)',
				boxShadow:
					'var(--p-card-shadow, 0 0 0 1px rgba(63, 63, 68, 0.05), 0 1px 3px 0 rgba(63, 63, 68, 0.15))',
				display: 'flex',
				height: 84,
				textAlign: 'center',
			}}
		>
			{data.map((item, index) => {
				return (
					<div
						key={String(index)}
						style={{
							borderRight:
								index === data.length - 1
									? undefined
									: '1px solid #dfe3e8',
							flex: 1,
							paddingTop: 16,
						}}
					>
						{loading ? (
							<Stack
								vertical
								spacing="tight"
								alignment="center"
								distribution="center"
							>
								<div
									style={{
										width: '12rem',
									}}
								>
									<SkeletonDisplayText />
								</div>
								<div
									style={{
										width: '20rem',
									}}
								>
									<SkeletonBodyText lines={1} />
								</div>
							</Stack>
						) : (
							<Stack
								vertical
								spacing="none"
								alignment="center"
								distribution="center"
							>
								<DisplayText>
									<span style={{fontWeight: 600}}>
										{item.title}
									</span>
								</DisplayText>
								<TextStyle variation="subdued">
									{item.desc}
								</TextStyle>
							</Stack>
						)}
					</div>
				);
			})}
		</div>
	);
}
