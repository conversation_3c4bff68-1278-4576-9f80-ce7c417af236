import {capture} from '@aftership/datacat';
import {Button, Card, Icon, Layout, Link, TextStyle} from '@shopify/polaris';
import {LockMinor, ConfettiMajor} from '@shopify/polaris-icons';
import {
	useGetPlanTrial,
	usePlanTrialUnlockModal,
	PlanGroupLevelEnum,
	IPlan,
	useActiveSubscription,
	useBillingConfig,
	useIsPayWithShopify,
} from 'aftershipBillingUi';
import React, {FC, useEffect} from 'react';
import {Trans, useTranslation} from 'react-i18next';
import {useHistory} from 'react-router';

import useBillingSubscribeActions from 'hooks/billings/useBillingSubscribeActions';
import {
	useIsEnterprise,
	useIsEnterpriseFeature,
} from 'hooks/billings/useChoosePlan';
import {usePlanTrialAvailable} from 'hooks/billings/usePlanTrialAvailable';
import {gaImpr, gaClick} from 'utils/gtag';

import './FeatureLockCard.scss';

interface IProps {
	plan?: IPlan;
	onClick?: () => void;
	coupon?: string;
	featureCode?: string;
}

const FeatureLockCard: FC<IProps> = ({
	plan,
	onClick = () => {},
	coupon = '',
	featureCode,
}) => {
	const {t} = useTranslation();
	const history = useHistory();
	const customDescription: Record<string, string> = {
		recommendation_aftership_smart: t('SUBSCRIBE_TO_1231'),
	};
	const isEnterprise = useIsEnterprise();

	const {open: openPlanTrialUnlockModal} = usePlanTrialUnlockModal({
		lowestUnlockPlanLevel: PlanGroupLevelEnum.Essentials,
	});

	const {isPayWithShopify} = useIsPayWithShopify();
	const canUsePlanTrial = usePlanTrialAvailable();
	const {subscription} = useActiveSubscription();
	const {planTrials} = useGetPlanTrial();
	const planCode = plan?.group?.level || 0;
	const planDiff = Math.abs(planCode - subscription.plan.group.level);
	const canCreatePlanTrial =
		(planTrials || []).length <= 0 && planDiff === 10000 && canUsePlanTrial;

	const {planGroups} = useBillingConfig();
	const planGroup = planGroups.filter(group =>
		canCreatePlanTrial
			? Number(group.groupLevel) >= 50000
			: Number(group.groupLevel) === plan?.group.level
	);
	const features = planGroup[0]?.description;

	const {unlockFeature} = useBillingSubscribeActions();
	// @ts-ignore
	const isEnterpriseFeature = useIsEnterpriseFeature(featureCode);

	useEffect(() => {
		capture('impression', {
			target: 'upgrade_modal',
			feature_code: featureCode,
		});
		if (canCreatePlanTrial) {
			gaImpr('E10228', {
				extraParams: {
					features: featureCode as string,
				},
			});
			capture('product_view', {
				'Promotion.id': featureCode,
				'Promotion.type': 'plan_trial',
				'Promotion.payment': isPayWithShopify ? 'shopify' : 'stripe',
			});
		} else {
			gaImpr('E10216', {
				extraParams: {
					features: featureCode as string,
				},
			});
		}
	}, []);

	const handleSubscribeClick = () => {
		if (isEnterprise) {
			history.push(
				`/pricing/tracking?promotion_type=upgrade&promotion_id=${featureCode}`
			);
		} else {
			unlockFeature({
				featureCode: featureCode || '',
				groupLevel: plan?.group?.level,
				promotion: {
					id: featureCode || '',
				},
				couponCode: coupon || '',
				plan: plan,
			});
		}

		onClick();

		capture('click', {
			target: 'upgrade_modal',
			feature_code: featureCode,
		});

		if (canCreatePlanTrial) {
			gaClick('E10229', {
				extraParams: {
					features: featureCode as string,
				},
			});
		} else {
			gaClick('E10217', {
				extraParams: {
					features: featureCode as string,
				},
			});
		}
	};
	const handleCreatePlanTrial = () => {
		onClick();

		// 临时，后面移交 Billing 支持
		openPlanTrialUnlockModal({
			promotion: {
				type: 'plan-trial',
				id: 'feature-lock-card',
				featureCode: featureCode || '',
			},
		});
	};

	let subtitle: React.ReactNode;
	if (customDescription[featureCode || '']) {
		subtitle = customDescription[featureCode || ''];
	} else if (canCreatePlanTrial) {
		subtitle = (
			<Trans
				i18nKey="GETFREEACC_5b6bc"
				defaults="Get free access to all the <lnk>Premium</lnk> plan features."
				components={{
					lnk: (
						// eslint-disable-next-line jsx-a11y/anchor-has-content
						<a
							href="/pricing/tracking?promotion_type=upgrade&promotion_id=premium-trial-compare&redirect=0"
							rel="noreferrer"
							style={{
								color: 'inherit',
							}}
						/>
					),
				}}
			/>
		);
	} else {
		subtitle = t('SUBSCRIBE_15706', {
			name: planGroup[0]?.name,
		});
	}

	// 临时，后面移交 Billing 支持
	if (isEnterprise) {
		subtitle = t('Contact sales now to access:');

		if (features?.featureHeader) {
			features.featureHeader = t('What’s in Enterprise Advanced:');
		}

		if (features?.featuresOverview) {
			features.featuresOverview = [
				{name: t('Advanced email triggers'), available: true},
				{
					name: t('AI Predictive estimated delivery dates'),
					available: true,
				},
				{name: t('Transit times report'), available: true},
				{
					name: t('Remove AfterShip branding'),
					available: true,
				},
				{name: t('On-time shipments report'), available: true},
				{
					name: t('Custom domain for tracking pages'),
					available: true,
				},
			];
		}
	}

	const upgradeText = isEnterpriseFeature
		? t('CONTACT_SA_799c9', 'Contact sales to unlock')
		: t('UPGRADE_AN_68826');

	return (
		<div>
			<Card>
				<div className="feature-lock-card">
					<Layout>
						<Layout.Section>
							<div className="feature-lock-icon">
								<Icon
									source={
										canCreatePlanTrial
											? ConfettiMajor
											: LockMinor
									}
									color="primary"
								/>
							</div>
						</Layout.Section>
						<Layout.Section>
							<div className="feature-lock-header">
								{canCreatePlanTrial
									? t(
											'ENJOY_PREMI_e57fa',
											'Enjoy Premium feature free trial'
									  )
									: t('UNLOCK_1bc26', {
											name: isEnterprise
												? t('Enterprise Advanced')
												: planGroup[0]?.name,
									  })}
							</div>
						</Layout.Section>
						<Layout.Section>
							<div style={{width: 325, margin: '0 auto'}}>
								{subtitle}
							</div>
						</Layout.Section>

						<div className="feature-lock-features__container">
							<div
								style={{
									fontSize: '14px',
									fontWeight: 600,
									marginBottom: '12px',
								}}
							>
								{features?.featureHeader}
							</div>
							<div
								style={{
									minWidth: '470px',
									display: 'flex',
									flexWrap: 'wrap',
								}}
							>
								{features?.featuresOverview.map(feature => {
									return (
										<div
											key={feature.name}
											style={{
												display: 'flex',
												width: '230px',
												minHeight: '32px',
												flexWrap: 'nowrap',
											}}
										>
											<div
												style={{
													marginRight: '4px',
													fontWeight: 700,
												}}
											>
												&#10003;
											</div>
											<div
												style={{
													fontSize: '12px',
													fontWeight: 400,
												}}
											>
												{feature.name}
											</div>
										</div>
									);
								})}
							</div>
						</div>

						<Layout.Section>
							<div>
								{isEnterprise ? (
									<Button
										primary
										onClick={() => {
											handleSubscribeClick();
										}}
									>
										{t('Contact sales')}
									</Button>
								) : (
									<Button
										primary
										onClick={
											canCreatePlanTrial
												? handleCreatePlanTrial
												: handleSubscribeClick
										}
									>
										{
											(
												<div className="feature-lock-btn-content">
													{!canCreatePlanTrial && (
														<Icon
															source={LockMinor}
															color="base"
														/>
													)}
													<p>
														{canCreatePlanTrial
															? t(
																	'STARTFREET_32c5e',
																	'Start free trial'
															  )
															: upgradeText}
													</p>
												</div>
											) as any
										}
									</Button>
								)}
							</div>
						</Layout.Section>
						{isEnterprise ? null : (
							<Layout.Section>
								<TextStyle variation="subdued">
									{canCreatePlanTrial ? (
										<Trans
											i18nKey="AFTERTHEFR_a7876"
											// eslint-disable-next-line no-template-curly-in-string
											defaults="After the free trial, your Premium plan is just ${{amount}}/{{intervalUnit}}. You can cancel anytime. <lnk>Terms</lnk> apply."
											components={{
												lnk: (
													// eslint-disable-next-line jsx-a11y/anchor-has-content
													<a
														href="https://www.aftership.com/legal/terms-of-service"
														target="_blank"
														rel="noreferrer"
														style={{
															color: 'inherit',
														}}
													/>
												),
											}}
											values={{
												amount: plan?.pricing.amount,
												intervalUnit:
													plan?.billingInterval
														.displayText || '',
											}}
										/>
									) : (
										// t('WANT_TO_CO_69431')
										<Trans
											i18nKey="WANT_TO_CO_69431"
											components={{
												lnk: (
													<Link
														onClick={() => {
															history.push(
																'/pricing/tracking?promotion_type=upgrade&promotion_id=want-to-compare-plan-button&redirect=0'
															);
															onClick();
															gaClick('E10224', {
																extraParams: {
																	features:
																		featureCode as string,
																},
															});
														}}
													/>
												),
											}}
										/>
									)}
								</TextStyle>
							</Layout.Section>
						)}
					</Layout>
				</div>
			</Card>
		</div>
	);
};

export default FeatureLockCard;
