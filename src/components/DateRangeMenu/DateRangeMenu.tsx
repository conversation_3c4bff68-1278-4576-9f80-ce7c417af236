import {Popover} from '@shopify/polaris';
import moment from 'moment-timezone';
import React from 'react';

import DateRangePicker from 'components/DateRangePicker';
import {DATE} from 'constants/Date';
import {
	getComparePeriodRange,
	getDateComparisonActivatorText,
} from 'pages/Dashboard/components/FilterHeader/utils';
import {useTrackingAnalyticsSearchState} from 'pages/Dashboard/hooks';
import {useCustomPopupClassNameRef} from 'pages/Insurance/hooks/useCustomPopupClassNameRef';
import {toOrgDate, toOrgMoment} from 'utils/day';

import styles from './DateRangeMenu.module.scss';

interface IDateRangeMenuProps {
	rangeOptions: {
		label: string;
		value: string;
		range: {
			start: Date;
			end: Date;
		};
	}[];
	menuDom: JSX.Element;
	minDate: Date;
	selectedDate: {start: Date | undefined; end: Date | undefined};
	selectedComparisonDate?: {start: Date | undefined; end: Date | undefined};
	onSelectedDate?: any;
	onSelectedComparisonDate?: any;
	activatorDom: JSX.Element;
	active: boolean;
	setActive: (val: boolean) => void;
	dateType?: string;
	isComparison?: boolean;
	setActivatorDateRangeText: (val: string) => void;
	comparisonType: string[];
	setComparisonType?: (val: string[]) => void;
	selectedDateRangeOption?: string[];
	setSelectedDateRangeOption?: (val: string[]) => void;
}

const DateRangeMenu = ({
	menuDom,
	rangeOptions,
	minDate,
	selectedDate,
	onSelectedDate,
	activatorDom,
	active,
	setActive,
	dateType,
	isComparison,
	comparisonType,
	setComparisonType,
	setActivatorDateRangeText,
	selectedComparisonDate,
	onSelectedComparisonDate,
	selectedDateRangeOption,
	setSelectedDateRangeOption,
}: IDateRangeMenuProps) => {
	const divRef = useCustomPopupClassNameRef<HTMLDivElement>(
		styles['date-range-menu']
	);
	const [query, onChangeQuery] = useTrackingAnalyticsSearchState();

	return (
		<div ref={divRef} className={styles['date-range-menu']}>
			<Popover
				active={active}
				fullHeight
				onClose={() => {
					setActive(false);

					setSelectedDateRangeOption &&
						setSelectedDateRangeOption([query.dateRangeOption]);
					onSelectedDate &&
						onSelectedDate(
							toOrgDate({
								start: query.startTime,
								end: query.endTime,
							})
						);

					setComparisonType &&
						setComparisonType([query.compareType || '']);
					onSelectedComparisonDate &&
						onSelectedComparisonDate(
							toOrgDate({
								start: query.compareStartTime,
								end: query.compareEndTime,
							})
						);
				}}
				activator={activatorDom}
			>
				<div className={styles['date-menu-container']}>
					<Popover.Pane>
						<div className={styles['date-menu']}>{menuDom}</div>
					</Popover.Pane>
					<DateRangePicker
						isMenuPicker
						withoutPopover
						minDate={minDate}
						rangeOptions={rangeOptions}
						onChange={(val: {
							start: Date | undefined;
							end: Date | undefined;
						}) => {
							const isBeforeMinDate =
								toOrgMoment(val.start).diff(
									toOrgMoment(minDate)
								) < 0;

							if (isComparison) {
								onChangeQuery({
									'date-type': dateType,
									'compare-start-date':
										comparisonType[0] === 'noComparison'
											? undefined
											: moment(val?.start).format(DATE),
									'compare-end-date':
										comparisonType[0] === 'noComparison'
											? undefined
											: moment(val?.end)
													.endOf('day')
													.format(DATE),
									'compare-type': comparisonType[0],
								});
								setActivatorDateRangeText(
									getDateComparisonActivatorText(
										comparisonType,
										selectedDate,
										val
									)
								);
								onSelectedComparisonDate(val);
							} else {
								onChangeQuery({
									'date-type': dateType,
									'start-date': isBeforeMinDate
										? toOrgMoment(minDate)
												.add(1, 'day')
												.format(DATE)
										: moment(val?.start).format(DATE),
									'end-date': moment(val?.end)
										.endOf('day')
										.format(DATE),
									'date-range-option':
										selectedDateRangeOption?.[0],
								});
								if (!comparisonType.includes('userOwnSelect')) {
									onChangeQuery({
										'date-type': dateType,
										'start-date': isBeforeMinDate
											? toOrgMoment(minDate)
													.add(1, 'day')
													.format(DATE)
											: moment(val?.start).format(DATE),
										'end-date': moment(val?.end)
											.endOf('day')
											.format(DATE),
										'compare-start-date': moment(
											getComparePeriodRange(
												val,
												comparisonType[0]
											)?.start
										).format(DATE),
										'compare-end-date': moment(
											getComparePeriodRange(
												val,
												comparisonType[0]
											)?.end
										).format(DATE),
										'date-range-option':
											selectedDateRangeOption?.[0],
									});
								}

								onSelectedDate({
									start: isBeforeMinDate
										? moment(minDate).add(1, 'day').toDate()
										: moment(val?.start).toDate(),

									end: moment(val?.end).endOf('day').toDate(),
								});
							}

							setActive(false);
						}}
						start={
							isComparison
								? selectedComparisonDate?.start
								: selectedDate?.start || new Date()
						}
						end={
							isComparison
								? selectedComparisonDate?.end
								: selectedDate?.end || new Date()
						}
						onClose={() => {
							setActive(!active);
						}}
						onSelectChange={() => {
							if (isComparison) {
								setComparisonType &&
									setComparisonType(['userOwnSelect']);
								onChangeQuery({
									'compare-type': 'userOwnSelect',
								});
							}
						}}
						onStartingChange={() => {
							if (isComparison) {
								setComparisonType &&
									setComparisonType(['userOwnSelect']);
								onChangeQuery({
									'compare-type': 'userOwnSelect',
								});
							}
						}}
						onEndingChange={() => {
							if (isComparison) {
								setComparisonType &&
									setComparisonType(['userOwnSelect']);
								onChangeQuery({
									'compare-type': 'userOwnSelect',
								});
							}
						}}
					/>
				</div>
			</Popover>
		</div>
	);
};

export default DateRangeMenu;
