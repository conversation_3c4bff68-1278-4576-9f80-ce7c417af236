import React from 'react';
import {useDispatch} from 'react-redux';

import {openModal} from 'actions/modals';
import {SUBSCRIPTION_MODAL} from 'constants/Modals';
import {useIsAvailableFeature} from 'hooks/billings';
import {useGetNearestPlanByFeatureCode} from 'hooks/billings/useChoosePlan';

export const NoFeatureMask: React.FC<{featureCode: string}> = props => {
	const {featureCode} = props;
	const dispatch = useDispatch();
	const [hasFeature] = useIsAvailableFeature(featureCode);
	const plan = useGetNearestPlanByFeatureCode(featureCode);

	if (hasFeature) return null;
	return (
		<div
			style={{
				width: '100%',
				height: '100%',
				opacity: 0,
				position: 'absolute',
				zIndex: 500,
				cursor: 'pointer',
				top: 0,
				left: 0,
			}}
			role="none"
			onClick={() => {
				dispatch(
					openModal(SUBSCRIPTION_MODAL, undefined, {
						planCode: plan ? plan.code : undefined,
						featureCodes: [featureCode],
					})
				);
			}}
		/>
	);
};
