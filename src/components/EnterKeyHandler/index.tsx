import React, {KeyboardEvent, ReactNode, useCallback} from 'react';

interface EnterKeyHandlerProps {
	onEnterPress: () => void;
	children: ReactNode;
	disabled?: boolean;
	stopPropagation?: boolean;
	preventDefault?: boolean;
}

const EnterKeyHandler: React.FC<EnterKeyHandlerProps> = ({
	onEnterPress,
	children,
	disabled = false,
	stopPropagation = false,
	preventDefault = true,
}) => {
	const handleKeyDown = useCallback(
		(event: KeyboardEvent<HTMLDivElement>) => {
			if (disabled) return;

			if (event.key === 'Enter') {
				if (preventDefault) {
					event.preventDefault();
				}

				if (stopPropagation) {
					event.stopPropagation();
				}

				onEnterPress();
			}
		},
		[onEnterPress, disabled, stopPropagation, preventDefault]
	);

	return (
		<div
			role="button"
			tabIndex={0}
			onKeyDown={handleKeyDown}
			aria-disabled={disabled}
		>
			{children}
		</div>
	);
};

export default EnterKeyHandler;
