import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';

import Enter<PERSON>eyHandler from './index';

describe('EnterKeyHandler', () => {
	it('应该在按下回车键时触发回调', () => {
		const mockEnterPress = jest.fn();

		render(
			<EnterKeyHandler onEnterPress={mockEnterPress}>
				<input data-testid="test-input" />
			</EnterKeyHandler>
		);

		const input = screen.getByTestId('test-input');
		fireEvent.keyDown(input, {key: 'Enter'});

		expect(mockEnterPress).toHaveBeenCalledTimes(1);
	});

	it('当禁用时不应触发回调', () => {
		const mockEnterPress = jest.fn();

		render(
			<EnterKeyHandler onEnterPress={mockEnterPress} disabled>
				<input data-testid="test-input" />
			</EnterKeyHandler>
		);

		const input = screen.getByTestId('test-input');
		fireEvent.keyDown(input, {key: 'Enter'});

		expect(mockEnterPress).not.toHaveBeenCalled();
	});

	it('不应对非回车键事件做出反应', () => {
		const mockEnterPress = jest.fn();

		render(
			<EnterKeyHandler onEnterPress={mockEnterPress}>
				<input data-testid="test-input" />
			</EnterKeyHandler>
		);

		const input = screen.getByTestId('test-input');
		fireEvent.keyDown(input, {key: 'A'});

		expect(mockEnterPress).not.toHaveBeenCalled();
	});
});
