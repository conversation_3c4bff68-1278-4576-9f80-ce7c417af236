import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';

import {CANCELED} from 'constants/billings/subscriptions/statuses';
import {useTrialSubscription} from 'hooks/billings/useSubscription';
import {useShopifyConnections} from 'hooks/connection';

dayjs.extend(isBetween);

export default function useIsFreeTrialExtendable() {
	const {data: connections = []} = useShopifyConnections();
	const isShopifyUser = Boolean(connections?.length);

	const {subscription} = useTrialSubscription();

	// 1. has shopify connection
	if (!isShopifyUser) return false;

	// 2. at least one trial subscription, new user don't have subscription
	if (!subscription) return false;

	// 3. trial subscription has not yet canceled
	if (subscription.status === CANCELED) return false;

	// subscription actual end at is depend on subscription.scheduleCancelAt
	const subscriptionDays = dayjs(subscription.scheduleCancelAt).diff(
		dayjs(subscription.currentPeriod.startAt),
		'day'
	);

	// 4. trial subscription period range is not larger than 7 days
	if (subscriptionDays > 7) return false;

	const diff = dayjs().diff(dayjs(subscription.currentPeriod.startAt), 'day');

	// 5. one day after subscription start
	return diff > 0;
}
