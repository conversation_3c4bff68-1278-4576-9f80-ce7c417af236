import {Banner} from '@shopify/polaris';
import {StarOutlineMinor} from '@shopify/polaris-icons';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {useDispatch, useSelector} from 'react-redux';

import {removeBanner} from 'actions/banners';
import {EXTEND_FREE_TRIAL} from 'constants/BannerNames';
import getBanner from 'selectors/getBanner';
import {openLiveChat} from 'utils/contactUs';

import styles from './ExtendFreeTrialBanner.module.scss';
import useIsFreeTrialExtendable from './hooks/useIsFreeTrialExtendable';

export default function ExtendFreeTrialBanner() {
	const {t} = useTranslation();
	const dispatch = useDispatch();
	const opened = useSelector(state => getBanner(state, EXTEND_FREE_TRIAL));

	const extendable = useIsFreeTrialExtendable();

	const triggerLiveChat = () => {
		dispatch(removeBanner(EXTEND_FREE_TRIAL));
		openLiveChat(t('CAN_I_EXT_c2374'));
	};
	if (!extendable || !opened) {
		return null;
	}

	return (
		<div className={styles.root}>
			<Banner
				icon={StarOutlineMinor}
				status="success"
				action={{
					content: t('CHAT_WITH_4bdfe'),
					onAction: triggerLiveChat,
				}}
				onDismiss={() => dispatch(removeBanner(EXTEND_FREE_TRIAL))}
			>
				<span role="img" aria-label={t('WOMAN_GEST_c2cb1')}>
					🙆‍♀️
				</span>
				{t('NBSP_SPEC_723a4')}
			</Banner>
		</div>
	);
}
