import React, {useEffect, useMemo, useRef} from 'react';
import {FixedSizeList as List} from 'react-window';

import {useEventHandler} from 'hooks/useEventHandler';

import {getFilterChoiceItem} from './Item';
import {FilterChoiceListProps, TransformOption} from './interface';
import {
	getChangedData,
	getExpandedData,
	getFakeOptions,
	getNullOptions,
	getOptionsMap,
	transformOption,
} from './utils';

export function FilterChoiceList({
	onMounted,
	onUnmounted,
	options,
	onExpand,
	onChange,
	expandedKeysMap = {},
	checkedKeysMap = {},
	hideValue,
	maxHeight,
	keyword,
	renderLabel,
	firstIsCard,
}: FilterChoiceListProps) {
	const optionsMap = useMemo(
		() => (options ? getOptionsMap(options) : {}),
		[options]
	);
	const onChangeWrapper = useEventHandler((key: string, checked: boolean) => {
		const data = getChangedData({
			key,
			checked,
			checkedKeysMap,
			optionsMap,
			firstIsCard,
		});
		onChange?.(data);
	});
	const onExpandedWrapper = useEventHandler(
		(key: string, expanded: boolean) => {
			const data = getExpandedData({
				key,
				expanded,
				expandedKeysMap,
			});
			onExpand?.(data);
		}
	);
	const {matchOptions, isMultiTier} = useMemo(() => {
		let allOptions: TransformOption[];
		if (!options) {
			allOptions = getFakeOptions(1);
		} else if (options.length === 0) {
			allOptions = getNullOptions(1);
		} else {
			allOptions = options.flatMap(option =>
				transformOption({
					option,
					level: 1,
					expandedKeysMap,
					keyword,
				})
			);
		}
		return {
			isMultiTier: Boolean(
				options?.some(
					option =>
						option.isHeader ||
						Boolean(option.children) ||
						option.isChildrenLazyLoad
				)
			),
			allOptions,
			matchOptions: allOptions.filter(({isMatch}) => isMatch),
		};
	}, [expandedKeysMap, options, keyword]);

	const row = getFilterChoiceItem({
		onExpand: onExpandedWrapper,
		onChange: onChangeWrapper,
		checkedKeysMap,
		hideValue,
		isMultiTier,
		renderLabel,
	});
	const realHeight = Math.min(maxHeight || 460, matchOptions.length * 30);
	const containerRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		onMounted?.();
		return () => {
			onUnmounted?.();
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);
	return (
		<div ref={containerRef}>
			<List
				height={realHeight}
				itemCount={matchOptions.length}
				itemSize={30}
				width="100%"
				itemData={matchOptions}
			>
				{row}
			</List>
		</div>
	);
}
