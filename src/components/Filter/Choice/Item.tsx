import {
	Checkbox,
	SkeletonBodyText,
	TextContainer,
	TextStyle,
} from '@shopify/polaris';
import {isNil} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {ListChildComponentProps} from 'react-window';

import {Expander} from './Expander';
import {FilterChoiceLabel} from './Label';
import {FilterChoiceListProps, TransformOption} from './interface';
import {isChecked} from './utils';

interface GetFilterChoiceItemProps
	extends Pick<
		FilterChoiceListProps,
		'expandedKeysMap' | 'hideValue' | 'renderLabel' | 'checkedKeysMap'
	> {
	isMultiTier: boolean;
	onChange(key: string, checked: boolean): void;
	onExpand(key: string, checked: boolean): void;
}

export const getFilterChoiceItem =
	({
		onExpand,
		onChange,
		checkedKeysMap = {},
		hideValue,
		isMultiTier,
		renderLabel,
	}: GetFilterChoiceItemProps) =>
	({index, style, data}: ListChildComponentProps<TransformOption[]>) => {
		const option = data[index];
		const {t} = useTranslation();
		const checked = isChecked(option.key?.split('.'), checkedKeysMap);
		return (
			<div style={style}>
				<div
					style={{
						display: 'flex',
						justifyContent: 'space-between',
						flexWrap: 'nowrap',
						alignItems: 'center',
					}}
					role="button"
					tabIndex={0}
					onClick={e => {
						e.stopPropagation();
					}}
				>
					{isMultiTier && (
						<Expander
							level={option.level}
							isExistsChild={option.isExistsChild}
							extended={option.extended}
							onExpand={() =>
								onExpand(option.key, !option.extended)
							}
						/>
					)}

					{option.isFake && (
						<div style={{width: '100%'}}>
							<TextContainer>
								<SkeletonBodyText lines={1} />
							</TextContainer>
						</div>
					)}
					{option.isNull && (
						<div
							style={{
								width: '100%',
								textAlign: 'left',
								marginLeft: 28,
							}}
						>
							<TextStyle variation="subdued">
								{t('NO_RESULTS_183e1')}
							</TextStyle>
						</div>
					)}
					{option.isHeader && (
						<div
							style={{
								color: '#6D7175',
								fontWeight: 600,
								width: '100%',
							}}
						>
							{`${option.label} (${option.value})`}
						</div>
					)}
					{!option.isFake && !option.isNull && !option.isHeader && (
						<>
							<div
								style={{
									flexGrow: 1,
									whiteSpace: 'nowrap',
									// overflow: 'hidden',
									// textOverflow: 'ellipsis',
									// width: '80px',
								}}
							>
								{/* .Polaris-Choice__Label */}
								<Checkbox
									id={option.key}
									label={
										renderLabel ? (
											renderLabel(
												option.key,
												option.label
											)
										) : (
											<FilterChoiceLabel
												label={option.label}
											/>
										)
									}
									checked={checked}
									onChange={checked => {
										if (!onChange) {
											return;
										}
										onChange(option.key, checked);
									}}
								/>
							</div>
							{!hideValue &&
								(!isNil(option.value) ? (
									<TextStyle variation="subdued">
										{option.value}
									</TextStyle>
								) : (
									<div style={{width: '30px'}}>
										<TextContainer>
											<SkeletonBodyText lines={1} />
										</TextContainer>
									</div>
								))}
						</>
					)}
				</div>
			</div>
		);
	};
