import {Tooltip} from '@shopify/polaris';
import React from 'react';

interface FilterChoiceLabelProps {
	label: string;
}

export const FilterChoiceLabel = ({label}: FilterChoiceLabelProps) => {
	if (label?.length <= 30) {
		return <>{label}</>;
	}
	return (
		<Tooltip content={label} dismissOnMouseOut preferredPosition="above">
			<div
				style={{
					width: '220px',
					whiteSpace: 'nowrap',
					overflow: 'hidden',
					textOverflow: 'ellipsis',
				}}
			>
				{label}
			</div>
		</Tooltip>
	);
};
