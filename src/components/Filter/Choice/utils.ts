import {
	cloneDeep,
	concat,
	filter,
	flow,
	forEach,
	get,
	merge,
	set,
} from 'lodash';

import {
	CheckedKeysMap,
	GetChangedDataProps,
	GetExpandedDataProps,
	Option,
	OptionsMap,
	TransformOption,
	TransformOptionProps,
} from './interface';

export const isChecked = (keys: string[], checkedKeysMap: CheckedKeysMap) => {
	let checked: boolean | 'indeterminate' | undefined;
	let i = 0;
	let node = checkedKeysMap;
	while (checked === undefined && i < keys?.length) {
		const key = keys[i];
		if (!node[key]) {
			checked = false;
		} else if (node[key] === true) {
			checked = true;
		} else {
			// @ts-ignore
			node = node[key];
		}
		i++;
	}
	if (checked === undefined && i === keys?.length && node) {
		checked = 'indeterminate';
	}
	return checked;
};

export const getFakeOptions = (level: number): TransformOption[] => [
	{
		isFake: true,
		key: '',
		label: '',
		level: level,
		isMatch: true,
	},
	{
		isFake: true,
		key: '',
		label: '',
		level: level,
		isMatch: true,
	},
	{
		isFake: true,
		key: '',
		label: '',
		level: level,
		isMatch: true,
	},
];

export const getNullOptions = (level: number): TransformOption[] => [
	{
		isNull: true,
		key: '',
		label: '',
		level: level,
		isMatch: true,
	},
];

export const transformOption = ({
	option,
	level,
	expandedKeysMap,
	keyword,
}: TransformOptionProps): TransformOption[] => {
	const {key, label, isChildrenLazyLoad, children, value, isHeader} = option;
	let tChildren: TransformOption[] = [];
	let isMatch =
		keyword && !isHeader
			? new RegExp(
					`^${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`,
					'i'
			  ).test(label)
			: true;
	const currentExpandedKeysMap = get(expandedKeysMap, key);
	if (currentExpandedKeysMap) {
		if (!children) {
			tChildren = getFakeOptions(level + 1);
		} else if (children.length > 0) {
			tChildren = children.flatMap(child =>
				transformOption({
					option: child,
					level: level + 1,
					expandedKeysMap,
					keyword,
				})
			);
		}
		const isMatchChild = tChildren.some(child => child.isMatch);
		if (isMatch && !isMatchChild) {
			tChildren.push({
				isNull: true,
				key: '',
				label: '',
				level: level,
				isMatch: true,
			});
		}
		if (!isMatch && isMatchChild) {
			isMatch = true;
		}
	}
	return concat(
		[
			{
				key,
				label,
				level,
				value,
				extended: Boolean(currentExpandedKeysMap),
				isChildrenLazyLoad,
				isChildrenUnload: !children,
				isExistsChild: Boolean(children) || isChildrenLazyLoad,
				isMatch,
				isHeader,
			},
		],
		tChildren
	);
};

export const getOptionsMap = (options: Option[]) => {
	const optionsMap: OptionsMap = {};
	options.forEach(option => {
		const {children, key} = option;
		set(optionsMap, key, {});
		if (children) {
			merge(optionsMap, getOptionsMap(children));
		}
	});
	return optionsMap;
};

export const getChangedData = ({
	key,
	checked,
	checkedKeysMap,
	optionsMap,
	firstIsCard,
}: GetChangedDataProps) => {
	const newCheckedKeysMap = cloneDeep(checkedKeysMap);
	if (checked) {
		set(newCheckedKeysMap, key, true);
		const keys = key.split('.');
		keys.pop();
		const minIndex = firstIsCard ? 1 : 0;
		while (keys.length > minIndex) {
			const keysMap = get(newCheckedKeysMap, keys);
			const keyCount = filter(keysMap, val => val === true).length;
			const optionCount = Object.keys(get(optionsMap, keys)).length;
			if (keyCount === optionCount) {
				set(newCheckedKeysMap, keys, true);
			}
			keys.pop();
		}
	} else if (get(checkedKeysMap, key)) {
		set(newCheckedKeysMap, key, undefined);
		const keys = key.split('.');
		keys.pop();
		while (keys.length > 0) {
			const values = flow(get, Object.values)(newCheckedKeysMap, keys);
			if (values.filter(Boolean).length) {
				break;
			}
			set(newCheckedKeysMap, keys, undefined);
			keys.pop();
		}
	} else {
		const keys = key.split('.');
		const lastKeys: string[] = [keys.pop() as string];
		while (keys.length > 0) {
			if (get(checkedKeysMap, keys)) {
				break;
			}
			lastKeys.push(keys.pop() as string);
		}
		if (keys.length > 0) {
			while (lastKeys.length > 0) {
				const nOptionsMap = get(optionsMap, keys);
				const lastKey = lastKeys.pop() as string;
				forEach(nOptionsMap, (_, appendKey) => {
					if (appendKey !== lastKey) {
						set(newCheckedKeysMap, [...keys, appendKey], true);
					} else {
						set(newCheckedKeysMap, [...keys, appendKey], undefined);
					}
				});
				keys.push(lastKey);
			}
		}
		while (keys.length > 0) {
			keys.pop();
			const values = Object.values(get(newCheckedKeysMap, keys, {}));
			if (values.length && !values.filter(Boolean)?.length) {
				set(newCheckedKeysMap, keys, undefined);
			}
		}
	}
	return newCheckedKeysMap;
};

export const getExpandedData = ({
	key,
	expanded,
	expandedKeysMap,
}: GetExpandedDataProps) => {
	const newExpandedKeysMap = cloneDeep(expandedKeysMap);
	set(newExpandedKeysMap, key, expanded ? {} : undefined);
	return newExpandedKeysMap;
};
