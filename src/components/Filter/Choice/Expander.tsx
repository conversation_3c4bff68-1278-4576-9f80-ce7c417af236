import {CaretDownMinor} from '@shopify/polaris-icons';
import React from 'react';

interface ExpanderProps {
	isExistsChild?: boolean;
	level: number;
	onExpand?(status: boolean): void;
	extended?: boolean;
}
export function Expander({
	level,
	isExistsChild,
	extended,
	onExpand,
}: ExpanderProps) {
	return (
		<div
			role="button"
			tabIndex={0}
			style={{
				padding: '0 5px 0 0',
				marginLeft: level > 1 ? 28 * (level - 1) : 0,
				position: 'relative',
				zIndex: 1,
				cursor: 'pointer',
				flexShrink: 0,
				top: 4,
				width: 28,
			}}
			onClick={() => {
				if (isExistsChild) {
					onExpand?.(!extended);
				}
			}}
		>
			{isExistsChild && (
				<div
					style={{
						transform: extended
							? 'none'
							: 'translate(2px, -3px) rotate(-90deg)',
					}}
				>
					<CaretDownMinor fill="#5C5F62" />
				</div>
			)}
		</div>
	);
}
