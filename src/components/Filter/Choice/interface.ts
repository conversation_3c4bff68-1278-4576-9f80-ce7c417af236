import React from 'react';

export interface Option {
	key: string;
	label: string;
	isHeader?: boolean;
	value?: string | number;
	isChildrenLazyLoad?: boolean;
	isChildrenLoading?: boolean;
	children?: Option[];
}

export interface TransformOption extends Omit<Option, 'children'> {
	isFake?: boolean;
	isNull?: boolean;
	isHeader?: boolean;
	level: number;
	isMatch: boolean;
	isExistsChild?: boolean;
	isChildrenUnload?: boolean;
	checked?: boolean | 'indeterminate';
	extended?: boolean;
}

export interface CheckedKeysMap {
	[key: string]: boolean | CheckedKeysMap | undefined;
}

export interface TransformOptionProps {
	option: Option;
	level: number;
	expandedKeysMap: CheckedKeysMap;
	parentChecked?: boolean;
	keyword?: string;
}

export interface CardOption {
	key: string;
	label: string;
	options: Option[];
}

export interface FilterChoiceListProps {
	onMounted?(key?: string): void;
	onUnmounted?(key?: string): void;
	renderLabel?(key: string, label: string): React.ReactNode;
	keyword?: string;
	options?: Option[];
	isLoading?: boolean;
	width?: number | string;
	maxHeight?: number;
	onChange?(data: CheckedKeysMap): void;
	onExpand?(data: CheckedKeysMap): void;
	checkedKeysMap?: CheckedKeysMap;
	expandedKeysMap?: CheckedKeysMap;
	hideValue?: boolean;
	firstIsCard?: boolean;
}

export interface OptionsMap {
	[key: string]: OptionsMap;
}

export interface GetChangedDataProps {
	key: string;
	checked: boolean;
	checkedKeysMap: CheckedKeysMap;
	optionsMap: OptionsMap;
	firstIsCard?: boolean;
}

export interface GetExpandedDataProps {
	key: string;
	expanded: boolean;
	expandedKeysMap: CheckedKeysMap;
}
