import React from 'react';

import {FilterChoiceList} from './List';
import {FilterChoiceListProps, Option} from './interface';

interface CardOption {
	key: string;
	label: string;
	value: number;
	options?: Option[];
}

export interface FilterChoiceCardProps
	extends Omit<FilterChoiceListProps, 'options'> {
	cardOptions: CardOption[];
	cardDefaultExpandedKeys?: string[];
}

export function FilterChoiceCard({
	cardOptions,
	...filterChoiceListProps
}: FilterChoiceCardProps) {
	const options = cardOptions.map(({label, key, value, options}) => ({
		label,
		key,
		isHeader: true,
		isChildrenLazyLoad: true,
		value,
		children: options,
	}));
	return (
		<FilterChoiceList
			{...filterChoiceListProps}
			options={options}
			firstIsCard
		/>
	);
}
