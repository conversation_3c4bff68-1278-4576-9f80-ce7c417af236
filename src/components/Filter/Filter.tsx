import {omit, pick} from 'lodash';
import React, {useState} from 'react';

import {
	FilterChoiceCard,
	FilterChoiceCardProps,
	FilterChoiceList,
	FilterChoiceListProps,
} from './Choice';
import {SearchField, SearchFieldProps} from './SearchField';

interface FilterProps extends Omit<SearchFieldProps, 'onSearch'> {
	withSearchField?: boolean;
	containerWidth?: number | string;
	containerClassName?: string;
	header?: React.ReactNode;
}

interface FilterWithCardProps extends FilterChoiceCardProps, FilterProps {}

interface FilterWithListProps extends FilterChoiceListProps, FilterProps {}

export function Filter({
	withSearchField,
	containerWidth,
	containerClassName,
	header,
	...props
}: FilterWithCardProps | FilterWithListProps) {
	const [keyword, setKeyword] = useState<string | undefined>();
	const searchFieldProps = pick(props, [
		'searchFieldClassName',
		'placeholder',
		'searchKeyword',
	]);
	const otherProps = omit(props, [
		'searchFieldClassName',
		'placeholder',
		'searchKeyword',
	]);
	let cardOptions;
	let options;
	if ('cardOptions' in otherProps) {
		({cardOptions} = otherProps);
	} else {
		({options} = otherProps as FilterChoiceListProps);
	}
	return (
		<div style={{width: containerWidth}} className={containerClassName}>
			{withSearchField && (
				<SearchField
					{...searchFieldProps}
					onSearch={value => setKeyword(value)}
					keyword={keyword}
				/>
			)}
			{header}
			{cardOptions && (
				<FilterChoiceCard
					{...otherProps}
					keyword={keyword}
					cardOptions={cardOptions}
				/>
			)}
			{!cardOptions && (
				<FilterChoiceList
					{...otherProps}
					keyword={keyword}
					options={options}
				/>
			)}
		</div>
	);
}
