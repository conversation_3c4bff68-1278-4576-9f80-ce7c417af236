import {Option} from './Choice/interface';

export const getMatchOptions = (
	keyword: string,
	options: Option[]
): Option[] => {
	return options
		.map(option => ({
			...option,
			isMatch: `${option.key} ${option.label}`
				.toLowerCase()
				.includes(keyword),
			children: option.children
				? getMatchOptions(keyword, option.children)
				: [],
		}))
		.filter(option => option.isMatch || option.children.length > 0);
};
