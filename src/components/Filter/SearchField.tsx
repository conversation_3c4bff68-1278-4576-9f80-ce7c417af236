import {Icon, TextField} from '@shopify/polaris';
import {SearchMinor} from '@shopify/polaris-icons';
import React from 'react';
import {useTranslation} from 'react-i18next';

export interface SearchFieldProps {
	searchFieldClassName?: string;
	placeholder?: string;
	keyword?: string;
	onSearch(keyword: string): void;
}
export function SearchField({
	searchFieldClassName,
	placeholder,
	keyword,
	onSearch,
}: SearchFieldProps) {
	const {t} = useTranslation();
	return (
		<div className={searchFieldClassName} style={{marginBottom: 8}}>
			<TextField
				label={t('KEYWORD_4b9f7')}
				prefix={<Icon source={SearchMinor} color="base" />}
				placeholder={placeholder || t('SEARCH_3a130')}
				value={keyword}
				onChange={onSearch}
				labelHidden
			/>
		</div>
	);
}
