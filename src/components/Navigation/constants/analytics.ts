export const BUNNINGS_DASHBOARD_ID = 'fddbc78af64744d9b6983974c2cae444';
export const APPLE_WALLET_DASHBOARD_ID = '8dae180408ea4017b2506cb5adfb39ab';
export const BUNNING_ORG_ID = '18e141fc2a51443fb8f639a051ad0a3e';

// https://aftership.atlassian.net/browse/ABU-31060
export const PUBLIC_TERM_DASHBOARDS = {
	'/dashboard/shipment': '68f3314dfb844573a40ba161e0bcc653',
	'/dashboard/order-to-delivery': 'fb8555237a0946309db350c0211c9647',
	'/dashboard/transit-time': '44825a398fc84762a26c80e61dc5a7f6',
	'/dashboard/on-time-shipment': '********************************',
	'/dashboard/exception': '64ea004af8144affab59bd9362cb02a0',
	'/dashboard/notification': 'de551144877b4122976d9563d444b092',
};
export const SHIPMENT_EVENTS_DASHBOARD_ID = '********************************';
export const SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID =
	'0fa70f74cb6d46ecb833a4aaf5d83be9';

export const TEST_PUBLIC_TERM_DASHBOARDS = {
	'/dashboard/shipment': '68f3314dfb844573a40ba161e0bcc653',
	'/dashboard/order-to-delivery': 'fb8555237a0946309db350c0211c9647',
	'/dashboard/transit-time': '44825a398fc84762a26c80e61dc5a7f6',
	'/dashboard/on-time-shipment': '********************************',
	'/dashboard/exception': '64ea004af8144affab59bd9362cb02a0',
	'/dashboard/notification': 'd1cebe920ba84144a88aaa1c7e1d40aa',
};
export const TEST_SHIPMENT_EVENTS_DASHBOARD_ID =
	'7d16588a18e546069695b47487d1db36';
export const TEST_SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID =
	'06642a3a19e84f14bd36511969dcd88a';
