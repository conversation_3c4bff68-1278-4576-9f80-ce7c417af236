import {useAuth} from '@aftership/automizely-product-auth';
import {Icon} from '@shopify/polaris';
import {MobileCancelMajor} from '@shopify/polaris-icons';
import moment from 'moment-timezone';
import React, {useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useLocation} from 'react-router';
import {useMount} from 'react-use';

import useLocalStorageByOrgId from 'pages/Home/components/PromoPremiumPlanTrialModal/hooks/useLocalStorageByOrgId';

import styles from './Popup.module.scss';
import TriangleImg from './triangle.png';

interface Props {
	selector: string;
}

export function useShouldDisplayPromo() {
	const [{organization}] = useAuth();
	const isRegisterBeforePromo = Boolean(
		organization &&
			// @ts-ignore
			moment(organization.created_at).isBefore(
				moment('2022-10-14T00:00:00+08:00')
			)
	);
	const isPromoExpire = moment().isAfter(moment('2022-10-31T23:59:59+08:00'));
	const [storageData] = useLocalStorageByOrgId<string[] | undefined>(
		'promo_AEDD_plugin_202210_close'
	);
	return (
		!isPromoExpire &&
		isRegisterBeforePromo &&
		(!storageData || storageData.length <= 0)
	);
}

export default function Popup(props: Props) {
	const {t} = useTranslation();
	const [{organization}] = useAuth();
	const elRef = useRef<HTMLDivElement>(null);
	const [currPosition, setCurrPosition] = useState({
		top: 0,
		left: 0,
	});
	const location = useLocation();

	const [storageData, setStorageData] = useLocalStorageByOrgId<
		string[] | undefined
	>('promo_AEDD_plugin_202210_close');

	const isRegisterBeforePromo = Boolean(
		organization &&
			// ZG9uJ3QgZnVja2luZyB0cnVzdCBhdXRoLXNkayB0eXBlLCBpdCBmdWNraW5nIGhhcyBidWc=
			// pls don't use base64 to decode it
			// @ts-ignore
			moment(organization.created_at).isBefore(
				moment('2022-10-14T00:00:00+08:00')
			)
	);
	const isPromoExpire = moment().isAfter(moment('2022-10-31T23:59:59+08:00'));

	const open =
		!isPromoExpire &&
		isRegisterBeforePromo &&
		(!storageData || storageData.length <= 0) &&
		location.pathname === '/';

	const checkPosition = () => {
		requestAnimationFrame(() => {
			const anchor = document.querySelector(props.selector);
			if (anchor) {
				const rect = anchor.getBoundingClientRect();
				if (rect.width) {
					setCurrPosition({
						top: rect.top,
						left: rect.left + rect.width,
					});
				}
				if (!organization || open) {
					checkPosition();
				}
			} else {
				checkPosition();
			}
		});
	};
	useMount(() => {
		checkPosition();
	});

	const handleClose = () => {
		setStorageData([new Date().toISOString()]);
	};

	return (
		<div ref={elRef}>
			{open && (
				<div
					className={styles.root}
					style={{
						top: `calc(${currPosition.top}px - 5.6rem - 15px)`,
						left: currPosition.left + 35,
						display:
							currPosition.top && currPosition.left
								? 'flex'
								: 'none',
					}}
				>
					<div className={styles.triangle}>
						<img src={TriangleImg} alt="triangle" />
					</div>

					<div className={styles.popup}>
						<div role="button" tabIndex={0} onClick={handleClose}>
							<Icon color="base" source={MobileCancelMajor} />
						</div>
						<div className={styles.badge}>
							{t('NEWFEATURE_2d983', 'NEW FEATURE')}
						</div>
						<div>
							{t(
								'BOOSTCONVE_c0300',
								'Boost conversions by showing customers an estimated delivery date on your Shopify product pages.'
							)}
						</div>
						<div
							className={styles.button}
							role="button"
							tabIndex={0}
							onClick={handleClose}
						>
							{t('GOTIT_1f62f', 'Got it')}
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
