.root {
	position: fixed;
}

.triangle img {
	position: absolute;
	transform: translateX(-19px);
	height: 20px;
	width: 20px;
	top: 20px;
}

.popup {
	height: 161px;
	width: 380px;
	box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);
	border-radius: 8px;
	background-color: #fff;
	padding: 16px;
	& > *:nth-child(1) {
		position: absolute;
		right: 20px;
		top: 20px;
		cursor: pointer;
	}
	display: flex;
	flex-direction: column;
	gap: 8px;
	color: #202223;
	font-weight: 400;
	font-size: 14px;
	line-height: 20px;
}
.badge {
	background-color: #fff1d9;
	color: #ffa300;
	font-size: 12px;
	line-height: 16px;
	padding: 4px 12px;
	font-weight: 700;
	width: fit-content;
	border-radius: 43px;
	pointer-events: all;
}
.button {
	font-weight: 500;
	line-height: 20px;
	padding: 5px 16px;
	background: #ffa300;
	box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.06);
	border-radius: 4px;
	color: #fff;
	align-self: flex-end;
	cursor: pointer;
	text-decoration: none;
}
