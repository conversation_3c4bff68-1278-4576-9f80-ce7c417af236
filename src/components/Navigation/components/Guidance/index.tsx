import {Stack} from '@shopify/polaris';
import {
	Popover as NtcPopover,
	useLaunchWeakAlertModal,
} from 'AftershipNavigation';
import React, {useContext, useRef, useEffect, useState} from 'react';
import {CircularProgressbar} from 'react-circular-progressbar';
import {CircularProgressbarStyles} from 'react-circular-progressbar/dist/types';
import {createPortal} from 'react-dom';
import {useTranslation} from 'react-i18next';
import {useHistory, useLocation} from 'react-router-dom';

import {
	Tours,
	TourContext,
	GuidanceTourSteps,
} from 'components/Tour/TourContext';
import {useTourHasShow} from 'components/Tour/utils';
import {useOrganizationOnboarding} from 'hooks/organization/useOrganizationOnboarding';

import styles from './index.module.scss';

const circularBarStyle = {
	root: {},
	path: {
		stroke: `#FF6B2B`,
		strokeLinecap: 'round',
		transition: 'stroke-dashoffset 0.5s ease 0s',
	},
	trail: {
		stroke: '#D9D9D9',
		strokeLinecap: 'butt',
	},
};

const Guidance = () => {
	const {initTour, closeTour} = useContext(TourContext);
	const {t} = useTranslation();
	const history = useHistory();
	const {pathname} = useLocation();
	const isSelected = pathname.includes('/guidance');
	const {progress, isFetched, isHideGuide} = useOrganizationOnboarding();

	const popoverTargetRef = useRef<HTMLDivElement>(null);
	const [targetTop, setTargetTop] = useState<number>(0);
	const [targetLeft, setTargetLeft] = useState<number>(0);
	const hasShowTour = useTourHasShow();
	const [openWeakAlertModal] = useLaunchWeakAlertModal();

	useEffect(() => {
		// 顶部 Push Center Banner 没有相关接口判断是否显示，先简单轮询判断位置变化
		const timer = setInterval(() => {
			const rect = popoverTargetRef.current?.getBoundingClientRect();

			setTargetTop(rect?.top || 0);
			setTargetLeft(rect?.left || 0);
		}, 1000);

		return () => {
			clearInterval(timer);
		};
	}, []);

	const isShowNtcPopover =
		!isSelected &&
		!hasShowTour &&
		Boolean(targetTop) &&
		Boolean(targetLeft);

	return !isFetched || isHideGuide ? null : (
		<>
			<ul
				className="Polaris-Navigation__Section"
				id="onboarding-navigation"
			>
				<li className="Polaris-Navigation__ListItem">
					<div
						className="Polaris-Navigation__ItemWrapper"
						ref={popoverTargetRef}
					>
						<a
							data-polaris-unstyled="true"
							className={`Polaris-Navigation__Item ${
								isSelected
									? 'Polaris-Navigation__Item--selected'
									: ''
							}`}
							role="button"
							tabIndex={0}
							style={{position: 'relative'}}
							onClick={() => {
								history.push('/guidance');

								setTimeout(() => {
									closeTour(Tours.guidanceTour);
								}, 100);
							}}
							onMouseEnter={() => {
								if (history.location.pathname !== '/guidance') {
									initTour(
										Tours.guidanceTour,
										GuidanceTourSteps.intro
									);
								}
							}}
							onMouseLeave={() => {
								closeTour(Tours.guidanceTour);
								// [ADMP-1709]Tracking admin growth 触点方案, stage: Corner card - feature related version 1
								openWeakAlertModal();
							}}
							id={styles['navigation-guidance']}
						>
							<div className={styles.circularProgressbar}>
								<CircularProgressbar
									value={progress}
									styles={
										circularBarStyle as CircularProgressbarStyles
									}
									strokeWidth={12}
								/>
							</div>
							<span className="Polaris-Navigation__Text">
								<Stack vertical spacing="none">
									<div>{t('GETTING_ST_cb59b')}</div>
									<div
										style={{
											color: '#637381',
											fontWeight: 400,
										}}
									>
										{progress}% {t('COMPLETE_d9a22')}
									</div>
								</Stack>
							</span>
						</a>
					</div>
				</li>
			</ul>
			{createPortal(
				<div
					className={styles.popoverCntr}
					style={{
						top: targetTop + 30,
						left: targetLeft + 180,
					}}
				>
					<NtcPopover
						pageCode="onboardingTop"
						open={isShowNtcPopover}
					/>
					<div className={styles.popoverMask} />
				</div>,
				document.body
			)}
		</>
	);
};

export default React.memo(Guidance);
