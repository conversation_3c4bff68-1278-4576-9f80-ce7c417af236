#navigation-guidance {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-top: 5px;
	margin-bottom: -10px;
	.circularProgressbar {
		width: 28px;
		height: 28px;
		display: flex;
	}
}

.popoverCntr {
	position: fixed;
	z-index: 999;
	width: 0;
	height: 0;
}
.popoverMask {
	display: none;
	position: absolute;
	top: -30px;
	left: -180px;
	width: 200px;
	height: 60px;
	// background-color: red;
	// opacity: 0.5;
}
.popoverCntr:has(:global(.ntc-base-container .ntc-popover)) .popoverMask {
	display: block;
}
