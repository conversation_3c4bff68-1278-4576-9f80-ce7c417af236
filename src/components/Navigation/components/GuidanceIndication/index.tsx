import {Stack, ProgressBar} from '@shopify/polaris';
import React from 'react';
import {useTranslation} from 'react-i18next';

import {useOnboardingItemsV2} from 'hooks/useOnboardingItemsV2';

const GuidanceIndication = ({hideProgressBar}: {hideProgressBar?: boolean}) => {
	const {t} = useTranslation();
	const {progress, itemsCompleted, itemsTotal, hasFinishedOnboarding} =
		useOnboardingItemsV2();

	return (
		<div>
			<Stack alignment="center">
				<Stack.Item>{t('GETTING_ST_f8267')}</Stack.Item>{' '}
			</Stack>
			{hasFinishedOnboarding || hideProgressBar ? null : (
				<div style={{display: 'flex', alignItems: 'center'}}>
					<div
						className="ProgressBar"
						style={{width: '120px', marginRight: '6px'}}
					>
						<ProgressBar progress={progress} size="small" />
					</div>
					<div style={{color: 'black'}}>
						{`${itemsCompleted}/${itemsTotal}`}
					</div>
				</div>
			)}
		</div>
	);
};

export default React.memo(GuidanceIndication);
