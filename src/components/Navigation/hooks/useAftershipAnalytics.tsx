import {
	aioAnalyticsFiltersToQueryString,
	PageParams,
	PagePermissionParams,
	parseLegacyAnalyticsCommonFilter,
	ProductCode,
	RouteEntry,
	transformLegacyAnalyticsFilter,
	useAnalytiker,
	TrackingDateType,
} from '@aftership/analytiker-editor';
import {useCallback, useEffect, useMemo} from 'react';
import {useHistory, useLocation} from 'react-router';

import {useShouldShowAllInOneReport} from 'hooks/analytics';
import {useHasShopifyScopeConnection} from 'hooks/connection';
import useShowAppleWallet from 'hooks/useShowAppleWallet';
import {AioPageWrapper} from 'pages/Dashboard/components/AioPageWrapper';
import {useIsAllinoneEmail} from 'pages/Notifications/Setting/hooks/useIsAllinoneEmail';

import {
	APPLE_WALLET_DASHBOARD_ID,
	BUNNING_ORG_ID,
	BUNNINGS_DASHBOARD_ID,
	PUBLIC_TERM_DASHBOARDS,
	SHIPMENT_EVENTS_DASHBOARD_ID,
	SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID,
	TEST_PUBLIC_TERM_DASHBOARDS,
	TEST_SHIPMENT_EVENTS_DASHBOARD_ID,
	TEST_SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID,
} from '../constants';

const isStagingOrProduction = ['staging', 'production'].includes(
	process.env.APP_ENV || ''
);

export function useAftershipShipmentAnalytics(routeEntries: RouteEntry[]) {
	const isShopifyUser = useHasShopifyScopeConnection();
	const shouldRedirectToAllInOne = useShouldShowAllInOneReport();

	return useMemo(() => {
		const entries: RouteEntry[] = [];
		const redirects: {
			from: string;
			to: string;
			path: string;
			search: string;
		}[] = [];

		if (shouldRedirectToAllInOne) {
			const TRACKING_DASHBOARD_MAPPING = isStagingOrProduction
				? PUBLIC_TERM_DASHBOARDS
				: TEST_PUBLIC_TERM_DASHBOARDS;

			if (isShopifyUser) {
				TRACKING_DASHBOARD_MAPPING['/dashboard/notification'] =
					isStagingOrProduction
						? SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID
						: TEST_SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID;
			}

			const shipmentEventsEntry = routeEntries.find(
				entry =>
					entry.dashboardId ===
					(isStagingOrProduction
						? SHIPMENT_EVENTS_DASHBOARD_ID
						: TEST_SHIPMENT_EVENTS_DASHBOARD_ID)
			);

			// redirect all the legacy dashboard path to the aio dashboard route entry with <Redirect from to>
			Object.entries(TRACKING_DASHBOARD_MAPPING).forEach(
				([legacyPath, dashboardId]) => {
					const entry = routeEntries.find(
						entry => entry.dashboardId === dashboardId
					);

					if (entry) {
						redirects.push({
							from: legacyPath,
							to: entry.url,
							path: entry.url.split('?')[0],
							search: entry.url.split('?')[1] || '',
						});

						// push event dashboard before notification dashboard
						if (
							legacyPath === '/dashboard/notification' &&
							shipmentEventsEntry
						) {
							entries.push(shipmentEventsEntry);
						}

						entries.push(entry);
					}
				}
			);
		}

		return {
			shipmentRouteEntries: entries,
			shipmentRouteRedirects: redirects,
		};
	}, [shouldRedirectToAllInOne, isShopifyUser, routeEntries]);
}

/**
 * redirect legacy dashboard path to the new all-in-one analytics dashboard path
 * e.g.
 * https://admin.aftership.io/dashboard/shipment
 * 	?compare-end-date=20240823&compare-start-date=20240816&compare-type=userOwnSelect
 * 	&end-date=20240830&start-date=20240824
 */
export const useRedirectLegacyShipmentDashboard = (
	shipmentRouteRedirects: {from: string; to: string}[]
) => {
	const history = useHistory();
	const location = useLocation();

	// @ts-ignore
	const {from: fromPath, search: queryString} = location.state || {};

	useEffect(() => {
		if (!fromPath || !queryString) {
			return;
		}

		if (shipmentRouteRedirects.length === 0) {
			return;
		}

		const redirect = shipmentRouteRedirects.find(
			redirect => redirect.from === fromPath
		);

		if (!redirect) {
			return;
		}

		const {to} = redirect;

		if (!queryString) {
			return;
		}

		// transform legacy query string to aio query string
		const query = parseLegacyAnalyticsCommonFilter(queryString);
		const isNotificationDashboard = fromPath === '/dashboard/notification';

		// only sync the date filters
		const aioQuery = transformLegacyAnalyticsFilter(
			{
				'start-date': query['start-date'],
				'end-date': query['end-date'],
				'date-type': !isNotificationDashboard
					? query['date-type']
					: TrackingDateType.NOTIFICATION_CREATED_AT,
				'compare-start-date': query['compare-start-date'],
				'compare-end-date': query['compare-end-date'],
				'compare-type': query['compare-type'],
				'date-range-type': query['date-range-type'],
			},
			false
		);
		const aioQueryString = aioQuery
			? aioAnalyticsFiltersToQueryString(aioQuery)
			: '';

		// check if the query string is the same
		if (location.search.includes(aioQueryString)) {
			return;
		}

		// replace cr-filters with the new query string
		const currentQuery = to.split('?')[1] || '';
		const newQuery = !currentQuery.includes('cr-filters')
			? currentQuery + '&' + aioQueryString
			: currentQuery.replace(/cr-filters=[^&]+/, aioQueryString);

		history.replace({
			pathname: to.split('?')[0],
			search: newQuery,
		});
	}, [fromPath, queryString, shipmentRouteRedirects]);
};

export const useAftershipAnalytics = (orgId: string) => {
	const isBunningOrg = orgId === BUNNING_ORG_ID;
	const isShopifyUser = useHasShopifyScopeConnection();
	const showAppleWalletApp = useShowAppleWallet();
	const isAllinone = useIsAllinoneEmail();

	const customRender = useCallback((param: PageParams) => {
		return <AioPageWrapper pageParams={param} />;
	}, []);

	const customPermission = useCallback(
		(page: PagePermissionParams) => {
			const isNotificationDashboard = [
				PUBLIC_TERM_DASHBOARDS['/dashboard/notification'],
				TEST_PUBLIC_TERM_DASHBOARDS['/dashboard/notification'],
				SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID,
				TEST_SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID,
			].includes(page.dashboardId);

			const defaultPermission = {
				query: true,
				interaction: true,
			};

			// if the dashboard is notification dashboard and the user is not upgraded, set the interaction to false
			if (isNotificationDashboard && !isAllinone) {
				return {
					...defaultPermission,
					interaction: false,
				};
			}

			return defaultPermission;
		},
		[isAllinone]
	);

	const {routeEntries, routeLoaders} = useAnalytiker({
		productCode: ProductCode.AS,
		syncFilters: true,
		render: customRender,
		permission: customPermission,
	});

	const newRouteEntries = useMemo(() => {
		return routeEntries.filter(entry => {
			// dashboard with special conditions to show
			if (entry.dashboardId === APPLE_WALLET_DASHBOARD_ID) {
				return showAppleWalletApp;
			}

			if (entry.dashboardId === BUNNINGS_DASHBOARD_ID) {
				return isBunningOrg;
			}

			return true;
		});
	}, [isBunningOrg, routeEntries, showAppleWalletApp]);

	// shipment analytics dashboards
	const {shipmentRouteEntries, shipmentRouteRedirects} =
		useAftershipShipmentAnalytics(routeEntries);

	// redirect legacy dashboard path to the new all-in-one analytics dashboard path
	useRedirectLegacyShipmentDashboard(shipmentRouteRedirects);

	// exclude shipment analytics dashboard
	const restRouteEntries = useMemo(() => {
		const TRACKING_DASHBOARD_MAPPING = isStagingOrProduction
			? PUBLIC_TERM_DASHBOARDS
			: TEST_PUBLIC_TERM_DASHBOARDS;

		const shopifyNotificationDashboardId = isStagingOrProduction
			? SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID
			: TEST_SHOPIFY_USER_NOTIFICATION_DASHBOARD_ID;
		const nonShopifyNotificationDashboardId =
			TRACKING_DASHBOARD_MAPPING['/dashboard/notification'];

		if (isShopifyUser) {
			TRACKING_DASHBOARD_MAPPING['/dashboard/notification'] =
				shopifyNotificationDashboardId;
		}

		const shipmentDashboardIds = Object.values(TRACKING_DASHBOARD_MAPPING)
			.concat(
				isStagingOrProduction
					? SHIPMENT_EVENTS_DASHBOARD_ID
					: TEST_SHIPMENT_EVENTS_DASHBOARD_ID
			)
			.concat([
				nonShopifyNotificationDashboardId,
				shopifyNotificationDashboardId,
			]);

		return newRouteEntries.filter(
			entry => !shipmentDashboardIds.includes(entry.dashboardId)
		);
	}, [newRouteEntries, isShopifyUser]);

	return {
		routeEntries: restRouteEntries,
		routeLoaders,
		shipmentRouteEntries,
		shipmentRouteRedirects,
	};
};
