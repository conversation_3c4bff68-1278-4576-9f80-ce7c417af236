import {useAuth} from '@aftership/automizely-product-auth';
import {parse} from 'query-string';
import {useMemo} from 'react';
import {useTranslation} from 'react-i18next';

import {AS_ANALYTICS_GLOBAL_FILTER} from 'constants/Analytics';
import {getLastDaysTick} from 'utils/day';

export const TEST_ID_SET = new Set([
	'0a909b7db4314b97aabc41103c845804', // test env id
	'896fde15e2dc4f3583cfef1c3ada0da0', // prod env id
	'1443dac454744cb09bdfcd54efd815f3', // test wm.nie
	'09ecdd72984d4357be664798d100181d', // prod wm.nie
	'b91aa9fddd5c4b45b7d817f23fb44ae9', // test x.yang
	'52a244236c054dc1a99cb2be4837135f', // test x.yang
	'7a535966da884c088886bb652dfaefbc', // prod x.yang
]);

export const SALES_DEMO_ID_SET = new Set(['8c3e2d9e017f41c8ab123f59c21b8f46']);
export const CANVA_ID_SET = new Set(['b924c10f45d647f78897bb1f9f0c88b2']);
export const ECOFLOW_SLA_ID_SET = new Set([
	'1ea08a08e9ad44cfa9ca9dc7101d2b3c',
	'd713bb4228c84bfab7e6fd43dccfd94b',
	'03f4ed884d424a6caf60351237426a47',
	'a62dc25b10ef47af9ad9d2228a5191d2',
	'1c8278501c9042d986106a40a8d3ddec',
	'74eb0277414c4837a9091521a32ad1b1',
]);
export const ECOFLOW_PRIME_ID_SET = new Set([
	'6dfcc03d58d746a4bcc9b753fb462f1c',
]);

export const CHECKPOINT_UPDATE_INTERVAL = new Set([
	// ABU-21431
	'00435d665d0642289b9e7bdb61be2403',
	'bdb907d50c5740a1bd90b0e5cba80760',
	'ab5eb882b79b48a19221a10af3ef68ab',
]);

export const CHECKPOINT_UPDATE_INTERVAL_TTS = new Set([
	// ABU-28191
	'a7c031518ca54c39aeeec11c2b306131',
	'1e71ea92a00745a49c008ce55304f672',
	'66d1d53a8c9c49f5a2470942a452f842',
	// test org
	'bb66d166678949b5bf346376bd10391b',
	'645caed47ed34fa6957fa926c712995e',
]);

export const EMAIL_PARSER = new Set(['fc212037b5ad490b891f4e0d6421026a']);
export const AESOP_ID_SET = new Set(['e360f1e461ec40d18e6567ca1ffa7693']);

export default function useCustomizeAnalyticsNavItem() {
	const {t} = useTranslation();
	const [{organization}] = useAuth();
	const orgId = organization?.id || '';

	const analyticsGlobalFilterStr =
		sessionStorage?.getItem(AS_ANALYTICS_GLOBAL_FILTER) || '';

	const analyticsGlobalFilter = parse(analyticsGlobalFilterStr);

	return useMemo(() => {
		const isTestOrg = TEST_ID_SET.has(orgId);
		const arr = [];
		if (isTestOrg || CANVA_ID_SET.has(orgId)) {
			arr.push({
				url: '/dashboard/customize/canva/carbon',
				label: t('CARBON_EM_7a849'),
			});
		}
		if (isTestOrg || SALES_DEMO_ID_SET.has(orgId)) {
			arr.push({
				url: '/dashboard/customize/demo/carbon-emission',
				label: 'Carbon emission ',
			});
		}
		if (isTestOrg || CHECKPOINT_UPDATE_INTERVAL.has(orgId)) {
			arr.push({
				url: `/dashboard/customize/checkpoint-update-interval`,
				label: t('CHECKPOINT_UPDATE_jf2342ef'),
			});
		}
		if (isTestOrg || CHECKPOINT_UPDATE_INTERVAL_TTS.has(orgId)) {
			arr.push({
				url: `/dashboard/customize/checkpoint-update-interval-tts`,
				label: 'Checkpoint update interval ',
			});
		}
		if (isTestOrg || EMAIL_PARSER.has(orgId)) {
			arr.push({
				url: `/dashboard/customize/email-parser?start-date=${
					analyticsGlobalFilter?.['start-date'] || getLastDaysTick(29)
				}&end-date=${
					analyticsGlobalFilter?.['end-date'] || getLastDaysTick(0)
				}`,
				label: t('PARSER_woe432'),
			});
		}
		if (isTestOrg || AESOP_ID_SET.has(orgId)) {
			arr.push({
				url: `/dashboard/customize/aesop/carbon-emission`,
				label: t('CARBONEMIS_98419'),
			});
		}
		return arr;
	}, [orgId, t, analyticsGlobalFilter]);
}
