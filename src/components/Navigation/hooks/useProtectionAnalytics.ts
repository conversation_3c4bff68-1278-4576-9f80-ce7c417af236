import {useAnalytiker} from '@aftership/analytiker-editor';
import {useAuth} from '@aftership/automizely-product-auth';
import {useRBACContext} from '@aftership/automizely-rbac-react';

import {RBAC_ACTION, RBAC_RESOURCE} from 'pages/Insurance/constants/rbac';

export const useProtectionAnalytics = () => {
	const [{company}] = useAuth();

	const {ability} = useRBACContext();
	const {routeEntries, routeLoaders} = useAnalytiker({
		env:
			process.env.APP_ENV &&
			['production', 'staging'].includes(process.env.APP_ENV)
				? (process.env.APP_ENV as 'production' | 'staging')
				: 'testing',
		productCode: 'protection',
		pathPrefix: '/protection/overview',
		companyId: company?.id,
		syncFilters: true,
	});

	const canViewClaimStatus = ability.can(
		RBAC_ACTION.VIEW,
		RBAC_RESOURCE.CLAIM_OVERVIEW_CLAIM_STATUS
	);
	const filteredEntries = routeEntries.filter(e => {
		if (
			canViewClaimStatus &&
			e.url.includes('bbd17f5b7f4849e4b1c20bd1f3b2b0c8')
		) {
			return false;
		}
		if (
			!canViewClaimStatus &&
			e.url.includes('84fb1d70932e4b0b89c13281ea1f709c')
		) {
			return false;
		}
		return true;
	});

	return {
		routeEntries: filteredEntries,
		routeLoaders,
	};
};
