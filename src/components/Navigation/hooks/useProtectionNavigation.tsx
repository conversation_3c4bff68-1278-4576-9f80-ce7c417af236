import {useAuth} from '@aftership/automizely-product-auth';
import {Badge, NavigationItemProps, SubNavigationItem} from '@shopify/polaris';
import {FraudProtectMajor} from '@shopify/polaris-icons';
import React, {useMemo} from 'react';
import {useTranslation} from 'react-i18next';

import {useOrganizationId} from 'hooks';
import useBusinessModel from 'pages/Insurance/hooks/useBusinessModel';
import {useProtectionEntryStatus} from 'pages/Insurance/hooks/useProtectionEntryStatus';
import isCompany from 'utils/isCompany';

import {useProtectionAnalytics} from './useProtectionAnalytics';

export const useProtectionNavigation = (): NavigationItemProps | null => {
	const {t} = useTranslation();
	const organizationId = useOrganizationId();
	const [{company}] = useAuth();
	const {isInsuranceModel} = useBusinessModel();
	const {isConfiguredProtection} = useProtectionEntryStatus();
	const {routeEntries} = useProtectionAnalytics();

	const isProtectionVisibleOnCompany = useMemo(
		() =>
			process.env.APP === 'production'
				? isCompany() && company?.id === 'C-100341'
				: true,
		[company]
	);

	const analyticsNavItems = routeEntries.map(entry => {
		return {
			url: entry.url,
			label: entry.label,
			matches: entry.matches(),
			badge: entry.badge,
		};
	});

	const subNavigationItems = useMemo<SubNavigationItem[]>(() => {
		let result = [
			{
				url: '/protection/claims',
				label: t('CLAIMS_6e2dd'),
			},
			{
				url: '/protection/coverages',
				label: t('COVERAGES_c9e80'),
			},
		];
		if (!isCompany()) {
			if (isInsuranceModel) {
				result = result.concat({
					url: '/protection/claim-page',
					label: 'Claim page',
				});
			}

			if (organizationId !== '8c06e2aabb894682a727d3bd800c1028') {
				result = result.concat({
					url: '/protection/notification',
					label: 'Notifications',
				});
			}
		}
		result = result.concat(analyticsNavItems);
		if (isInsuranceModel && !isCompany()) {
			result = result.concat({
				url: '/protection/settings',
				label: t('SETTINGS_267ef'),
			});
		}
		return result;
	}, [analyticsNavItems, isInsuranceModel, organizationId, t]);

	return useMemo(() => {
		if (!isProtectionVisibleOnCompany) {
			return null;
		}
		if (isConfiguredProtection || isCompany()) {
			return {
				exactMatch: true,
				url: '/protection/claims',
				label: t('PROTECTIO_c5cac'),
				icon: FraudProtectMajor,
				badge: !isCompany ? (
					<Badge status="new" size="small">
						{t('NEW_11893')}
					</Badge>
				) : undefined,
				subNavigationItems,
			};
		}
		return {
			exactMatch: true,
			url: '/protection/welcome',
			label: t('PROTECTIO_01239'),
			icon: FraudProtectMajor,
		};
	}, [
		isConfiguredProtection,
		isProtectionVisibleOnCompany,
		subNavigationItems,
		t,
	]);
};
