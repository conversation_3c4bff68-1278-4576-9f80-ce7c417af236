import {ProductCode, useAnalytiker} from '@aftership/analytiker-editor';
import {useAuth} from '@aftership/automizely-product-auth';

export const useAftershipCompanyAnalytics = () => {
	const [{company}] = useAuth();

	const {routeEntries, routeLoaders} = useAnalytiker({
		productCode: ProductCode.AS,
		syncFilters: true,
		companyId: company?.id,
	});

	return {
		routeEntries,
		routeLoaders,
	};
};
