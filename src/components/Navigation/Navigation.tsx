/* eslint-disable max-lines */

import {
	TasksMini,
	NavigationItem,
	useOnboardingInited,
} from '@aftership/sdk-journey-onboarding';
import {ApolloProvider} from '@apollo/client';
import {
	Navigation as PolarisNavigation,
	Stack,
	Icon,
	Badge,
	NavigationItemProps,
	Tooltip,
} from '@shopify/polaris';
import {
	HomeMajor,
	OnlineStoreMajor,
	ViewMinor,
	ShipmentMajor,
	AppsMajor,
	LockMinor,
	EmailMajor,
	ExternalMinor,
	AnalyticsMajor,
	SettingsMajor,
	HintMajor,
	QuestionMarkMajor,
} from '@shopify/polaris-icons';
import {IdeaPortalEntryConfig} from 'AftershipNavigation';
import {FeatureSlugs, useBillingFeatureStatus} from 'aftershipNotification';
import qs, {parse} from 'query-string';
import React, {useEffect, useMemo, useRef} from 'react';
import {TFunction, useTranslation} from 'react-i18next';
import {useHistory, useLocation} from 'react-router';

import {useTrackingPagesQuery} from '@graphql/generated';
import {AS_ANALYTICS_GLOBAL_FILTER} from 'constants/Analytics';
import {
	ANALYTICS_ON_TIME_SHIPMENT,
	NOTIFICATION_REPORT,
	SHIPMENTS_REPORT,
	TRACKING_PAGE_REPORT,
	TRANSIT_TIME_REPORT_CODE,
	TRACKING_PAGE_REVIEWS_REPORT,
	EXCEPTION_SHIPMENTS_REPORT,
	ANALYTICS_ORDER_TO_DELIVERY,
	BENCHMARKS_DASHBOARD,
	TRACKING_PAGE_TRACKING_POP_UP,
} from 'constants/billings/features';
import {HIDE_TRACKING_CLIPS_NEW_FEATURE_BADGE} from 'constants/localstorageKeys';
import {
	AS_FILTER_CURRENT_ORG_RECORD,
	AS_FILTER_LAST_ORG_RECORD,
} from 'constants/sessionStorageKeys';
import {useModal, useOrganizationId} from 'hooks';
import {useShouldShowAllInOneReport} from 'hooks/analytics';
import {useIsAvailableFeature} from 'hooks/billings';
import {useWhiteOrg} from 'hooks/configCenter/whiteList';
import {useOrganizationOnboarding} from 'hooks/organization/useOrganizationOnboarding';
import useHasBillingAndExtraFeature from 'hooks/useHasBillingAndExtraFeature';
import useIsWSC from 'hooks/useIsWSC';
import useIsWineShipping from 'hooks/useIsWineShipping';
import useLocalStorageWithOrgId from 'hooks/useLocalStorageWithOrgId';
import {useOrdersPermission} from 'hooks/useOrdersPermission';
import useShowAppleWallet from 'hooks/useShowAppleWallet';
import {ReactComponent as IconAIGrey} from 'icons/AI-grey.svg';
import {ReactComponent as IconAppleWallet} from 'icons/appleWallet.svg';
import {ReactComponent as IconEdd} from 'icons/logo_edd.svg';
import {ReactComponent as IconGreen} from 'icons/logo_green.svg';
import {ReactComponent as IconTracking} from 'icons/logo_tracking.svg';
import {ReactComponent as IconPaypal} from 'icons/trackingPage/PayPal-logo.svg';
import {useProtectionEntryStatus} from 'pages/Insurance/hooks/useProtectionEntryStatus';
import {useIsAllinoneEmail} from 'pages/Notifications/Setting/hooks/useIsAllinoneEmail';
import {useTrackWithPayPalPermissions} from 'pages/PaypalTracking/hooks';
import useShowTrackingCopilot from 'pages/TrackingCopilot/hooks/useShowTrackingCopilot';
import {useTrackingPageBaseURL} from 'pages/URLsAndDomain/utils/hooks';
import {automizelyClient} from 'utils/apollo';
import {getLastDaysTick} from 'utils/day';
import {gaClick} from 'utils/gtag';

import GuidanceIndication from './components/GuidanceIndication';
import Popup from './components/Popup';
import {useAftershipAnalytics} from './hooks/useAftershipAnalytics';
import useCustomizeAnalyticsNavItem from './hooks/useCustomizeAnalyticsNavItem';
import {useProtectionNavigation} from './hooks/useProtectionNavigation';

import './Navigation.css';

import {useImportingConnections} from 'hooks/connection';
import {useTrackingConnectionJob} from 'pages/Shipments/hooks';
import {openLiveChat} from 'utils/crisp';

export const goToExternalUrlHandler = (url: string) =>
	window.open(url, '_blank', 'noopener noreferrer');

function getExtraProductItem(options: any, t: TFunction) {
	const productUrl = qs.stringifyUrl({
		url: options.url,
		query: {
			organization_id: options.organizationId,
		},
	});
	return {
		label: options.label,
		icon: options.icon,
		selected: options.selected,
		badge: (
			<>
				<Stack spacing="none" alignment="center">
					{options.new && (
						<div style={{marginRight: 10, display: 'flex'}}>
							<Badge status="new" size="small">
								{t('NEW_1ddaf')}
							</Badge>
						</div>
					)}

					<Icon source={ExternalMinor} color="base" />
				</Stack>
			</>
		),
		onClick: () => {
			goToExternalUrlHandler(productUrl);
		},
	};
}

const FLOW_LIST_FEATURES_SLUGS_LIST = [
	FeatureSlugs.All_In_One_Tracking_Notification_Billing,
];
const Navigation = () => {
	const {
		t,
		i18n: {language},
	} = useTranslation();
	const {isWineShipping} = useIsWineShipping();
	const {isWSC} = useIsWSC();
	const organizationId = useOrganizationId();
	const {isAllAvailable: isAllInOneFlowAvailable} = useBillingFeatureStatus({
		featureSlugs: FLOW_LIST_FEATURES_SLUGS_LIST,
	});
	// const {fetchUser} = props;

	const navigationBoxRef = useRef<HTMLDivElement>(null);

	const {isConfiguredProtection} = useProtectionEntryStatus();
	const protectionNavEntry = useProtectionNavigation();

	const {data: trackingPages = []} = useTrackingPagesQuery(undefined, {
		select: res => res.trackingPages.trackingPages,
	});

	const location = useLocation();
	const {pathname} = location;

	const analyticsGlobalFilterStr =
		sessionStorage?.getItem(AS_ANALYTICS_GLOBAL_FILTER) || '';

	const commonDataRangeQuery = useMemo(() => {
		const analyticsGlobalFilter = parse(analyticsGlobalFilterStr);

		return `start-date=${
			analyticsGlobalFilter?.['start-date'] || getLastDaysTick(29)
		}&end-date=${
			analyticsGlobalFilter?.['end-date'] || getLastDaysTick(0)
		}&compare-start-date=${
			analyticsGlobalFilter?.['compare-start-date'] ||
			analyticsGlobalFilter?.['compare-start-date'] === ''
				? analyticsGlobalFilter?.['compare-start-date']
				: getLastDaysTick(59)
		}&compare-end-date=${
			analyticsGlobalFilter?.['compare-end-date'] ||
			analyticsGlobalFilter?.['compare-end-date'] === ''
				? analyticsGlobalFilter?.['compare-end-date']
				: getLastDaysTick(30)
		}&compare-type=${
			analyticsGlobalFilter?.['compare-type'] || 'previousPeriod'
		}&date-range-type=${
			analyticsGlobalFilter?.['date-range-type'] || 'last 30 days'
		}&filterV2=${analyticsGlobalFilter?.filterV2 || ''}`;
	}, [analyticsGlobalFilterStr]);

	const customFieldQuery = useMemo(() => {
		const analyticsGlobalFilter = parse(analyticsGlobalFilterStr);

		return parse(analyticsGlobalFilterStr)?.['custom-field']
			? `&custom-field=${
					parse(analyticsGlobalFilterStr)?.['custom-field']
			  }&filterV2=${analyticsGlobalFilter?.filterV2 || ''}`
			: '';
	}, []);

	const notificationHistoryLast30DaysQuery = useMemo(() => {
		return `start-date=${getLastDaysTick(29)}&end-date=${getLastDaysTick(
			0
		)}`;
	}, []);

	const hasOnTimeShipment = useHasBillingAndExtraFeature(
		ANALYTICS_ON_TIME_SHIPMENT
	);

	const reviewsQuery = useMemo(() => {
		const analyticsGlobalFilter = parse(analyticsGlobalFilterStr);
		const carriersStr = analyticsGlobalFilter?.carriers
			? `&carriers=${analyticsGlobalFilter?.carriers}`
			: '';
		const ratingsStr = analyticsGlobalFilter?.ratings
			? `&ratings=${analyticsGlobalFilter?.ratings}`
			: '';
		const keywordsStr = analyticsGlobalFilter?.keywords
			? `&keywords=${analyticsGlobalFilter?.keywords}`
			: '';
		const queryStr = `${carriersStr}${ratingsStr}${keywordsStr}`;
		return queryStr;
	}, [analyticsGlobalFilterStr]);

	const history = useHistory();

	/*
	该逻辑用于处理切换org时，全局filter的处理

	需求: 在当前页面刷新时不更改filter, 切换org时，清空filter

	org切换背景:
	若当前org为 xx
	点击切换org按钮时, 当前org变为yy, 然后再变为 '' , 再变为 yy
	刷新页面时, 当前org先变为 '' , 再变为xx,

	AS_FILTER_LAST_ORG_RECORD: 用于记录上一次的org, 用于判断是否切换org

	case1:
	首次进入页面 =
	org为空 --- 且无last org值, 不进入任何判断 ->
	(org有值 --- 无current org)->(进入判断1) ,设置current org为当前org


	cast2:
	更改filter后刷新页面 =
	org为空 --- 无last org值,  不进入任何判断 ->
	org有值 --- org值和current org相等, return 不做任何操作


	case3:
	切换org =
	org 变为新org --- (存在current org 且新org !== current org)->(进入判断3), 设置last org为旧 org, 设置current org为新org ->
	org变为 '' --- 同时存在last org , 清空 last org并重置页面 ->
	org变为新org --- (新org = current org)->(进入判断2) , return 不做任何操作


	cas4:
	再次切换org-> 重复case 3


	case 5:
	切换org后刷新: 重复case 2
	*/

	const {hasPermission: hasOrdersPermission} = useOrdersPermission();

	useEffect(() => {
		const commonQuery = `start-date=${getLastDaysTick(
			29
		)}&end-date=${getLastDaysTick(0)}&compare-start-date=${getLastDaysTick(
			59
		)}&compare-end-date=${getLastDaysTick(
			30
		)}&compare-type=previousPeriod&date-range-type=last 30 days&filterV2=`;
		const currentOrg = sessionStorage.getItem(AS_FILTER_CURRENT_ORG_RECORD);
		const lastOrg = sessionStorage.getItem(AS_FILTER_LAST_ORG_RECORD);
		if (!currentOrg && organizationId) {
			sessionStorage.setItem(
				AS_FILTER_CURRENT_ORG_RECORD,
				organizationId
			);
		}

		if (organizationId && organizationId === currentOrg) {
			return;
		}
		// 有orgID(避免刷新页面时org变为''走进该逻辑), 缓存中有当前org, 切换时orgId和缓存中的不相等(用于判断是在切换org)
		if (organizationId && organizationId !== currentOrg && currentOrg) {
			// 判断是在切换org而不是在当前页面刷新的逻辑: org会在页面刷新, org变为空之前先变为 yy(即切换的org id), 此时设置标识符last org为当前的org xx, 当前org 为 yy
			sessionStorage.setItem(AS_FILTER_LAST_ORG_RECORD, currentOrg || '');
			sessionStorage.setItem(
				AS_FILTER_CURRENT_ORG_RECORD,
				organizationId
			);
		}
		// 切换org后, org变为空时 , 重置页面并把last org的缓存设为 ''
		if (!organizationId && lastOrg) {
			sessionStorage.setItem(AS_FILTER_LAST_ORG_RECORD, '');
			sessionStorage.setItem(AS_ANALYTICS_GLOBAL_FILTER, '');
			if (!pathname.includes('ens/')) {
				history.replace({
					search: commonQuery,
				});
			}
			if (pathname.includes('/carrier-business-days')) {
				history.replace({
					pathname: '/settings/carrier-business-days',
					search: '',
				});
			}
		}
	}, [organizationId]);

	// 用于报表订阅功能跳转时, 会在url中保留organization信息导致首次切换org不生效, 在org相关信息读取完成后去除org信息
	useEffect(() => {
		if (
			organizationId &&
			window.location.href.includes('?organization_id=') &&
			pathname.includes('/dashboard')
		) {
			history.replace({
				search: '',
			});
		}
	}, [window.location.href, organizationId]);

	const showAppleWalletApp = useShowAppleWallet();
	const {isShowTrackingCopilot} = useShowTrackingCopilot();

	const [
		hasTransitTime,
		hasTotalShipments,
		hasNotificationReport,
		hasTrackingPageReport,
		hasTrackingPageReviewsReport,
		hasExceptionReport,
		hasOrderToDelivery,
		hasBenchmarks,
		hasTrackingPageTrackingPopUp,
	] = useIsAvailableFeature(
		TRANSIT_TIME_REPORT_CODE,
		SHIPMENTS_REPORT,
		NOTIFICATION_REPORT,
		TRACKING_PAGE_REPORT,
		TRACKING_PAGE_REVIEWS_REPORT,
		EXCEPTION_SHIPMENTS_REPORT,
		ANALYTICS_ORDER_TO_DELIVERY,
		BENCHMARKS_DASHBOARD,
		TRACKING_PAGE_TRACKING_POP_UP
	);

	const customizeAnalyticNavItems = useCustomizeAnalyticsNavItem();
	const benchmarkSubNavigation = [
		{
			url: `/dashboard/benchmark`,
			matches: pathname.includes('/dashboard/benchmark'),
			label: t('BENCHMARKS_200d7', 'Benchmarks'),
			badge: !hasBenchmarks && (
				<span style={{marginRight: 10}}>
					<Icon source={LockMinor} color="base" />
				</span>
			),
		},
	];

	/**
	 * tracking page tracking pop up analytics for Samsung
	 * @link https://www.notion.so/automizely/Analytics-to-Samsung-Popups-2214757578d4453da23b70306e244671
	 */
	const trackingPageTrackingPopupNavItems = useMemo(() => {
		if (!hasTrackingPageTrackingPopUp) return [];

		return [
			{
				url: `/dashboard/tracking-pop-up?${commonDataRangeQuery}`,
				label: t('TRACKING_P_518a0', 'Page tracking clips'),
				matches: pathname.includes('/dashboard/tracking-pop-up'),
			},
		];
	}, [commonDataRangeQuery, hasTrackingPageTrackingPopUp, pathname, t]);

	const {routeEntries: asAnalyticsRoutes, shipmentRouteEntries} =
		useAftershipAnalytics(organizationId);
	const showAllInOneReport = useShouldShowAllInOneReport();

	const shipmentAnalyticsSubNavigation = useMemo(() => {
		// show all in one dashboards
		if (showAllInOneReport && shipmentRouteEntries.length > 0) {
			return shipmentRouteEntries.map(route => ({
				url: route.url,
				label: route.label,
				// listen to the location or search string change
				matches: route.matches(),
				badge: route.badge,
			}));
		}

		return [
			{
				url: `/dashboard/shipment?${commonDataRangeQuery}`,
				label: t('SHIPMENTS_1ac8f'),
				// @ts-ignore
				badge: !hasTotalShipments && (
					<span style={{marginRight: 10}}>
						<Icon source={LockMinor} color="base" />
					</span>
				),
				matches:
					['/dashboard/shipment', 'reports/shipment'].some(path =>
						pathname.includes(path)
					) && !pathname.includes('/dashboard/shipment-reviews'),
			},
			{
				url: `/dashboard/order-to-delivery?${commonDataRangeQuery}`,
				label: t('ORDER_TO_D_1ac8f', 'Order-to-delivery time'),
				// @ts-ignore
				badge: !hasOrderToDelivery && (
					<span style={{marginRight: 10}}>
						<Icon source={LockMinor} color="base" />
					</span>
				),
				matches: ['/dashboard/order-to-delivery'].some(path =>
					pathname.includes(path)
				),
			},
			{
				url: `/dashboard/transit-time?${commonDataRangeQuery}`,
				label: t('TRANSIT_T_57a29'),
				// @ts-ignore
				badge: !hasTransitTime && (
					<span style={{marginRight: 10}}>
						<Icon source={LockMinor} color="base" />
					</span>
				),
				matches: ['/dashboard/transit-time'].some(path =>
					pathname.includes(path)
				),
			},
			{
				url: `/dashboard/on-time-shipment?${commonDataRangeQuery}`,
				label: t('ON_TIME_S_739d9'),
				badge: !hasOnTimeShipment && (
					<span style={{marginRight: 10}}>
						<Icon source={LockMinor} color="base" />
					</span>
				),
			} as {url: string; label: string},
			{
				url: `/dashboard/exception?${commonDataRangeQuery}`,
				label: t('EXCEPTIONS_02291', {
					defaultValue: 'Exceptions',
				}),
				// @ts-ignore
				badge: !hasExceptionReport && (
					<span style={{marginRight: 10}}>
						<Icon source={LockMinor} color="base" />
					</span>
				),
				matches: pathname.startsWith('/dashboard/exception'),
			},
			{
				url: `/dashboard/notification?${commonDataRangeQuery}${customFieldQuery}`,
				label: t('NOTIFICAT_02a2e'),
				// @ts-ignore
				badge: !hasNotificationReport && (
					<span style={{marginRight: 10}}>
						<Icon source={LockMinor} color="base" />
					</span>
				),
			},
		];
	}, [
		showAllInOneReport,
		shipmentRouteEntries,
		commonDataRangeQuery,
		t,
		hasTotalShipments,
		pathname,
		location,
		hasOrderToDelivery,
		hasTransitTime,
		hasOnTimeShipment,
		hasExceptionReport,
	]);

	const isAllInOne = useIsAllinoneEmail();

	const trackingPagePreviewUrl = useTrackingPageBaseURL();

	const {hasPermissions: hasTrackWithPayPalPermissions} =
		useTrackWithPayPalPermissions();

	const [hideTrackingClipsBadge, setHideTrackingClipsBadge] =
		useLocalStorageWithOrgId(HIDE_TRACKING_CLIPS_NEW_FEATURE_BADGE, false);

	const genNavigation = useMemo(() => {
		const navigationList: NavigationItemProps[] = [
			{
				url: '/',
				label: t('HOME_f9390'),
				icon: HomeMajor,
				exactMatch: true,
			},
			{
				url: '/shipments',
				label: t('TRACKING_D_76ee6', 'Tracking dashboards'),
				icon: ShipmentMajor,
				subNavigationItems: hasOrdersPermission
					? [
							{
								label: t('SHIPMENTS_06d95', 'Shipments'),
								url: '/shipments',
							},
							{
								url: '/orders',
								label: t('ORDERS_T_8a3ee', 'Orders'),
							},
					  ]
					: [],
			},
			{
				url: '/tracking-pages',
				label: (
					<div
						style={{
							display: 'inline-flex',
							justifyContent: 'space-between',
							alignItems: 'center',
						}}
					>
						{t('TRACKING_P_e515c')}
						{trackingPages.length ? (
							<div
								onClick={() => {
									window.open(
										trackingPagePreviewUrl,
										'_blank'
									);
								}}
								tabIndex={0}
								style={{
									padding: '0px 16px',
								}}
								role="button"
							>
								<Icon
									accessibilityLabel={t('VIEW_TRAC_43e58')}
									source={ViewMinor}
									color="base"
								/>
							</div>
						) : null}
					</div>
				) as unknown as string,
				icon: OnlineStoreMajor,
				subNavigationItems: [
					{
						url: '/tracking-pages',
						label: t('PAGES_VARI_e7418'),
					},
					{
						url: '/segmentation',
						label: t('SEGMENTATI_4a2b3'),
						matchPaths: ['/segments'],
					},
					{
						url: '/urls-and-domain',
						label: t('UR_LS_AND_D_32ec1', 'URLs and domain'),
					},
					{
						url: '/embedded-pages',
						label: t('EMBEDDING_9eda4'),
					},
					{
						url: '/track-button',
						label: t('ORDER_LOOK_ce36c', 'Order lookup widget'),
					},
					{
						url: '/tracking-clips',
						label: t('TRACKING_C_dab3a', 'Tracking clips'),
						new: !hideTrackingClipsBadge,
					},
				],
			},
			{
				url: isAllInOne ? '/ens/flows' : '/notifications/setting',
				label: t('NOTIFICAT_36d43'),
				icon: EmailMajor,
				exactMatch: true,
				badge: <div id="navigation-notifications" />,
				subNavigationItems: [
					...(isAllInOne
						? [
								{
									url: '/ens/flows',
									label: 'Flows',
									matches: [
										'/ens/flows',
										'/ens/flow-templates',
									].some(path => pathname.includes(path)),
									// @ts-ignore
									badge: !isAllInOneFlowAvailable ? (
										<span style={{marginRight: 10}}>
											<Icon
												source={LockMinor}
												color="subdued"
											/>
										</span>
									) : undefined,
								},
								{
									url: '/ens/custom-templates',
									label: 'Email templates',
								},
						  ]
						: [
								{
									url: '/notifications/setting',
									label: t('EMAILS_AND_367c6'),
								},
						  ]),

					{
						url: '/notifications/webhooks',
						label: t('WEBHOOKS_49ead'),
					},
					// @ts-ignore
					{
						url: `/notifications/record?page=1&${notificationHistoryLast30DaysQuery}`,
						label: t('HISTORY_034f2'),
					},
				],
			},
			{
				url: `/dashboard/shipment?${
					!showAllInOneReport ? commonDataRangeQuery : ''
				}`,
				label: t('ANALYTICS_9af3c'),
				icon: AnalyticsMajor,
				matches: ['/dashboard', '/reports', '/analytiker'].some(path =>
					pathname.startsWith(path)
				),
				// force match for the subNavigate
				subNavigationItems: [
					...shipmentAnalyticsSubNavigation,
					{
						url: `/dashboard/tracking-page?${commonDataRangeQuery}`,
						label: t('TRACKING_c9817'),
						matches: [
							'/dashboard/tracking-page',
							'reports/tracking-page',
						].some(path => pathname.includes(path)),
						// @ts-ignore
						badge: !hasTrackingPageReport && (
							<span style={{marginRight: 10}}>
								<Icon source={LockMinor} color="base" />
							</span>
						),
					},
					...trackingPageTrackingPopupNavItems,
					{
						url: `/dashboard/shipment-reviews?${commonDataRangeQuery}${reviewsQuery}`,
						matches: pathname.includes(
							'/dashboard/shipment-reviews'
						),
						label: t('SHIPMENT_cba89'),
						// @ts-ignore
						badge: !hasTrackingPageReviewsReport && (
							<span style={{marginRight: 10}}>
								<Icon source={LockMinor} color="base" />
							</span>
						),
					},
					...asAnalyticsRoutes.map(route => ({
						url: route.url,
						label: route.label,
						matches: route.matches(),
						badge: route.badge,
					})),
					...benchmarkSubNavigation,
					...customizeAnalyticNavItems,
				],
			},
			{
				url: '/apps',
				label: t('APPS_00c1c'),
				icon: AppsMajor,
			},
		];

		if (protectionNavEntry) navigationList.splice(2, 0, protectionNavEntry);

		return navigationList;
	}, [
		t,
		hasOrdersPermission,
		trackingPages.length,
		trackingPagePreviewUrl,
		hideTrackingClipsBadge,
		isAllInOne,
		isAllInOneFlowAvailable,
		notificationHistoryLast30DaysQuery,
		showAllInOneReport,
		commonDataRangeQuery,
		shipmentAnalyticsSubNavigation,
		customFieldQuery,
		hasNotificationReport,
		hasTrackingPageReport,
		trackingPageTrackingPopupNavItems,
		reviewsQuery,
		pathname,
		hasTrackingPageReviewsReport,
		asAnalyticsRoutes,
		benchmarkSubNavigation,
		customizeAnalyticNavItems,
		protectionNavEntry,
	]);

	const hideAddOnsAll = useWhiteOrg('hide_add_ons_all');
	const hideAddOnsEDD = useWhiteOrg('hide_add_ons_edd');
	const hideAddOnsGreen = useWhiteOrg('hide_add_ons_green');
	const hideAddOnsTrackingApp = useWhiteOrg('hide_add_ons_tracking_app');
	const hideAddOnsAppleOrderTracking = useWhiteOrg(
		'hide_add_ons_apple_order_tracking'
	);
	const hideAddOnsCopilot = useWhiteOrg('hide_add_ons_copilot');

	const isHideAddOns = isWSC || hideAddOnsAll;
	const isHideAddOnsEDD = hideAddOnsEDD;
	const isHideAddOnsGreen = hideAddOnsGreen;
	const isHideAddOnsTrackingApp = hideAddOnsTrackingApp;
	const isHideAddOnsAppleOrderTracking =
		hideAddOnsAppleOrderTracking || !showAppleWalletApp;
	const isHideCopilot = hideAddOnsCopilot || !isShowTrackingCopilot;

	/** TODO 临时针对canva 屏蔽carbon footprint. 后面交给opp维护 */

	let addonsNav = [
		...(isHideAddOnsEDD
			? []
			: [
					{
						url: '/add-ons/post-purchase-edd',
						label: t('AI_PREDICTI_5fffe'),
						icon: IconEdd,
						subNavigationItems: [
							{
								url: '/add-ons/post-purchase-edd',
								label: t('POST_PURCH_341ea'),
							},
							{
								url: '/add-ons/pre-purchase-edd',
								label: t('PRE_PURCHA_5c8e9'),
							},
						],
					},
			  ]),
		...(isHideAddOnsGreen
			? []
			: [
					{
						url: '/add-ons/integrations/carbon-emissions-report',
						label: t('CARBON_FOOT_732b2'),
						icon: IconGreen,
					},
			  ]),
		...(isHideAddOnsTrackingApp
			? []
			: [
					{
						url: '/add-ons/tracking-app',
						label: t('TRACKING_AP_d7796'),
						icon: IconTracking,
					},
			  ]),
		...(isHideAddOnsAppleOrderTracking
			? []
			: [
					{
						url: '/add-ons/apple-wallet-order-tracking',
						label: t('APPLE_ORDER_84469'),
						icon: IconAppleWallet,
					},
			  ]),
		...(hasTrackWithPayPalPermissions
			? [
					{
						url: '/add-ons/paypal-package-tracking',
						label: t(
							'PAY_PAL_PAC_84d68',
							'PayPal Package tracking'
						),
						icon: IconPaypal,
					},
			  ]
			: []),
	];

	if (isWineShipping) {
		addonsNav = [
			{
				url: '/add-ons/post-purchase-edd',
				label: t('AI_PREDICTI_5fffe'),
				icon: IconEdd,
				subNavigationItems: [
					{
						url: '/add-ons/post-purchase-edd',
						label: t('POST_PURCH_341ea'),
					},
				],
			},
		];
	}

	const {
		isOnboardingV3,
		isFetched,
		isHideGuide,
		progress: progressV3,
		disableGuide,
	} = useOrganizationOnboarding();

	const guidancePosition =
		isFetched && !isHideGuide && !(progressV3 >= 100) ? 'top' : 'bottom';

	// 由于 secondary action 没有 click 事件监听，需要通过事件冒泡进行事件捕获
	const handleSecondaryNavigationClick = (
		e: React.MouseEvent<HTMLDivElement>
	) => {
		// 获取当前点击的 dom
		const currentDom = e.target as HTMLDivElement;

		// 获取顶层 dom
		const boxDom = navigationBoxRef?.current;
		if (!boxDom) {
			return;
		}

		{
			// 获取 tracking page preview 的按钮 dom
			const trackingPagePreviewIconDom = boxDom.querySelector(
				`[href="${trackingPagePreviewUrl}"]`
			);

			// 判断是否点击了 tracking page preview
			const isClickTrackingPagePreview =
				trackingPagePreviewIconDom?.contains(currentDom);

			// 上报 click 事件
			if (isClickTrackingPagePreview) {
				gaClick('E10275', {
					extraParams: {
						page_sn: 'P10005',
						page_st_sn: 'S10210',
						page_desc: 'Tracking page',
						page_st_desc: 'Page variations',
						page_el_sn: 'E10275',
						page_el_desc: 'Preview icon',
					},
				});
			}
		}

		{
			// 获取 tracking clips 的按钮 dom
			const trackingClipsNavDom = boxDom.querySelector(
				`[href="/tracking-clips"]`
			);

			// 判断是否点击了 tracking clips nav
			const isClickTrackingPagePreview =
				trackingClipsNavDom?.contains(currentDom);

			isClickTrackingPagePreview && setHideTrackingClipsBadge(true);
		}
	};

	const onboardingInited = useOnboardingInited();
	const {data: connections} = useImportingConnections({refetchOnMount: true});
	const {data: connectionsJobsData} = useTrackingConnectionJob();
	const isAllinone = useIsAllinoneEmail();

	const guidance = onboardingInited ? (
		<TasksMini
			useModal={useModal}
			openLiveChat={openLiveChat}
			connections={connections}
			connectionsJobs={connectionsJobsData?.connectionsJobs}
			hasTrackingPage={trackingPages?.length > 0}
			isNewFlows={isAllinone}
		/>
	) : null;

	// if (!disableGuide && isOnboardingV3 && guidancePosition === 'top') {
	// 	guidance = <Guidance />;
	// }

	return (
		<div className="navigation-container" id="navigation-container">
			<PolarisNavigation location={pathname}>
				<div id="navigation-main-feature">
					{guidance}
					<div
						role="button"
						tabIndex={0}
						ref={navigationBoxRef}
						onClick={handleSecondaryNavigationClick}
					>
						<PolarisNavigation.Section
							// @ts-ignore
							location={pathname}
							rollup={{
								after: 10,
								view: 'view',
								hide: 'hide',
								activePath: '/',
							}}
							key={`protection-config-${isConfiguredProtection}`}
							items={genNavigation}
						/>
					</div>
					{isHideAddOns ? null : (
						<PolarisNavigation.Section
							title="ADD-ONS"
							items={addonsNav}
						/>
					)}
				</div>
				<div
					id="navigation-helpCenter"
					style={{
						marginTop: 'auto',
						transform: 'translateY(14px)',
					}}
				>
					{!disableGuide &&
					(!isOnboardingV3 || guidancePosition !== 'top') ? (
						<PolarisNavigation.Section
							items={[
								{
									url: '/guidance',
									label: (
										<GuidanceIndication
											hideProgressBar={isOnboardingV3}
										/>
									) as unknown as string,
									icon: HintMajor,
									new: false,
								},
							]}
						/>
					) : null}
					{onboardingInited ? <NavigationItem /> : null}
				</div>
				<div
					id="navigation-settings"
					style={{
						position: 'relative',
					}}
				>
					<PolarisNavigation.Section
						items={[
							IdeaPortalEntryConfig({
								productCode: 'aftership',
								// @ts-ignore
								language: language,
								color: 'base',
							}),
							!isHideCopilot
								? {
										url: '/tracking-copilot',
										label: (
											<Stack
												spacing="extraTight"
												alignment="center"
											>
												<span>
													{t('COPILOT_bb9ba')}
												</span>
												<Tooltip
													content={
														<div
															style={{
																maxWidth:
																	'25rem',
															}}
														>
															{t(
																'THE_AI_ASS_47de0'
															)}
														</div>
													}
												>
													<Badge>
														{t('BETA_0b87d')}
													</Badge>
												</Tooltip>
											</Stack>
										) as unknown as string,
										icon: IconAIGrey,
								  }
								: getExtraProductItem(
										{
											url: 'https://support.aftership.com',
											label: t('HELP_1ac8f'),
											icon: QuestionMarkMajor,
											new: false,
											selected: false,
											organizationId: organizationId,
										},
										t
								  ),
							{
								url: '/settings',
								label: t('SETTINGS_1ac8f'),
								icon: SettingsMajor,
							},
						].filter(Boolean)}
					/>
				</div>
			</PolarisNavigation>
			<Popup selector="#nav-apps-anchor" />
		</div>
	);
};

export default function NavigationWrapper() {
	return (
		<ApolloProvider client={automizelyClient}>
			<Navigation />
		</ApolloProvider>
	);
}
