// @ts-nocheck
import React, {useMemo} from 'react';
import {useTranslation} from 'react-i18next';

import {CouponType} from 'constants/billings/coupon';
import {campaignConfig, CampaignType} from 'constants/campaignType';
import {useGetCouponConfig} from 'hooks/billings/useSubscription';
import {useCountDown, useGetStartTime} from 'hooks/useCountDown';

import styles from './CountDownClock.module.scss';

const CountDownClock = ({
	position,
	couponType = CouponType.trialEndCoupon,
	campaignType = CampaignType.planDiscountCampaign,
	campaignStartTime,
}: {
	position: string;
	couponType?: CouponType;
	campaignType?: CampaignType;
	campaignStartTime?: any;
}) => {
	const {t} = useTranslation();
	const startTime = useGetStartTime();
	const {duration} = useGetCouponConfig(couponType);
	const {campaignDuration} = campaignConfig;

	const countDownConfig = useMemo(() => {
		switch (campaignType) {
			default:
				return {duration: duration, startTime: startTime};
		}
	}, [
		campaignDuration,
		campaignStartTime,
		campaignType,
		duration,
		startTime,
	]);

	const {hour, minute, second, milSec} = useCountDown(
		countDownConfig as {startTime: any; duration: number}
	);

	return (
		<>
			<div
				className={styles[position]}
				style={{
					display: 'flex',
					marginTop: '8px',
					alignItems: 'baseline',
				}}
			>
				<div>
					<div className={styles.countDown}>{hour}</div>
					<div className={styles.unit}>{t('HOURS_2ed6d')}</div>
				</div>

				<div className={styles.colon}>:</div>
				<div>
					<div className={styles.countDown}>{minute}</div>
					<div className={styles.unit}>{t('MINS_c213a')}</div>
				</div>

				<div className={styles.colon}>:</div>
				<div>
					<div className={styles.countDownSec}>
						<div className={styles.secLeft}>{second}</div>
						<div>.</div>
						<div className={styles.secRight}>{milSec}</div>
					</div>
					<div className={styles.unit}>{t('SECS_b90dc')}</div>
				</div>
			</div>
		</>
	);
};

export default CountDownClock;
