import {Operator} from 'pages/Shipments/interfaces/Filter';

export const getOperatorDescription = (operator: Operator) => {
	switch (operator) {
		case Operator.IS_ANY_OF:
			return 'is any of';

		case Operator.IS_NONE_OF:
			return 'is none of';

		case Operator.CONTAINS_ANY_OF:
			return 'contains any of';

		case Operator.NOT_CONTAINS_ANY_OF:
			return 'contains none of';

		default:
			return '';
	}
};
