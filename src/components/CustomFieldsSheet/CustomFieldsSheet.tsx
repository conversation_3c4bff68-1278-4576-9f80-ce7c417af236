import {useGetPlanTrial} from 'aftershipBillingUi';
import {
	<PERSON>ton,
	Link,
	Scrollable,
	Sheet,
	Spinner,
	TextStyle,
	Icon,
} from '@shopify/polaris';
import {ChevronLeftMinor, MobileCancelMajor} from '@shopify/polaris-icons';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {
	Tracking_Analytics_Facets_Include_Field,
	Tracking_Facets_Include_Field,
	useTrackingAnalyticsFacetsQuery,
	useCompanyTrackingAnalyticsFacetsQuery,
	useTrackingFacetsQuery,
	useCompanyTrackingFacetsQuery,
} from '@graphql/generated';
import FeatureStatusBadge from 'components/FeatureStatusBadge';
import {CUSTOM_TRACKING_FILTERING} from 'constants/billings/features';
import {useFeatureValidator} from 'hooks/billings';
import {useIsEnterprise} from 'hooks/billings/useChoosePlan';
import {usePlanTrialAvailable} from 'hooks/billings/usePlanTrialAvailable';
import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {GetTrackingsProps} from 'pages/Shipments/interfaces';
import {BasicFilterData} from 'pages/Shipments/interfaces/Filter';
import {useAllCompanyOrganizationsQuery} from 'services/company';
import {toOrgMoment} from 'utils/day';
import {gaClick} from 'utils/gtag';
import isCompany from 'utils/isCompany';

import {CustomFieldSelect} from './components/CustomFieldSelect';
import {FieldCard} from './components/FieldCard';
import {useMultipleCustomFields} from './hooks/customFieldHooks';
import style from './index.module.scss';

interface Props {
	// TODO:有空将下面两个参数合并为type
	isSubscription?: boolean;
	isShipment?: boolean;
	open: boolean;
	title: string;
	onClose: () => void;
	query:
		| ReturnType<typeof transformTrackingAnalyticsQuery>['query']
		| GetTrackingsProps
		| any;
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
}
const CustomFieldsSheet = ({
	isSubscription,
	isShipment,
	open,
	title,
	onClose,
	query,
	onChangeQuery,
}: Props) => {
	const {t} = useTranslation();

	const {advanceFilterItem} = useMultipleCustomFields({
		isShipment: isShipment,
		query: query,
		onChangeQuery: onChangeQuery,
		targetPath: 'customFilter',
		propertyKey: 'custom_fields_object',
	});

	const isEnterprise = useIsEnterprise();

	const [openFeatureLockModal] = useFeatureValidator(
		CUSTOM_TRACKING_FILTERING
	);
	const canUsePlanTrial = usePlanTrialAvailable();

	const {planTrials} = useGetPlanTrial();

	const canCreatePlanTrial =
		(planTrials || []).length <= 0 && canUsePlanTrial;

	const [enabledCustomFieldsList, setEnabledCustomFieldsList] = useState<
		string[]
	>(
		// eslint-disable-next-line array-callback-return, consistent-return
		advanceFilterItem
			?.map((item: BasicFilterData) => {
				if (item?.propertyName?.includes('custom_fields_object')) {
					return item?.propertyName?.split('.')?.[1];
				}
				return undefined;
			})
			.filter((item: string | undefined) => item !== undefined)
	);

	const [, available] = useFeatureValidator(
		CUSTOM_TRACKING_FILTERING,
		() => {}
	);
	const {minDate} = useDatePickerRangeOptions();

	const {data: allOrganizations} = useAllCompanyOrganizationsQuery({
		enabled: isCompany(),
	});
	const allOrganizationIds = allOrganizations?.map(org => org.id) || [];

	let shipmentCustomFieldsEnabled = false;
	if (isShipment) {
		shipmentCustomFieldsEnabled = isCompany()
			? allOrganizationIds?.length > 0
			: true;
	}
	const {
		data: shipmentCustomFieldsKeys,
		isLoading: shipmentCustomFieldsKeysLoading,
	} = (isCompany() ? useCompanyTrackingFacetsQuery : useTrackingFacetsQuery)(
		{
			includeField: Tracking_Facets_Include_Field.CustomFieldsKey,
			//  解耦展示所有custom_field
			filter: {
				...(isCompany()
					? {
							organizationIds: allOrganizationIds || [],
					  }
					: {}),
			},
			...(isCompany() ? {useV3API: true} : {}),
		},
		{
			enabled: shipmentCustomFieldsEnabled,
			select: (res: any) => {
				const resKey = isCompany()
					? 'companyTrackingFacets'
					: 'trackingFacets';

				return res?.[resKey];
			},
		}
	);

	let analyticsCustomFieldsEnabled = false;
	if (!isShipment) {
		analyticsCustomFieldsEnabled = isCompany()
			? allOrganizationIds?.length > 0
			: true;
	}
	const {
		data: analyticsCustomFieldsKeys,
		isLoading: analyticsCustomFieldsKeysLoading,
	} = (
		isCompany()
			? useCompanyTrackingAnalyticsFacetsQuery
			: useTrackingAnalyticsFacetsQuery
	)(
		{
			includeField:
				Tracking_Analytics_Facets_Include_Field.CustomFieldsKey,
			//  解耦展示所有custom_field
			// @ts-ignore
			filter: {
				startTime: toOrgMoment(minDate)
					.add('1', 'day')
					.startOf('day')
					.format(),
				endTime: toOrgMoment().endOf('day').format(),
				...(isCompany()
					? {
							organizationIds: allOrganizationIds || [],
					  }
					: {}),
			},
		},
		{
			enabled: analyticsCustomFieldsEnabled,
			select: (res: any) => {
				const resKey = isCompany()
					? 'companyTrackingAnalyticsFacets'
					: 'trackingAnalyticsFacets';

				return res?.[resKey];
			},
		}
	);

	const customDescriptionDom = () => {
		if (!available) {
			return (
				<>
					<div style={{marginBottom: '16px'}}>
						<TextStyle variation="strong">
							{t(
								'FILTERUSIN_3b20e',
								'Filter using your custom fields'
							)}
						</TextStyle>
					</div>
					<TextStyle>
						{t(
							'CUSTOMFIEL_0f03d',
							'Custom fields let you add extra info to shipments, and can be used as filters for your shipments dashboard and analytics. You can add custom fields on the shipments dashboard or when importing shipments by API.'
						)}
					</TextStyle>
					<Link
						external
						url="https://www.aftership.com/docs/tracking/9870f0fa51370-create-a-tracking"
					>
						{t('LEARN_MORE_d5904', 'Learn more')}
					</Link>

					<div style={{marginTop: '8px'}}>
						<Button
							primary
							onClick={() => {
								openFeatureLockModal();
							}}
						>
							{canCreatePlanTrial && !isEnterprise
								? t('START_PREM_10d30')
								: t('UPGRADE_AN_68826')}
						</Button>
					</div>
				</>
			);
		}

		if (
			shipmentCustomFieldsKeysLoading ||
			analyticsCustomFieldsKeysLoading
		) {
			return <Spinner />;
		}

		if (
			available &&
			// 没有custom_fieldKey
			!(
				(isShipment &&
					shipmentCustomFieldsKeys?.customFields?.length) ||
				(!isShipment && analyticsCustomFieldsKeys?.customFields?.length)
			)
		) {
			return (
				<>
					<div style={{marginBottom: '16px'}}>
						<TextStyle variation="strong">
							{t('NOCUSTOMFI_38db0', 'No custom fields yet')}
						</TextStyle>
					</div>
					<TextStyle>
						{t(
							'CUSTOMFIEL_e6a4e',
							'Custom fields let you add extra info to shipments, and can be used as filters for your shipments dashboard and analytics. You can add custom fields on the shipments dashboard or when importing shipments by API.'
						)}
					</TextStyle>
					<Link
						external
						url="https://www.aftership.com/docs/tracking/9870f0fa51370-create-a-tracking"
					>
						{t('LEARN_MORE_d5904', 'Learn more')}
					</Link>
				</>
			);
		}

		return (
			<TextStyle>
				{t('NOCUSTOMFI_2a7de', 'No custom field added yet')}
			</TextStyle>
		);
	};

	useEffect(() => {
		gaClick('E10260');
	}, []);

	return (
		<Sheet open={open} onClose={onClose} accessibilityLabel="">
			{/* <WarnAboutUnsavedChanges dirty /> */}
			<div
				style={{
					alignItems: 'center',
					borderBottom: '1px solid #E1E3E5',
					display: 'flex',
					justifyContent: 'space-between',
					padding: '16px 20px',
					fontSize: '16px',
				}}
			>
				<div style={{display: 'flex'}}>
					{isSubscription && (
						<div
							onClick={onClose}
							style={{cursor: 'pointer', marginRight: '4px'}}
							role="button"
							tabIndex={0}
						>
							<Icon source={ChevronLeftMinor} />
						</div>
					)}
					<div style={{marginRight: '4px'}}>
						<TextStyle variation="strong">{title}</TextStyle>
					</div>

					{/* CUSTOM_TRACKING_FILTERING Feature Code 只在 Premium Plan 支持，Enterprise 不支持 */}
					{/* 临时简单处理，在 Trial Features 的情况下，FeatureStatusBadge 没相关处理 */}
					{available ? null : (
						<FeatureStatusBadge
							featureCode={CUSTOM_TRACKING_FILTERING}
						/>
					)}
				</div>
				<Button
					accessibilityLabel="Cancel"
					icon={MobileCancelMajor}
					onClick={onClose}
					plain
				/>
			</div>
			<div className={style.scrollable}>
				{enabledCustomFieldsList.length ? (
					<Scrollable style={{padding: '16px 20px', height: '100%'}}>
						{enabledCustomFieldsList?.map(label => {
							return (
								<div key={label}>
									<FieldCard
										isSubscription={isSubscription}
										isShipment={isShipment}
										query={query}
										onChangeQuery={onChangeQuery}
										customFieldLabel={label}
										enabledCustomFieldsList={
											enabledCustomFieldsList
										}
										setEnabledCustomFieldsList={
											setEnabledCustomFieldsList
										}
									/>
								</div>
							);
						})}
					</Scrollable>
				) : (
					<div
						style={{
							width: '100%',
							height: '100%',
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center',
							flexDirection: 'column',
							padding: '20px',
							textAlign: 'center',
						}}
					>
						{customDescriptionDom()}
					</div>
				)}
			</div>
			{/* 在shipment没有custom */}
			{(isShipment && !shipmentCustomFieldsKeys?.customFields?.length) ||
			// 在analytics 没有custom
			(!isShipment && !analyticsCustomFieldsKeys?.customFields?.length) ||
			// 没有权限并且没有已有的custom
			(!available && !enabledCustomFieldsList.length) ? (
				<></>
			) : (
				<div
					style={{
						borderTop: '1px solid #E1E3E5',
						padding: '16px 20px',
					}}
				>
					<CustomFieldSelect
						isSubscription={isSubscription}
						isShipment={isShipment}
						query={query}
						onChangeQuery={onChangeQuery}
						enabledCustomFieldsList={enabledCustomFieldsList}
						setEnabledCustomFieldsList={setEnabledCustomFieldsList}
					/>
				</div>
			)}
		</Sheet>
	);
};
export default CustomFieldsSheet;
