/* eslint-disable no-nested-ternary */
import {cloneDeep, findIndex, isNil, set} from 'lodash';

import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {GetTrackingsProps} from 'pages/Shipments/interfaces';
import {BasicFilterData, Operator} from 'pages/Shipments/interfaces/Filter';

export const useMultipleCustomFields = ({
	isShipment,
	query,
	targetPath,
	propertyKey,
	onChangeQuery,
}: {
	isShipment?: boolean;
	query:
		| ReturnType<typeof transformTrackingAnalyticsQuery>['query']
		| GetTrackingsProps;
	targetPath: string;
	propertyKey: string;
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
}) => {
	const advanceFilter = query?.filterV2
		? !isShipment
			? JSON.parse(query.filterV2 as string)
			: query.filterV2
		: {
				basicFilter: [],
				combineFilter: [],
				customFilter: [],
		  };
	const defaultAdvanceFilterItem = {
		propertyName: '',
		operator: Operator.IS_ANY_OF,
		values: [],
	};
	const clonedAdvanceFilter = cloneDeep(
		query?.filterV2
			? !isShipment
				? JSON.parse(query.filterV2 as string)
				: query.filterV2
			: {
					basicFilter: [],
					combineFilter: [],
					customFilter: [],
			  }
	);

	const advanceFilterItem = advanceFilter?.[targetPath] || [];

	const clonedAdvanceFilterItem = clonedAdvanceFilter?.[targetPath];

	const advanceFilterItemForPropertyKey = advanceFilterItem.filter(
		(item: BasicFilterData) => {
			return item?.propertyName?.includes(propertyKey);
		}
	);
	const deleteTargetTypeAdvanceFilterItem = (propertyLabel: string) => {
		const currentItemIndex = findIndex(
			advanceFilterItem,
			(item: BasicFilterData) =>
				item?.propertyName?.split('.')?.[1] === propertyLabel
		);

		clonedAdvanceFilter?.[targetPath]?.splice(currentItemIndex, 1);

		onChangeQuery({
			filterV2: isShipment
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const clearTargetTypeAdvanceFilter = () => {
		set(clonedAdvanceFilter, targetPath, []);

		onChangeQuery({
			filterV2: isShipment
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const createTargetTypeAdvanceFilter = ({
		propertyLabel,
		operator,
	}: {
		propertyLabel: string;
		operator: Operator;
	}) => {
		clonedAdvanceFilterItem?.push({
			propertyName: `${propertyKey}.${propertyLabel}`,
			operator: operator,
		});

		onChangeQuery({
			filterV2: isShipment
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const getInitialAdvanceFilterItem = (propertyLabel: string) => {
		return (
			advanceFilter[targetPath].filter((item: BasicFilterData) => {
				return item?.propertyName?.split('.')?.[1] === propertyLabel;
			})?.[0] || defaultAdvanceFilterItem
		);
	};

	const changeAdvanceFilterItemKeyValue = ({
		propertyLabel,
		currentOperator,
		currentValues,
	}: {
		propertyLabel: string;
		currentOperator?: Operator;
		currentValues?: string[];
	}) => {
		const currentItemIndex = findIndex(
			advanceFilterItem,
			(item: BasicFilterData) =>
				item?.propertyName?.split('.')?.[1] === propertyLabel
		);

		if (currentItemIndex !== -1) {
			if (currentOperator) {
				clonedAdvanceFilterItem[currentItemIndex].operator =
					currentOperator;
			}
			if (currentValues) {
				clonedAdvanceFilterItem[currentItemIndex].values =
					currentValues;
			}
		}

		onChangeQuery({
			filterV2: isShipment
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const getResultValues = (
		propertyLabel: string,
		currentHandleOptionValue: string
	) => {
		if (!isNil(currentHandleOptionValue)) {
			let resultChoices = [];
			const currentValues =
				getInitialAdvanceFilterItem(propertyLabel).values || [];

			resultChoices = !currentValues?.includes(currentHandleOptionValue)
				? [...currentValues, currentHandleOptionValue]
				: currentValues?.filter((item: string) => {
						return item !== currentHandleOptionValue;
				  });
			return resultChoices;
		}
		return [];
	};

	return {
		advanceFilterItem,
		advanceFilterItemForPropertyKey,
		getResultValues,
		deleteTargetTypeAdvanceFilterItem,
		clearTargetTypeAdvanceFilter,
		createTargetTypeAdvanceFilter,
		getInitialAdvanceFilterItem,
		changeAdvanceFilterItemKeyValue,
	};
};
