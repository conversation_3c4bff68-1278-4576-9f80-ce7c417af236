/* eslint-disable jsx-a11y/no-static-element-interactions */
import {
	<PERSON><PERSON>,
	I<PERSON>,
	OptionList,
	Popover,
	Stack,
	Tag,
	TextStyle,
} from '@shopify/polaris';
import {DeleteMinor} from '@shopify/polaris-icons';
import {set, without} from 'lodash';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useDebouncedCallback} from 'use-debounce';

import {
	TrackingAnalyticsFilterInput,
	Tracking_Analytics_Sub_Facets_Include_Field,
	Tracking_Analytics_Sub_Facets_Parent_Field,
	Tracking_Sub_Facets_Include_Field,
	Tracking_Sub_Facets_Parent_Field,
	useTrackingAnalyticsSubFacetsQuery,
	useCompanyTrackingAnalyticsSubFacetsQuery,
	useTrackingSubFacetsV2Query,
	useCompanyTrackingSubFacetsV2Query,
} from '@graphql/generated';
import {MultiTextField} from 'components/MultiTextField';
import {FilterPropertyMap, OperatorMap} from 'constants/ShipmentFilters';
import {CUSTOM_TRACKING_FILTERING} from 'constants/billings/features';
import {useFeatureValidator} from 'hooks/billings';
import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {MultiChoiceList} from 'pages/Shipments/components/ShipmentFilter/AdvancedFilters/components/MultiFilter/MultiChoiceList';
import {
	FilterData,
	FilterPropertyName,
	Operator,
} from 'pages/Shipments/interfaces/Filter';
import {useAllCompanyOrganizationsQuery} from 'services/company';
import {toOrgMoment} from 'utils/day';
import isCompany from 'utils/isCompany';
import {numberWithCommas} from 'utils/number';
import {getCharCode} from 'utils/string';

import {useMultipleCustomFields} from '../hooks/customFieldHooks';
import {getOperatorDescription} from '../utils/analyticsUtils';

const OPERATOR_GROUPS = [
	[Operator.IS_ANY_OF, Operator.IS_NONE_OF],
	[Operator.CONTAINS_ANY_OF, Operator.NOT_CONTAINS_ANY_OF],
];

interface Props {
	isShipment?: boolean;
	isSubscription?: boolean;
	customFieldLabel: string;
	query:
		| ReturnType<typeof transformTrackingAnalyticsQuery>['query']
		| FilterData
		| any;
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
	enabledCustomFieldsList: any;
	setEnabledCustomFieldsList: any;
}

export const FieldCard = ({
	isShipment,
	isSubscription,
	customFieldLabel,
	query,
	onChangeQuery,
	enabledCustomFieldsList,
	setEnabledCustomFieldsList,
}: Props) => {
	const {t} = useTranslation();

	const [popoverActive, setPopoverActive] = useState(false);
	const [operationSelectActive, setOperationSelectActive] = useState(false);
	const [searchKeyword, setSearchKeyword] = useState('');

	const multiTextFieldRef = useRef<HTMLDivElement>(null);

	const debouncedSetSearchKeyword = useDebouncedCallback(
		setSearchKeyword,
		1000
	);

	const [, available] = useFeatureValidator(
		CUSTOM_TRACKING_FILTERING,
		() => {}
	);
	// 当前的advanceFilter值
	const togglePopoverActive = useCallback(
		() => setPopoverActive(popoverActive => !popoverActive),
		[]
	);
	const toggleOperationSelectActive = useCallback(
		() =>
			setOperationSelectActive(
				operationSelectActive => !operationSelectActive
			),
		[]
	);
	const {minDate} = useDatePickerRangeOptions();

	const {
		getResultValues,
		deleteTargetTypeAdvanceFilterItem,
		getInitialAdvanceFilterItem,
		changeAdvanceFilterItemKeyValue,
	} = useMultipleCustomFields({
		isShipment: isShipment,
		query: query,
		onChangeQuery: onChangeQuery,
		targetPath: 'customFilter',
		propertyKey: 'custom_fields_object',
	});

	const [currentSelectOperator, setCurrentSelectOperator] = useState(
		getInitialAdvanceFilterItem(customFieldLabel).operator
	);

	const [activeFieldValue, setActiveFieldValue] = useState<any>(
		getInitialAdvanceFilterItem(customFieldLabel).values || []
	);

	const {data: allOrganizations} = useAllCompanyOrganizationsQuery({
		enabled: isCompany(),
	});
	const allOrganizationIds = allOrganizations?.map(org => org.id) || [];

	const advancedFilters = useMemo(() => {
		return searchKeyword && isShipment && !isSubscription
			? [
					{
						propertyName: `custom_fields_object.${customFieldLabel}`,
						operator: 'PREFIX',
						value: searchKeyword,
					},
					{
						propertyName: 'created_at',
						operator: 'IN_X_DAYS',
						offset: '-7',
						timezone: '0',
					},
			  ]
			: [];
	}, [customFieldLabel, isShipment, isSubscription, searchKeyword]);

	const {data: trackingSubFacets, isLoading: trackingSubFacetsLoading} = (
		isCompany()
			? useCompanyTrackingAnalyticsSubFacetsQuery
			: useTrackingAnalyticsSubFacetsQuery
	)(
		{
			includeField:
				Tracking_Analytics_Sub_Facets_Include_Field.CustomFieldsValue,
			parents: [
				{
					field: Tracking_Analytics_Sub_Facets_Parent_Field.CustomFieldsKey,
					value: customFieldLabel,
				},
			],
			// @ts-ignore
			filter: {
				// dateType: Tracking_Analytics_Facets_Date_Type.CreatedAt,
				startTime: toOrgMoment(minDate)
					.add('1', 'day')
					.startOf('day')
					.format(),
				endTime: toOrgMoment().endOf('day').format(),
				...(isCompany()
					? {
							organizationIds: allOrganizationIds || [],
					  }
					: {}),
			} as TrackingAnalyticsFilterInput,
		},
		{
			enabled:
				(isCompany() ? allOrganizationIds?.length > 0 : true) &&
				popoverActive &&
				(!isShipment || isSubscription),
			refetchOnMount: 'always',
			select: (res: any) => {
				const resKey = isCompany()
					? 'companyTrackingAnalyticsSubFacets'
					: 'trackingAnalyticsSubFacets';

				return res?.[resKey]?.customFieldValues;
			},
		}
	);

	const {data: shipmentSubFacets} = (
		isCompany()
			? useCompanyTrackingSubFacetsV2Query
			: useTrackingSubFacetsV2Query
	)(
		{
			includeField: Tracking_Sub_Facets_Include_Field.CustomFieldsValue,
			parents: [
				{
					field: Tracking_Sub_Facets_Parent_Field.CustomFieldsKey,
					value: customFieldLabel,
				},
			],
			filter: {
				...(isCompany()
					? {
							organizationIds: allOrganizationIds || [],
					  }
					: {}),
			},
			advancedFilters,
			useV3API: isShipment,
		},
		{
			enabled: isCompany() ? allOrganizationIds?.length > 0 : true,
			keepPreviousData: true,
			select: (res: any) => {
				const resKey = isCompany()
					? 'companyTrackingSubFacetsV2'
					: 'trackingSubFacetsV2';

				return res?.[resKey]?.customFieldValues;
			},
		}
	);
	const activator = (
		<Button
			disabled={!available}
			disclosure="down"
			textAlign="left"
			onClick={togglePopoverActive}
			fullWidth
		>
			Select values
		</Button>
	);

	const operationActivator = (
		<Button
			disabled={!available}
			disclosure="down"
			textAlign="left"
			plain
			onClick={toggleOperationSelectActive}
			fullWidth
		>
			{getOperatorDescription(currentSelectOperator)}
		</Button>
	);

	const options = useMemo(() => {
		return (
			isShipment && !isSubscription
				? shipmentSubFacets
				: trackingSubFacets
		)?.map((facet: any) => ({
			key: getCharCode(facet.label),
			label: facet.label,
			value: numberWithCommas(facet.value),
		}));
	}, [isShipment, isSubscription, shipmentSubFacets, trackingSubFacets]);

	const resultChecked = useMemo(() => {
		const checkKeysMap = {};
		if (activeFieldValue?.length) {
			activeFieldValue?.forEach((key: string) => {
				set(checkKeysMap, getCharCode(key), true);
			});
		}
		return checkKeysMap;
	}, [activeFieldValue]);

	useEffect(() => {
		if (
			[Operator.CONTAINS_ANY_OF, Operator.NOT_CONTAINS_ANY_OF].includes(
				currentSelectOperator
			)
		) {
			const input: HTMLInputElement | null | undefined =
				multiTextFieldRef?.current?.querySelector?.(
					'.Polaris-TextField__Input'
				);

			input?.focus?.();
		}
	}, [currentSelectOperator]);

	return (
		<>
			<div
				style={{
					padding: '16px 12px',
					backgroundColor: '#F6F6F7',
					borderRadius: '8px',
					marginBottom: '16px',
				}}
			>
				<div
					style={{
						marginBottom: '14px',
						display: 'flex',
						justifyContent: 'space-between',
					}}
				>
					<div style={{display: 'flex'}}>
						<TextStyle>{customFieldLabel} </TextStyle>
						<Popover
							active={operationSelectActive}
							activator={operationActivator}
							onClose={toggleOperationSelectActive}
							fixed
						>
							<div
								role="button"
								tabIndex={0}
								onClick={e => e.stopPropagation()}
								style={{whiteSpace: 'nowrap'}}
							>
								<OptionList
									options={FilterPropertyMap[
										FilterPropertyName.customFields
									].operators?.filter(operator => {
										// Analytics 全部迁移到 AIO 后可以去掉
										if (
											(!isShipment || isSubscription) &&
											[
												OperatorMap[
													Operator.CONTAINS_ANY_OF
												].value,
												OperatorMap[
													Operator.NOT_CONTAINS_ANY_OF
												].value,
											].includes(operator.value)
										) {
											return false;
										}

										return true;
									})}
									selected={[]}
									onChange={value => {
										// 设置当前的operator
										setCurrentSelectOperator(
											value?.[0] as Operator
										);

										changeAdvanceFilterItemKeyValue({
											propertyLabel: customFieldLabel,
											currentOperator:
												value?.[0] as Operator,
										});

										setOperationSelectActive(false);

										const nowGroup = OPERATOR_GROUPS.find(
											group =>
												group?.includes?.(
													currentSelectOperator
												)
										);

										if (
											!nowGroup ||
											!nowGroup?.includes?.(
												value?.[0] as Operator
											)
										) {
											setActiveFieldValue([]);
											changeAdvanceFilterItemKeyValue({
												propertyLabel: customFieldLabel,
												currentValues: [],
											});
										}
									}}
								/>
							</div>
						</Popover>
					</div>

					<div
						style={{cursor: 'pointer'}}
						onClick={() => {
							setEnabledCustomFieldsList(
								without(
									enabledCustomFieldsList,
									customFieldLabel
								)
							);
							deleteTargetTypeAdvanceFilterItem(customFieldLabel);
						}}
					>
						<Icon source={DeleteMinor} />
					</div>
				</div>
				{[
					Operator.CONTAINS_ANY_OF,
					Operator.NOT_CONTAINS_ANY_OF,
				].includes(currentSelectOperator) ? (
					<div ref={multiTextFieldRef}>
						<MultiTextField
							values={activeFieldValue || []}
							setValue={(values: string[]) => {
								setActiveFieldValue(values);

								changeAdvanceFilterItemKeyValue({
									propertyLabel: customFieldLabel,
									currentValues: values,
								});
							}}
							placeholder={t('ENTER_VALU_b2b35', 'Enter value')}
						/>
					</div>
				) : (
					<>
						<Popover
							active={popoverActive}
							activator={activator}
							onClose={togglePopoverActive}
							fullWidth
							fixed
						>
							<div
								style={{padding: '10px'}}
								onClick={e => {
									e.stopPropagation();
								}}
							>
								<MultiChoiceList
									labelMapping={{
										'': '(No value)',
									}}
									loading={
										trackingSubFacetsLoading &&
										!searchKeyword
									}
									options={options || []}
									hideValue
									placeholder={t(
										'SEARCHVALU_9ed16',
										'Search values'
									)}
									withSearchInput
									onChange={handleValue => {
										const currentHandleOptionValue =
											options
												?.filter(
													({key}: any) =>
														key === handleValue
												)
												.map(({label}: any) => label) ||
											[];

										setActiveFieldValue(
											getResultValues(
												customFieldLabel,
												currentHandleOptionValue?.[0]
											)
										);
										changeAdvanceFilterItemKeyValue({
											propertyLabel: customFieldLabel,
											currentValues: getResultValues(
												customFieldLabel,
												currentHandleOptionValue?.[0]
											),
										});
									}}
									onSearchChange={value => {
										debouncedSetSearchKeyword(value || '');
									}}
									checkedKeysMap={resultChecked}
								/>
								<Popover.Pane fixed>
									<div style={{padding: '10px 0 0'}}>
										<Button
											plain
											disabled={!activeFieldValue.length}
											onClick={() => {
												setActiveFieldValue([]);
												changeAdvanceFilterItemKeyValue(
													{
														propertyLabel:
															customFieldLabel,
														currentValues: [],
													}
												);
											}}
										>
											{t('CLEAR_72975')}
										</Button>
									</div>
								</Popover.Pane>
							</div>
						</Popover>
						<div style={{marginTop: '4px', paddingRight: '8px'}}>
							<Stack spacing="tight">
								{activeFieldValue?.map((item: string) => {
									return (
										<Tag
											key={item}
											onRemove={() => {
												// 当前操作的值的字符串string表现
												const currentHandleOptionValue =
													options
														?.filter(
															({label}: any) =>
																label === item
														)
														.map(
															({label}: any) =>
																label
														) || [];

												setActiveFieldValue(
													getResultValues(
														customFieldLabel,
														currentHandleOptionValue?.[0]
													)
												);

												changeAdvanceFilterItemKeyValue(
													{
														propertyLabel:
															customFieldLabel,
														currentValues:
															getResultValues(
																customFieldLabel,
																currentHandleOptionValue?.[0]
															),
													}
												);
											}}
										>
											{item === '' ? '(No value)' : item}
										</Tag>
									);
								})}
							</Stack>
						</div>
					</>
				)}
			</div>
		</>
	);
};
