import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Popover, Tooltip} from '@shopify/polaris';
import {difference} from 'lodash';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {
	Tracking_Analytics_Facets_Include_Field,
	Tracking_Facets_Include_Field,
	useTrackingAnalyticsFacetsQuery,
	useCompanyTrackingAnalyticsFacetsQuery,
	useTrackingFacetsQuery,
	useCompanyTrackingFacetsQuery,
} from '@graphql/generated';
import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {Operator} from 'pages/Shipments/interfaces/Filter';
import {useAllCompanyOrganizationsQuery} from 'services/company';
import {toOrgMoment} from 'utils/day';
import isCompany from 'utils/isCompany';

import {useMultipleCustomFields} from '../hooks/customFieldHooks';

interface Props {
	isSubscription?: boolean;
	isShipment?: boolean;
	query: ReturnType<typeof transformTrackingAnalyticsQuery>['query'];
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
	enabledCustomFieldsList: string[];
	setEnabledCustomFieldsList: (value: string[]) => void;
}

export const CustomFieldSelect = ({
	isShipment,
	isSubscription,
	query,
	onChangeQuery,
	enabledCustomFieldsList,
	setEnabledCustomFieldsList,
}: Props) => {
	const {t} = useTranslation();

	const [popoverActive, setPopoverActive] = useState(false);

	const togglePopoverActive = useCallback(
		() => setPopoverActive(popoverActive => !popoverActive),
		[]
	);
	const {
		advanceFilterItemForPropertyKey,
		createTargetTypeAdvanceFilter,
		clearTargetTypeAdvanceFilter,
	} = useMultipleCustomFields({
		isShipment: isShipment,
		query: query,
		onChangeQuery: onChangeQuery,
		targetPath: 'customFilter',
		propertyKey: 'custom_fields_object',
	});
	const {minDate} = useDatePickerRangeOptions();

	const {data: allOrganizations} = useAllCompanyOrganizationsQuery({
		enabled: isCompany(),
	});
	const allOrganizationIds = allOrganizations?.map(org => org.id) || [];

	const {data: analyticsCustomFieldsData} = (
		isCompany()
			? useCompanyTrackingAnalyticsFacetsQuery
			: useTrackingAnalyticsFacetsQuery
	)(
		{
			includeField:
				Tracking_Analytics_Facets_Include_Field.CustomFieldsKey,
			//  解耦展示所有custom_field
			// @ts-ignore
			filter: {
				startTime: toOrgMoment(minDate)
					.add('1', 'day')
					.startOf('day')
					.format(),
				endTime: toOrgMoment().endOf('day').format(),
				...(isCompany()
					? {
							organizationIds: allOrganizationIds || [],
					  }
					: {}),
			},
		},
		{
			// @ts-ignore
			enabled:
				(isCompany() ? allOrganizationIds?.length > 0 : true) &&
				Boolean(!isShipment || isSubscription),
			select: (res: any) => {
				const resKey = isCompany()
					? 'companyTrackingAnalyticsFacets'
					: 'trackingAnalyticsFacets';

				return res?.[resKey];
			},
		}
	);

	const {data: shipmentCustomFieldsData} = (
		isCompany() ? useCompanyTrackingFacetsQuery : useTrackingFacetsQuery
	)(
		{
			includeField: Tracking_Facets_Include_Field.CustomFieldsKey,
			//  解耦展示所有custom_field
			filter: {
				...(isCompany()
					? {
							organizationIds: allOrganizationIds || [],
					  }
					: {}),
			},
			...(isCompany() ? {useV3API: true} : {}),
		},
		{
			// @ts-ignore
			enabled:
				(isCompany() ? allOrganizationIds?.length > 0 : true) &&
				Boolean(isShipment && !isSubscription),
			select: (res: any) => {
				const resKey = isCompany()
					? 'companyTrackingFacets'
					: 'trackingFacets';

				return res?.[resKey];
			},
		}
	);

	// 除去已经enable的customField
	const customFieldList = useMemo(() => {
		const customFieldKeys = (
			isShipment && !isSubscription
				? shipmentCustomFieldsData
				: analyticsCustomFieldsData
		)?.customFields
			.map((customField: any) => {
				return customField?.label;
			})
			.slice(0, 1000);
		const canEnabledCustomFieldsList = difference(
			customFieldKeys,
			enabledCustomFieldsList
		);
		return canEnabledCustomFieldsList.map(customLabel => {
			return {
				label: customLabel,
				value: customLabel,
			};
		});
	}, [
		shipmentCustomFieldsData,
		analyticsCustomFieldsData,
		enabledCustomFieldsList,
	]);

	const customFieldValueList = customFieldList.map(item => {
		return item.value;
	});

	const activator = !customFieldValueList.length ? (
		<Tooltip content={t('ALLCUSTOMF_8fb74', 'All custom fields added')}>
			<Button
				onClick={togglePopoverActive}
				disabled={!customFieldValueList.length}
			>
				{t('ADDCUSTOMF_70e69', '+ Add custom field')}
			</Button>
		</Tooltip>
	) : (
		<Button onClick={togglePopoverActive}>
			{t('ADDCUSTOMF_70e69', '+ Add custom field')}
		</Button>
	);

	useEffect(() => {
		if (!enabledCustomFieldsList.length) {
			setTimeout(() => {
				setPopoverActive(true);
			}, 500);
		}
	}, [enabledCustomFieldsList]);
	return (
		<>
			<div style={{display: 'flex', justifyContent: 'space-between'}}>
				<Popover
					active={popoverActive}
					activator={activator}
					autofocusTarget="first-node"
					onClose={togglePopoverActive}
					fullWidth
				>
					<OptionList
						onChange={value => {
							setEnabledCustomFieldsList([
								...enabledCustomFieldsList,
								...value,
							]);

							createTargetTypeAdvanceFilter({
								propertyLabel: value[0] || '',
								operator: Operator.IS_ANY_OF,
							});
							setPopoverActive(false);
						}}
						options={customFieldList}
						selected={[]}
					/>
				</Popover>
				<Button
					disabled={!advanceFilterItemForPropertyKey?.length}
					plain
					onClick={() => {
						setEnabledCustomFieldsList([]);

						clearTargetTypeAdvanceFilter();
					}}
				>
					{t('CLEARALL_262a3', 'Clear all')}
				</Button>
			</div>
		</>
	);
};
