.scrollable {
	height: calc(100% - 122px);
	:global(.Polaris-Scrollable) {
		overflow-x: hidden;
	}

	.divider {
		margin: 24px 0;
		border-top: 1px solid #e1e3e5;
	}
}

.filters {
	// overflow: auto;
}

.filters
	:global(.Polaris-Filters-ConnectedFilterControl__MoreFiltersButtonContainer) {
	display: none;
}

.items {
	display: flex;
	margin-left: 0.9rem;
	align-items: center;
	justify-content: center;
}

:global(.Polaris-Sheet__Container:has(div[aria-label='More filters'])) {
	z-index: 999999999;
}

.activator {
	height: 28px;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-wrap: nowrap;
	border: 1px solid #c9cccf;
	border-radius: 2px;
	cursor: pointer;
	.content {
		font-size: 12px;
		padding: 0 8px;
	}
	.icon {
		width: 28px;
		height: 28px;
		border-left: 1px solid #c9cccf;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	:global(.Polaris-Button--sizeSlim) {
		padding: 0;
	}
}

.popoverContainer {
	width: 288px;
	padding: 16px;
	:global(.Polaris-Popover__Pane + .Polaris-Popover__Pane) {
		border-top: none;
	}
	:global(.Polaris-Scrollable--hasTopShadow) {
		box-shadow: none;
	}
	:global(.Polaris-Scrollable--hasBottomShadow) {
		box-shadow: none;
	}
}
