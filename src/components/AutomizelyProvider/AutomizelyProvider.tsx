import {useBillingGlobalState} from 'aftershipBillingUi';
import {AutomizelyProvider} from '@aftership/uikit-admin-polaris-extends';
import React, {FC} from 'react';

import isCompany from 'utils/isCompany';

const AutomizelyProviderWrapper: FC = ({children}) => {
	const {isEnterprise: _isEnterprise, loading} = useBillingGlobalState();

	const isEnterprise = loading ? undefined : _isEnterprise;
	if (!isCompany()) {
		return <>{children}</>;
	}

	return (
		<AutomizelyProvider
			currentProduct="aftership"
			fixedLayout="all-in-one"
			isEnterprise={isEnterprise}
			reportPageView={false}
		>
			{children}
		</AutomizelyProvider>
	);
};

export default AutomizelyProviderWrapper;
