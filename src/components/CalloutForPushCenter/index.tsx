import {But<PERSON>, <PERSON>} from '@shopify/polaris';
import clsx from 'clsx';
import React from 'react';

import style from './index.module.scss';

interface Props {
	title: string;
	subTitle?: string;
	primaryBtnText?: string;
	secondaryBtnText?: string;
	imageUrl?: string;
	primaryAction?: any;
	isShowPagination?: boolean;
	actions?: JSX.Element;
}

export const CalloutCardForPushCenter = ({
	title,
	subTitle,
	primaryBtnText,
	secondaryBtnText,
	imageUrl,
	primaryAction,
	isShowPagination,
	actions,
}: Props) => {
	return (
		<div>
			<div className={style['ntc-banner']}>
				<Card>
					<div className={style['ntc-banner-content']}>
						<div className={style['ntc-banner-title']}>{title}</div>
						<div className="ntc-banner-sub">{subTitle}</div>
						<div className={style['ntc-banner-btn-group']}>
							<Button onClick={primaryAction}>
								{primaryBtnText}
							</Button>
							{secondaryBtnText && (
								<Button plain onClick={() => {}}>
									{secondaryBtnText}
								</Button>
							)}
						</div>
					</div>
					{imageUrl && (
						<div
							className={clsx(
								style['ntc-banner-img'],
								isShowPagination
									? style['ntc-banner-hasPagination']
									: null
							)}
						>
							<img src={imageUrl} alt="" />
						</div>
					)}
					{actions}
				</Card>
			</div>
		</div>
	);
};
