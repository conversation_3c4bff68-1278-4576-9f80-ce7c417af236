import {But<PERSON>, Modal, Toast} from '@shopify/polaris';
import {stringifyUrl} from 'query-string';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {useFacebookConnectionCreateMutation} from '@graphql/generated';

import fb_logo from './fb_logo.png';
import styles from './modal.module.css';

const getCodeFromPostMessage = (): Promise<string | undefined> =>
	new Promise(resolve => {
		const onReceiveMessage = (event: MessageEvent) => {
			const {data = {}} = event;
			if (data.source !== 'secure' || data.type !== 'FACEBOOK_CONNECT') {
				return;
			}
			window.removeEventListener('message', onReceiveMessage, false);
			resolve(data?.result?.code);
		};
		window.addEventListener('message', onReceiveMessage, false);
	});

type Props = {
	open: boolean;
	onClose: () => void;
	onConnect: (connectionId: string) => void;
};

const ConnectFacebookModal: React.FC<Props> = ({open, onConnect, onClose}) => {
	const {t} = useTranslation();
	const [error, setError] = useState('');
	const [connecting, setConnecting] = useState(false);

	const authWindowRef = useRef<Window | null>();

	const clear = useCallback(() => {
		authWindowRef.current = null;
		setConnecting(false);
	}, []);

	useEffect(() => clear, []);

	const {mutateAsync} = useFacebookConnectionCreateMutation({
		onSuccess: data => {
			authWindowRef.current = null;
			setConnecting(false);
			onConnect(data.facebookConnectionCreate.id);
			onClose();
		},
	});

	const connect = async () => {
		if (authWindowRef.current) {
			authWindowRef.current.focus();
			return;
		}

		setConnecting(true);

		const token = '_' + Math.random().toString(36).substr(2, 9);
		sessionStorage.setItem('csrf_token', token);
		const redirect_uri =
			(process.env.PUBLIC_URL || 'http://localhost:3303') +
			'/facebook/callback.html';

		const authUrl = stringifyUrl(
			{
				url: 'https://m.facebook.com/v13.0/dialog/oauth',
				query: {
					client_id: process.env.FACEBOOK_CLIENT_ID,
					redirect_uri,
					scope: [
						'pages_manage_ads',
						'pages_manage_metadata',
						'pages_read_engagement',
						'pages_read_user_content',
						'pages_messaging',
						'pages_messaging_subscriptions',
					],
					state: token,
				},
			},
			{arrayFormat: 'comma'}
		);

		const windowWidth = 576;
		const windowHeight = 739;
		const screenX = (window.screen.width - windowWidth) / 2;
		const screenY = (window.screen.height - windowHeight) / 2;

		const authWindow = window.open(
			'/facebook/authorize.html?url=' + authUrl,
			'_blank',
			`popup=1,width=${windowWidth}px,height=${windowHeight}px,screenX=${screenX}px,screenY=${screenY}px`
		);

		authWindowRef.current = authWindow;

		const code = await getCodeFromPostMessage();

		if (code) {
			await mutateAsync({
				code,
				redirectUrl: redirect_uri,
			});
			// close modal
			onClose();
		} else {
			setError(t('SOMETHING_bf830'));
		}

		// clear state
		clear();
	};

	const modalRef = useCallback((node: HTMLDivElement | null) => {
		if (node !== null) {
			const rootRef = node.closest('[data-portal-id]');

			if (rootRef) {
				rootRef.classList.add(styles.modal);
			}
		}
	}, []);

	return (
		<>
			{error && (
				<Toast error onDismiss={() => setError('')} content={error} />
			)}

			<Modal
				title={t('CONNECT_FA_f57fc')}
				titleHidden
				open={open}
				onClose={() => {
					onClose();
					clear();
				}}
				sectioned
			>
				<div ref={modalRef} />

				<div className={styles.modalContainer}>
					<div className={styles.logoContainer}>
						<img
							className={styles.logo}
							src={fb_logo}
							alt={t('FACEBOOK_L_4935d')}
						/>
					</div>

					<div className={styles.modalTitle}>
						{t('CONNECT_FA_0a428')}
					</div>

					<div className={styles.modalDesc}>
						{t('YOU_MUST_C_06bb9')}
					</div>

					<div style={{textAlign: 'center', marginBottom: '49px'}}>
						<Button
							fullWidth
							primary
							loading={connecting}
							onClick={() =>
								connect().catch(err => {
									// eslint-disable-next-line no-console
									console.error(err);
									clear();
									setError(String(err));
								})
							}
						>
							{t('CONNECT_NO_ea647')}
						</Button>
					</div>
				</div>
			</Modal>
		</>
	);
};

export default ConnectFacebookModal;
