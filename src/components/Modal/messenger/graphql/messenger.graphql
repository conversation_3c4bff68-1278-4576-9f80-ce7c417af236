mutation facebookConnectionCreate($code: String!, $redirectUrl: String!) {
	facebookConnectionCreate(
		facebookConnectionCreateInput: {code: $code, redirectUrl: $redirectUrl}
	) {
		id
	}
}

mutation facebookPageConnect($connectionId: String!, $pageId: String!) {
	facebookPageConnect(pageId: $pageId, connectionId: $connectionId)
}

mutation facebookConnectionDelete($connectionId: String!) {
	facebookConnectionDelete(connectionId: $connectionId)
}

query facebookPages {
	facebookPages {
		id
		name
	}
}
