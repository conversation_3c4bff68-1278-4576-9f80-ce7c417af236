import {But<PERSON>, Modal, Select} from '@shopify/polaris';
import React, {useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {
	useFacebookPagesQuery,
	useFacebookPageConnectMutation,
} from '@graphql/generated';

import fb_logo from './fb_logo.png';
import styles from './modal.module.css';

type Props = {
	open: boolean;
	onClose: VoidFunction;

	connectionId?: string;
	onChange: VoidFunction;
	noClose?: boolean;
};

const ChangeFacebookPageModal: React.FC<Props> = ({
	open,
	onClose,
	onChange,
	connectionId = '',
	noClose = false,
}) => {
	const {t} = useTranslation();
	const [selected, setSelected] = useState('');

	const {data: pages = [], isLoading} = useFacebookPagesQuery(undefined, {
		enabled: open,
		select: data => data.facebookPages,
		onSuccess: (pages: any[] = []) => {
			if (pages?.[0]?.id) {
				setSelected(pages[0].id);
			}
		},
	});

	const {mutate, isLoading: isUpdating} = useFacebookPageConnectMutation({
		onSuccess: () => {
			onClose();
			onChange();
		},
	});

	const modalRef = useCallback((node: HTMLDivElement | null) => {
		if (node !== null) {
			const rootRef = node.closest('[data-portal-id]');

			if (rootRef) {
				rootRef.classList.add(styles.modal);

				if (noClose) rootRef.classList.add(styles.noClose);
			}
		}
	}, []);

	return (
		<Modal
			title={t('CHANGE_FAC_4c5b3')}
			titleHidden
			onClose={onClose}
			open={open}
			sectioned
		>
			<div ref={modalRef} />

			<div className={styles.modalContainer}>
				<div className={styles.logoContainer}>
					<img
						className={styles.logo}
						src={fb_logo}
						alt={t('FACEBOOK_L_de9aa')}
					/>
				</div>

				<div className={styles.modalTitle}>{t('CHOOSE_A_F_263d3')}</div>

				<div className={styles.modalSelect}>
					<Select
						label={t('SELECT_PAG_551e9')}
						labelHidden
						disabled={isUpdating || isLoading}
						options={pages.map(page => ({
							label: page.name,
							value: page.id,
						}))}
						onChange={setSelected}
						value={selected}
					/>
				</div>

				<div style={{paddingBottom: '29px'}}>
					<Button
						primary
						fullWidth
						disabled={!selected}
						onClick={() => mutate({connectionId, pageId: selected})}
						loading={isLoading || isUpdating}
					>
						{noClose ? t('CONFIRM_9ca9b') : t('UPDATE_1e0d5')}
					</Button>
				</div>
			</div>
		</Modal>
	);
};

export default ChangeFacebookPageModal;
