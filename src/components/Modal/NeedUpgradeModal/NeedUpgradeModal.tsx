import {IPlan} from 'aftershipBillingUi';
import {Modal} from '@shopify/polaris';
import React, {useCallback} from 'react';

import FeatureLockCard from 'components/FeatureLockCard';
import {FEATURE_LOCK_UPGRADE_MODAL} from 'constants/Modals';
import {useModal} from 'hooks';

import style from './NeedUpgradeModal.module.scss';

const NeedUpgradeModal = () => {
	const {close, opened, option} = useModal<{
		plan: IPlan;
		coupon: string;
		availableFeature: string | undefined;
	}>(FEATURE_LOCK_UPGRADE_MODAL);

	const modalRef = useCallback((node: HTMLDivElement | null) => {
		if (node !== null) {
			const rootRef = node.closest('[data-portal-id]');

			if (rootRef) {
				const modalEl = rootRef.querySelector<HTMLDivElement>(
					'.Polaris-Modal-Dialog__Modal'
				);

				modalEl?.classList?.add(style.featureLockModal);
			}
		}
	}, []);

	return (
		<Modal open={opened} onClose={close} title="" noScroll>
			<div ref={modalRef}>
				<FeatureLockCard
					plan={option?.plan}
					onClick={close}
					coupon={option?.coupon}
					featureCode={option?.availableFeature}
				/>
			</div>
		</Modal>
	);
};

export default NeedUpgradeModal;
