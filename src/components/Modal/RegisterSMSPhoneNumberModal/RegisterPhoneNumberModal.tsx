import {Button} from '@shopify/polaris';
import {
	CommonTipModal,
	useNotificationUpgradeUtils,
	captureData,
	useNiceModal,
	useRegistrationFlowStore,
	useSMSPhoneNumber,
	SmsPhoneNumberRegistrationFlow,
} from 'aftershipNotification';
import React, {useRef} from 'react';

import {useIsAllinoneEmail} from 'pages/Notifications/Setting/hooks/useIsAllinoneEmail';
import emitter from 'utils/eventBus';

import styles from './index.module.scss';

const RegisterPhoneNumberModal = () => {
	const ref = useRef(null);
	const {toggleModal, nextModal, closeModals} = useRegistrationFlowStore(
		state => ({
			toggleModal: state.toggleModal,
			nextModal: state.nextModal,
			closeModals: state.closeModals,
		})
	);
	const isAllInOne = useIsAllinoneEmail();
	const {
		smsStatus,
		migrationSchedule,
		addToUpgradeListFromContract,
		updateMigrationScheduleToSuspend,
	} = useNotificationUpgradeUtils({
		skip: isAllInOne,
	});
	const {fetchSMSPhoneNumbers} = useSMSPhoneNumber();

	const laterModal = useNiceModal(CommonTipModal, {
		title: 'Join upgrade queue later',
		content: `When you're ready, you can join the upgrade queue from the Notifications page.`,
		primaryAction: {
			content: 'Got it',
			onAction() {
				laterModal.hide();
				captureData('modal_click', {
					extraParams: {
						sms_status: smsStatus,
						modal_name: 'upgrade_later',
						button_name: 'got_it',
					},
				});
				emitter.emit(
					'refresh_notification_upgrade',
					migrationSchedule?.sales_order_id
				);
			},
		},
	});

	const leaveModal = useNiceModal(CommonTipModal, {
		title: 'Leave the registration process?',
		content:
			'You can only enter the upgrade queue after you’ve registered a sending number. If you leave now, you can still complete registration later on the Notifications page.',
		primaryAction: {
			content: 'Continue registration',
			onAction() {
				captureData('modal_click', {
					extraParams: {
						sms_status: smsStatus,
						modal_name: 'upgrade_step',
						button_name: 'continue_registration',
					},
				});
				emitter.emit(
					'refresh_notification_upgrade',
					migrationSchedule?.sales_order_id
				);
				leaveModal.hide();
				toggleModal('compliance', true);
			},
		},
		secondaryActions: [
			{
				content: 'Leave now',
				onAction() {
					captureData('modal_click', {
						extraParams: {
							sms_status: smsStatus,
							modal_name: 'upgrade_step',
							button_name: 'leave_now',
						},
					});
					leaveModal.hide();
				},
			},
		],
	});

	const handleOnNoticeRegister = () => {
		captureData('modal_click', {
			extraParams: {
				sms_status: smsStatus,
				modal_name: 'upgrade_step',
				button_name: 'register_now',
			},
		});
		nextModal('notice', 'compliance');
	};

	const handleOnNoticeClose = () => {
		closeModals();
		laterModal.show();
	};

	const handleOnNoticeLater = () => {
		captureData('modal_click', {
			extraParams: {
				sms_status: smsStatus,
				modal_name: 'upgrade_step',
				button_name: 'upgrade_later',
			},
		});
		handleOnNoticeClose();
	};

	const handleSetupPhoneNumber = async () => {
		const phoneNumbers = await fetchSMSPhoneNumbers();
		const hasSMSPhoneNumber = phoneNumbers.length > 0;

		if (!hasSMSPhoneNumber) return;

		captureData('modal_click', {
			extraParams: {
				sms_status: smsStatus,
				modal_name: 'upgrade_step',
				button_name: 'go_it',
			},
		});

		// 如果其中有号码正在验证中，则暂停升级队列
		if (
			phoneNumbers.some(phoneNumber =>
				['verifying', 'rejected'].includes(phoneNumber.status)
			)
		) {
			await updateMigrationScheduleToSuspend();
			emitter.emit(
				'refresh_notification_upgrade',
				migrationSchedule?.sales_order_id
			);
			return;
		}

		// 如果全部号码已经验证过，则加入升级队列
		if (
			phoneNumbers.every(phoneNumber =>
				['unverified', 'active'].includes(phoneNumber.status)
			)
		) {
			await addToUpgradeListFromContract();
			emitter.emit(
				'refresh_notification_upgrade',
				migrationSchedule?.sales_order_id
			);
		}
	};

	const onUnlockRegion = (region: string) => {
		const button_name =
			region === 'United Kingdom'
				? 'uk_upgrade_unlock'
				: 'others_upgrade_unlock';

		captureData('modal_click', {
			extraParams: {
				sms_status: smsStatus,
				modal_name: 'upgrade_step',
				button_name,
			},
		});
	};

	const getRegions = (smsTypes: any) => {
		const enableTollFree =
			smsTypes?.find(
				c => c?.type === 'toll_free' && c?.countryRegion === 'US'
			)?.enabled ?? false;

		const enableUK10DLC =
			smsTypes?.find(
				c => c?.type === 'long_code' && c?.countryRegion === 'GB'
			)?.enabled ?? false;

		const enable10DLC =
			smsTypes?.find(
				c => c?.type === 'long_code' && c?.countryRegion === 'US'
			)?.enabled ?? false;

		return [
			[enableTollFree, 'United States'],
			[enableUK10DLC, 'United Kingdom'],
			[enable10DLC, 'Other countries/regions'],
		]
			.filter(([selected]) => selected)
			.map(([, region]) => region)
			.join(', ');
	};

	const onStepChange = (step: number, nextStep: number, values: any) => {
		if (step == 1 && nextStep === 2) {
			const regions = getRegions(values?.smsTypes || []);
			captureData('modal_click', {
				extraParams: {
					sms_status: smsStatus,
					regions,
					modal_name: 'upgrade_step',
					button_name: 'choose_regions',
				},
			});
			return;
		}

		if (step === 2 && nextStep === 3) {
			captureData('modal_click', {
				extraParams: {
					sms_status: smsStatus,
					modal_name: 'upgrade_step',
					button_name: 'submit_info',
				},
			});
			return;
		}

		if (step === 3 && nextStep > 2) {
			captureData('modal_click', {
				extraParams: {
					sms_status: smsStatus,
					modal_name: 'upgrade_step',
					button_name: 'compliance_confirm',
				},
			});
		}
	};

	const onLeave = () => {
		leaveModal.show();
	};

	const onConfirm = () => {
		captureData('modal_click', {
			extraParams: {
				sms_status: smsStatus,
				modal_name: 'upgrade_step',
				button_name: 'compliance_confirm',
			},
		});
	};

	return (
		<SmsPhoneNumberRegistrationFlow
			ref={ref}
			noticeModalProps={{
				title: 'Register a dedicated SMS number',
				modalWidth: '612px',
				addClassName: styles.container,
				primaryAction: {
					content: 'Register now',
					onAction: handleOnNoticeRegister,
				},
				secondaryActions: [
					{
						content: `I'll do it later`,
						onAction: handleOnNoticeLater,
					},
				],
				onClose: handleOnNoticeClose,
				children: (
					<p>
						You're currently using a shared number to send SMS
						notifications. You'll need to register a dedicated
						sending number for SMS notifications before you can join
						the upgrade queue.{' '}
						<Button
							plain
							external
							url="https://support.aftership.com/en/article/importance-of-registering-a-dedicated-phone-number-1rc6w0v/"
						>
							Learn more
						</Button>
					</p>
				),
			}}
			complianceModalProps={{
				onConfirm,
				onUnlockRegion,
				onStepChange,
				onLeave: onLeave,
			}}
			confirmModalProps={{
				title: "Congratulations, you're now in the queue!",
				description:
					"Once your sending numbers are ready, we'll find a time when you're logged out to complete the upgrade.",
				submitText: 'Got it',
				onSubmit: handleSetupPhoneNumber,
				onClose: handleSetupPhoneNumber,
			}}
		/>
	);
};

export default RegisterPhoneNumberModal;
