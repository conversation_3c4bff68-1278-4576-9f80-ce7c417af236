import {Modal as PolarisModal, ModalProps} from '@shopify/polaris';
import PropTypes from 'prop-types';
import React, {PureComponent} from 'react';

import {MODAL_BANNER} from 'constants/BannerNames';
import {gaModalView, gaModalLeave} from 'utils/gtag';
import history from 'utils/history';

interface Props extends ModalProps {
	removeBanner: (modalName: string) => any;
}

class Modal extends PureComponent<Props> {
	static propTypes = {
		removeBanner: PropTypes.func.isRequired,
		open: PropTypes.bool,
	};

	static Section = PolarisModal.Section;

	static defaultProps = {
		open: false,
	};

	onView() {
		gaModalView({
			title:
				this.props.title?.toString() ||
				this.props.modalType?.toString() ||
				'',
			page: history.location.pathname + history.location.search,
		});
	}

	onLeave() {
		gaModalLeave({
			title: this.props.title?.toString() || '',
			page: history.location.pathname + history.location.search,
		});
	}

	componentDidMount() {
		if (this.props.open) {
			this.onView();
		}
	}

	componentDidUpdate(prevProps: Props) {
		if (prevProps.open && prevProps.open !== this.props.open) {
			this.props.removeBanner(MODAL_BANNER);
		}

		if (prevProps.open !== this.props.open) {
			if (this.props.open) {
				this.onView();
			} else {
				this.onLeave();
			}
		}
	}

	componentWillUnmount() {
		this.props.removeBanner(MODAL_BANNER);

		if (this.props.open) {
			this.onLeave();
		}
	}

	render() {
		return (
			<PolarisModal {...this.props}>{this.props.children}</PolarisModal>
		);
	}
}

export default Modal;
