import React from 'react';
import {Banner} from '@shopify/polaris';
import {useSubscriptionActions} from 'aftershipBillingUi';
import {useGetNearestPlanByFeatureCode} from 'hooks/billings/useChoosePlan';

function ModalBanner() {
	const {bannerInfo} = useSubscriptionActions({
		modalId: 'subscription',
	});
	if (!bannerInfo) {
		return <></>;
	}
	return (
		<Banner status={bannerInfo?.status || 'critical'}>
			{bannerInfo?.content}
		</Banner>
	);
}

export const RequiredPlanBanner = ({
	neededFeatures,
}: {
	neededFeatures: string[];
}) => {
	const nearestPlan = useGetNearestPlanByFeatureCode(neededFeatures);
	if (!nearestPlan) {
		return null;
	}
	if (nearestPlan.name.toLowerCase().indexOf('essential') > -1) {
		return (
			<Banner status="info">
				This feature is available to Essentials, Pro and Enterprise
				plans only. Please upgrade to get access.
			</Banner>
		);
	} else if (nearestPlan.name.toLowerCase().indexOf('pro') > -1) {
		return (
			<Banner status="info">
				This feature is available to Pro and Enterprise plans only.
				Please upgrade to get access.
			</Banner>
		);
	}
	return null;
};

export default ModalBanner;
