import React from 'react';
import {Stack, DisplayText, TextStyle, Banner} from '@shopify/polaris';
import {IPlan} from 'aftershipBillingUi';
import {useI18n} from '@shopify/react-i18n';
import {useGetOriginalPlan} from 'hooks/billings/useSubscription';

export const DiscountPlanInfo = ({plan}: {plan: IPlan}) => {
	const [i18n] = useI18n();
	const originPlan = useGetOriginalPlan(plan);

	return (
		<Stack vertical>
			<Stack.Item fill>
				<Banner status="info">
					Too expensive? Get a discount. Offer expires today.
				</Banner>
			</Stack.Item>

			<Stack vertical spacing="extraTight" alignment="center">
				<Stack.Item />
				<TextStyle>
					<del>
						$
						{Number(
							originPlan?.pricing.amount.toFixed() || 0
						).toLocaleString()}
						/mo
					</del>
					<span
						style={{
							display: 'inline-block',
							margin: '0 0 0 3px',
							padding: '2px 8px 2px 8px',
							borderRadius: 100,
							border: 'solid 2px #ffffff',
							backgroundColor: '#ffea8a',
							fontSize: 13,
							fontWeight: 'bold',
							lineHeight: 1.23,
							color: ' #de3618',
						}}
					>
						20% off
					</span>
				</TextStyle>
				<Stack.Item />
				<Stack vertical spacing="none" alignment="center">
					<DisplayText size="large">
						<TextStyle variation="strong">
							<span style={{color: '#de3618'}}>
								{'$' +
									Number(
										plan.pricing.amount.toFixed()
									).toLocaleString()}
							</span>
						</TextStyle>
						<span
							style={{
								fontSize: 14,
								color: '#de3618',
								fontWeight: 'normal',
							}}
						>
							/mo
						</span>
					</DisplayText>
				</Stack>
			</Stack>
		</Stack>
	);
};
