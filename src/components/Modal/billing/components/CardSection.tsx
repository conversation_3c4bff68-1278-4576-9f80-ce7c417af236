import {IPlan, PlanType, usePayment} from 'aftershipBillingUi';
import {Card, Stack, TextStyle} from '@shopify/polaris';
import dayjs from 'dayjs';
import React, {useEffect, useState} from 'react';

import StripeSection from 'components/PaymentMethod/StripeSection';
import {DATE_END} from 'constants/Date';
import {useIsPayWithShopify} from 'hooks/billings/useChoosePlan';
import useShopifySubdomain from 'hooks/billings/useShopifySubdomain';
import {
	useActiveSubscription,
	useGetCurrentPlan,
} from 'hooks/billings/useSubscription';
import {ReactComponent as ShopifyIcon} from 'icons/Shopify.svg';

const useText = (externalPlan: IPlan) => {
	const currentPlan = useGetCurrentPlan();
	const [plan, setPlan] = useState(externalPlan);
	useEffect(() => {
		setPlan(externalPlan);
	}, [externalPlan.code]);
	const {subscription} = useActiveSubscription();
	const date = dayjs(subscription?.currentPeriod.endAt).format(DATE_END);
	if (plan?.isEnterprise || !currentPlan) {
		return ``;
	}
	if (plan?.type === PlanType.trial) {
		return `You're downgrading your plan from <strong>${currentPlan?.name}</strong> to Free. Your new plan will take effect on <strong>${date}</strong>.`;
	}
	if (plan?.level > currentPlan.level) {
		return `To upgrade your plan from <strong>${currentPlan?.name}</strong> to <strong>${plan.name}</strong>, you should pay the difference between the two plans and it will take effect immediately.`;
	}
	if (plan?.level < currentPlan.level) {
		return `You're downgrading your plan from <strong>${currentPlan?.name}</strong> to <strong>${plan.name}</strong>. Your next payment will be due on <strong>${date}</strong>.`;
	}
	return '';
};

type CardSectionProps = React.ComponentProps<typeof StripeSection> & {
	plan: IPlan;
};

export default ({plan, ...rest}: CardSectionProps) => {
	const subdomain = useShopifySubdomain();
	const {loading} = usePayment();
	const isPayWithShopify = useIsPayWithShopify();
	const text = useText(plan);
	if (loading) {
		return null;
	}
	if (isPayWithShopify) {
		const shopifyUrl = subdomain + '.myshopify.com';
		return (
			<Card.Section title="Payment method">
				<Stack vertical spacing="loose">
					<Stack.Item />
					<Stack spacing="tight">
						<ShopifyIcon width={20} height={20} />
						<p>{`Shopify (${shopifyUrl})`}</p>
					</Stack>
					{plan.type !== PlanType.addOn && (
						<TextStyle variation="subdued">
							<p dangerouslySetInnerHTML={{__html: text}} />
						</TextStyle>
					)}
				</Stack>
			</Card.Section>
		);
	}
	return (
		<Card.Section title="Payment method">
			<StripeSection {...rest} />
		</Card.Section>
	);
};
