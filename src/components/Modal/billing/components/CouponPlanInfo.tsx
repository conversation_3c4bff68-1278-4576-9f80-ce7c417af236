import {IPlan, usePriceComputer} from 'aftershipBillingUi';
import {Stack, DisplayText, TextStyle} from '@shopify/polaris';
import {useI18n} from '@shopify/react-i18n';
import camelCase from 'lodash/camelCase';
import React, {useMemo} from 'react';

import {isAnnually} from 'hooks/billings/useChoosePlan';

export enum DiscountScheme {
	amountOff = 'amountOff',
	percentOff = 'percentOff',
}

export const CouponPlanInfo = ({
	plan,
	coupon: couponContext,
	currentFee,
	originalFee,
	upcoming,
}: {plan?: IPlan} & ReturnType<typeof usePriceComputer>) => {
	const [i18n] = useI18n();

	const offContent = useMemo(() => {
		if (
			camelCase(couponContext?.discountScheme) ===
			DiscountScheme.amountOff
		) {
			return i18n.formatCurrency(couponContext?.amountOff);
		}
		return `${couponContext?.percentOff}%`;
	}, [
		couponContext?.discountScheme,
		couponContext?.amountOff,
		couponContext?.percentOff,
	]);
	const timeDiff = couponContext?.maxDeductCycles;
	const cycleUnit = `${plan?.billingInterval.displayText}${
		timeDiff > 1 ? 's' : ''
	}`;
	const annually = isAnnually(plan);
	const displayedOriginalFee = annually
		? Math.ceil(Number(upcoming.originalFee / 12))
		: originalFee;
	const displayedCurrentFee = annually
		? Math.ceil(Number(currentFee / 12))
		: currentFee;

	return (
		<Stack vertical>
			<Stack vertical alignment="center">
				<span
					style={{
						display: 'inline-block',
						margin: '0 0 0 3px',
						padding: '2px 8px 2px 8px',
						borderRadius: 100,
						border: 'solid 2px #ffffff',
						backgroundColor: '#ffea8a',
						fontSize: 13,
						fontWeight: 'bold',
						lineHeight: 1.23,
						color: ' #de3618',
					}}
				>
					{offContent} off{' '}
					{couponContext?.maxDeductCycles === 0
						? ''
						: `for ${timeDiff} ${cycleUnit}`}
				</span>
				<Stack vertical spacing="extraTight" alignment="center">
					<Stack alignment="baseline" spacing="extraTight">
						<TextStyle>
							<del>
								$
								{Number(
									displayedOriginalFee?.toFixed() || 0
								).toLocaleString()}
								/month
							</del>
						</TextStyle>
						<DisplayText size="large">
							<TextStyle variation="strong">
								<span style={{color: '#de3618'}}>
									{i18n.formatCurrency(displayedCurrentFee, {
										currency: plan?.pricing.currency,
									})}
								</span>
							</TextStyle>
							<span
								style={{
									fontSize: 14,
									color: '#de3618',
									fontWeight: 'normal',
								}}
							>
								/month
							</span>
						</DisplayText>
					</Stack>
					<Stack.Item />
				</Stack>
			</Stack>
		</Stack>
	);
};
