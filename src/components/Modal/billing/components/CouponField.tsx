import {Text<PERSON><PERSON>, Stack, Button, Link} from '@shopify/polaris';
import React, {useState, useEffect} from 'react';

export default React.memo(
	({
		coupon,
		setCoupon,
		loading,
	}: {
		setCoupon: (coupon: string) => any;
		coupon?: string;
		loading: boolean;
	}) => {
		const [localValue, setValue] = useState(coupon || '');
		useEffect(() => {
			// FIXME: change setCoupon from useGetCoupons
			setValue(coupon || '');
			setCoupon(coupon || '');
		}, [coupon]);
		const [showFormField, setToShow] = useState(false);

		if (!showFormField && !localValue) {
			return (
				<Stack alignment="center">
					<Link onClick={() => setToShow(true)}>
						Have coupon code?
					</Link>
				</Stack>
			);
		}

		return (
			<Stack spacing="tight" wrap={false} distribution="fill">
				<Stack.Item fill>
					<TextField
						label="Apply"
						labelHidden
						name="coupon"
						value={localValue}
						onChange={v => setValue(v)}
						onClearButtonClick={() => {
							setValue('');
							setCoupon('');
						}}
						clearButton
						placeholder="Coupon code"
						connectedRight={
							<Button
								loading={loading}
								onClick={() => {
									setCoupon(localValue);
								}}
							>
								Apply
							</Button>
						}
					/>
				</Stack.Item>
			</Stack>
		);
	}
);
