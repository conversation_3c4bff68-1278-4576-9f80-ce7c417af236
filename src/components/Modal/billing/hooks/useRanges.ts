import {IPlan} from 'aftershipBillingUi';
import {useEffect, useState} from 'react';

import {gaClick} from 'utils/gtag';

export const useRanges = (planList: IPlan[], defaultPlanCode: string) => {
	const [planCode, setPlanCode] = useState(defaultPlanCode);
	const enterPrisePlan = planList.find(({isEnterprise}) => isEnterprise)!;
	useEffect(() => {
		setPlanCode(defaultPlanCode);
	}, [defaultPlanCode]);

	const rawRanges = planList
		.filter(({isEnterprise}) => !isEnterprise)
		.map(plan => ({
			quota: plan.service.quotas[0].quota,
			code: plan.code,
			isEnterprise: plan.isEnterprise,
		}))
		.sort(({quota: quota1}, {quota: quota2}) => (quota1 > quota2 ? 1 : -1));
	const ranges = rawRanges.concat(
		enterPrisePlan
			? [
					{
						quota: rawRanges[rawRanges.length - 1].quota,
						code: enterPrisePlan.code,
						isEnterprise: true,
					},
			  ]
			: []
	);
	const rangeValue = ranges.findIndex(({code}) => planCode === code);
	const plan = ranges.find(({code}) => planCode === code);

	useEffect(() => {
		const text = document.querySelector<HTMLElement>(
			'.Polaris-RangeSlider-SingleThumb__OutputText'
		);
		if (text?.innerHTML) {
			text.innerHTML = plan?.isEnterprise
				? plan?.quota.toLocaleString() + '+'
				: String(plan?.quota.toLocaleString());
		}
	}, [planCode]);

	return {
		handleRangeSliderChange: (expectedIndex: number) => {
			const code = ranges.find(
				(range, index) => expectedIndex === index
			)?.code;
			if (code) {
				setPlanCode(code);
				gaClick('E10097', {
					extraParams: {planCode: code},
				});
			}
		},
		planCode,
		min: 0,
		max: ranges.length - 1,
		minQuotas: ranges[0].quota,
		maxQuotas: ranges[ranges.length - 1].quota,
		rangeValue,
		plan,
	};
};
