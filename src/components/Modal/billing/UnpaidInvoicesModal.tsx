import {Card, Stack, TextStyle} from '@shopify/polaris';
import {
	usePayUnpaidInvoices,
	useUnpaidInvoices,
	useBillingPlans,
	useBillingGlobalState,
} from 'aftershipBillingUi';
import React, {useLayoutEffect, useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {useLocation} from 'react-router';
import {usePrevious, useInterval} from 'react-use';

import {addToast} from 'actions/toast';
import Modal from 'components/ModalWithoutCloseButton';
import SpinLoading from 'components/SpinLoading';
import {
	useSuspendedSubscription,
	useGetOneChannelPlan,
} from 'hooks/billings/useSubscription';
import history from 'utils/history';

const getIsDone = (current: boolean, previous?: boolean) => {
	return previous !== undefined && previous !== current && current === false;
};

function LoopGetSubscription({callback}: {callback: Function}) {
	const {initialized} = useBillingGlobalState();
	const randomPlanId = useGetOneChannelPlan();
	const {getSubscriptions} = useBillingPlans(randomPlanId);
	const previousInitialized = usePrevious(initialized);
	const {subscription: suspendedSubscription} = useSuspendedSubscription();
	const previousSuspendSubscription = usePrevious(suspendedSubscription);
	const {unpaidInvoices} = useUnpaidInvoices();

	useInterval(() => {
		if (suspendedSubscription && !unpaidInvoices.length) {
			getSubscriptions();
		}
	}, 2000);

	useEffect(() => {
		if (!previousInitialized) {
			return;
		}
		if (previousSuspendSubscription && !suspendedSubscription) {
			callback();
		}
	}, [previousSuspendSubscription?.id, suspendedSubscription?.id]);

	return <></>;
}

function InvoicesModal() {
	const {loading, error} = usePayUnpaidInvoices();
	const dispatch = useDispatch();
	const location = useLocation();
	const previousLoading = usePrevious(loading || false);

	const isPostDone = getIsDone(loading, previousLoading);
	useLayoutEffect(() => {
		if (isPostDone && !error) {
			dispatch(
				addToast({
					message: 'Payment finished.',
				})
			);
			history.push(location.pathname);
		}
	}, [isPostDone, error]);

	return (
		<>
			<Modal open={loading} title="Processing payment">
				<Card>
					<Card.Section>
						<TextStyle>
							We are processing your payment, please wait a few
							seconds.
						</TextStyle>
						<Stack vertical alignment="center">
							<Stack.Item />
							<Stack.Item />
							<SpinLoading />
							<Stack.Item />
						</Stack>
					</Card.Section>
				</Card>
			</Modal>
			<LoopGetSubscription
				callback={() => {
					history.push('/settings/billing');
				}}
			/>
		</>
	);
}

export default InvoicesModal;
