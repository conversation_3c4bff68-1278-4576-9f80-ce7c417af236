import {
	StorageType,
	useBackendStorage,
	useTrackAdminActivity,
	useGetProductCode,
	CommonTipModal,
	NotificationUpgradeModal as UpgradeModal,
	useNotificationUpgradeUtils,
	useNiceModal,
	captureData,
	useSMSPhoneNumber,
	useRegistrationFlowStore,
} from 'aftershipNotification';
import React, {FC, useEffect} from 'react';

import {NOTIFICATION_UPGRADE_MODAL} from 'constants/Modals';
import {useModal} from 'hooks';
import {useIsAllinoneEmail} from 'pages/Notifications/Setting/hooks/useIsAllinoneEmail';
import emitter from 'utils/eventBus';

const NotificationUpgradeModal: FC = () => {
	const {productCode} = useGetProductCode();
	const isAllInOne = useIsAllinoneEmail();
	const {fetchSMSPhoneNumbers} = useSMSPhoneNumber();
	const {opened, open, close} = useModal(NOTIFICATION_UPGRADE_MODAL);
	const {toggleModal} = useRegistrationFlowStore(state => ({
		toggleModal: state.toggleModal,
	}));
	const {
		smsStatus,
		migrationSchedule,
		canCustomerMigrate,
		getCanCustomerMigrate,
		addToUpgradeListFromContract,
		updateMigrationScheduleToSuspend,
	} = useNotificationUpgradeUtils({
		skip: isAllInOne,
	});

	// 当前没有 sales order 维度的storage接口,
	// 只能只用 organization storage 使用sales_order_id 作为 key
	const {
		value: migrationModalVisible,
		setStorage: updateMigrationModalVisible,
	} = useBackendStorage({
		initFetch: true,
		key: migrationSchedule?.sales_order_id
			? `${migrationSchedule.sales_order_id}_migration_modal`
			: ``,
		storageType: StorageType.OrganizationStorages,
		initValue: false,
	});

	useEffect(() => {
		// 如果已经是 all in one 的用户，则不显示升级弹窗
		if (isAllInOne) return;
		// 针对最初始化的用户，显示升级弹窗
		if (
			canCustomerMigrate?.can_upgrade &&
			migrationModalVisible === false &&
			migrationSchedule?.migration_status === 'initial'
		) {
			open();
		}
	}, [
		isAllInOne,
		migrationModalVisible,
		canCustomerMigrate?.can_upgrade,
		migrationSchedule?.migration_status,
	]);

	const emitRefresh = () => {
		emitter.emit(
			'refresh_notification_upgrade',
			migrationSchedule?.sales_order_id
		);
	};

	const successModal = useNiceModal(CommonTipModal, {
		title: "Congratulations, you're now in the queue!",
		content:
			"We'll find a time when you're logged out to complete the upgrade.",
		primaryAction: {
			content: 'Got it',
			onAction: () => {
				emitRefresh();
				captureData('modal_click', {
					extraParams: {
						sms_status: smsStatus,
						modal_name: 'upgrade_guide_public',
						button_name: 'go_it',
					},
				});
				successModal.hide();
			},
		},
		onClose: () => {
			captureData('modal_click', {
				extraParams: {
					sms_status: smsStatus,
					modal_name: 'upgrade_guide_public',
					button_name: 'upgrade_close',
				},
			});
		},
	});

	const laterModal = useNiceModal(CommonTipModal, {
		title: 'Join upgrade queue later',
		content: `When you're ready, you can join the upgrade queue from the Notifications page.`,
		primaryAction: {
			content: 'Got it',
			onAction() {
				laterModal.hide();
			},
		},
	});

	const handleOnClose = () => {
		updateMigrationModalVisible(true).finally(() => emitRefresh());
		close();
	};

	const handleOnLater = () => {
		handleOnClose();
		laterModal.show();
	};

	const handleOnUpgrade = async () => {
		handleOnClose();

		const canCustomerMigrate = await getCanCustomerMigrate();
		const phoneNumbers = await fetchSMSPhoneNumbers();

		// 没有开启 sms notification 的用户, 直接加入升级队列
		if (!canCustomerMigrate?.canCustomerMigrate?.enabled_sms) {
			await addToUpgradeListFromContract();
			successModal.show();
			return;
		}

		// 开启了 sms notification
		if (canCustomerMigrate?.canCustomerMigrate?.enabled_sms) {
			// 没有设置 sms phone number, 则打开设置 sms phone number 的 modal
			if (!phoneNumbers?.length) {
				toggleModal('notice', true);
				return;
			}

			// 如果其中有号码正在验证中 或者 验证失败，则暂停升级队列
			if (
				phoneNumbers.some(phoneNumber =>
					['verifying', 'rejected'].includes(phoneNumber.status)
				)
			) {
				await updateMigrationScheduleToSuspend();
				successModal.show();
				return;
			}

			// 如果全部号码已经验证过，则加入升级队列
			if (
				phoneNumbers.every(phoneNumber =>
					['unverified', 'active'].includes(phoneNumber.status)
				)
			) {
				await addToUpgradeListFromContract();
				successModal.show();
			}
		}
	};

	// 上报心跳(每分钟)
	useTrackAdminActivity({
		// 有以下情况不上报心跳:
		// 1. 不在在 tracking app 的时候
		// 2. 已经是 all in one
		// 3. migration_status 为 null 该 account 不在迁移名单内
		skip:
			productCode !== 'aftership' ||
			isAllInOne ||
			!migrationSchedule?.migration_status,
		delay: 1000 * 60,
		immediate: true,
		input: {
			activity_type: 'Heartbeat',
		},
	});

	return (
		<UpgradeModal
			open={opened}
			onClose={handleOnClose}
			onUpgrade={handleOnUpgrade}
			onLater={handleOnLater}
			smsStatus={smsStatus as any}
		/>
	);
};

export default NotificationUpgradeModal;
