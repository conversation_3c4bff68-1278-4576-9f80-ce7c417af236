import {gaClickFn} from '@aftership/datacat';
import {Button} from '@shopify/polaris';
import {kebabCase} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';

import Switch from 'components/Switch';
import './index.scss';

interface Props {
	disabled?: boolean;
	panelID: string;
	tag: string;
	title: string;
	value: boolean;
	onChange: (value: boolean) => void;
}

export const EnableItem: React.FC<Props> = ({
	panelID,
	tag,
	title,
	onChange,
	value,
	disabled,
}) => {
	const {t} = useTranslation();
	// /notifications/setting/${kebabCase(
	// 	tag
	// )}/template/sms/edit`
	const getEditLink = () => {
		if (panelID === 'email_to_subscriber') {
			return `/notifications/setting/to-subscribers/${kebabCase(
				tag
			)}/template/email/edit`;
		}
		if (panelID === 'sms_to_subscriber') {
			return `/notifications/setting/${kebabCase(
				tag
			)}/template/sms/edit?target=subscriber`;
		}
		if (panelID === 'messenger_to_subscriber') {
			return null;
		}
		return null;
	};
	const editLink = getEditLink();
	return (
		<div className="notification-list-enable-item">
			<div className="title">{title}</div>
			<div style={{display: 'flex', gap: 10}}>
				<Switch onChange={onChange} value={value} disabled={disabled} />
				{editLink && (
					<Button
						plain
						disabled={disabled}
						onClick={() => {
							window.open(editLink, '_blank');
							gaClickFn('E10020');
						}}
					>
						{t('EDIT_47fc5')}
					</Button>
				)}
			</div>
		</div>
	);
};
