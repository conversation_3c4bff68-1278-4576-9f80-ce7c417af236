import {LockMinor} from '@shopify/polaris-icons';
import {get, noop} from 'lodash';
import React, {useState} from 'react';
import {useDispatch} from 'react-redux';
import {change} from 'redux-form';

import {useConnectionsAdaptiveQuery} from '@graphql/generated';
import ChangeFacebookPageModal from 'components/Modal/messenger/ChangeFacebookPageModal';
import ConnectFacebookModal from 'components/Modal/messenger/ConnectFacebookModal';
import {NOTIFICATION_SETTINGS} from 'constants/FormNames';
import {
	NotificationType,
	NOTIFICATION_ALLOW_TYPES,
} from 'constants/billings/features';
import {useFeatureValidator} from 'hooks/billings';
import SettingToggleField from 'pages/components/Form/SettingToggleField';

type Props = {
	name: string;
	status: string;
	loading: boolean;
	updating: boolean;
};

const FacebookToggle: React.FC<Props> = ({loading, updating, name}) => {
	const [upgradeMessenger, canChangeMessenger] = useFeatureValidator(
		feature =>
			feature.code === NOTIFICATION_ALLOW_TYPES &&
			Boolean(get(feature, `options.${NotificationType.messenger}`)),
		undefined,
		{code: `${NOTIFICATION_ALLOW_TYPES}_${NotificationType.messenger}`}
	);

	const dispatch = useDispatch();

	const {
		data: hasConnection,
		isLoading: isLoadingConnections,
		refetch,
	} = useConnectionsAdaptiveQuery(undefined, {
		select: data =>
			data.connectionsAdaptive.nodes.some(
				node => node.slug === 'messenger'
			),
	});

	const [openConnectModal, setOpenConnectModal] = useState(false);

	const [changeModalProps, openChangeModalProps] = useState({
		open: false,
		connectionId: '',
	});

	return (
		<>
			<ConnectFacebookModal
				open={openConnectModal}
				onClose={() => setOpenConnectModal(false)}
				onConnect={connectionId =>
					openChangeModalProps({open: true, connectionId})
				}
			/>

			<ChangeFacebookPageModal
				{...changeModalProps}
				// disable closing
				noClose
				onClose={noop}
				onChange={() => {
					openChangeModalProps({open: false, connectionId: ''});
					// refetch connections
					refetch();
					// enable trigger after finishing connecting
					dispatch(change(NOTIFICATION_SETTINGS, name, true));
				}}
			/>

			<SettingToggleField
				name={name}
				// @ts-ignore
				text={{
					pre: 'Facebook Messenger',
					suf: 'Messenger',
				}}
				icon={!canChangeMessenger ? LockMinor : undefined}
				shouldFieldChange={() => {
					if (!canChangeMessenger) {
						upgradeMessenger();
						return false;
					}

					if (!hasConnection) {
						setOpenConnectModal(true);
						return false;
					}

					return true;
				}}
				loading={loading || isLoadingConnections}
				updating={updating}
			/>
		</>
	);
};

export default FacebookToggle;
