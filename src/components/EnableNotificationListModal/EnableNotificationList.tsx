import {Banner, Card, Modal, Tabs} from '@shopify/polaris';
import {
	BillingBadgeTooltip,
	FeatureSlugs,
	BillingLockPage,
	SubscriberFlows,
} from 'aftershipNotification';
import {useFormikContext} from 'formik';
import {cloneDeep, get, set} from 'lodash';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {NOTIFICATION_TRIGER_LIST} from 'constants/Modals';
import {RBAC_ACTION, RBAC_RESOURCE} from 'constants/RbacAction';
import {useModal} from 'hooks';
import {useCanAction} from 'hooks/useCanAction';
import useGetOrganizationOwner from 'hooks/useGetOrganizationOwner';
import {
	IUserNotificationTriggers,
	TRACKING_STATUS,
	useUserNotificationTriggers,
} from 'hooks/user/useUserNotificationTriggers';
import {useIsAllinoneEmail} from 'pages/Notifications/Setting/hooks/useIsAllinoneEmail';
import {differenceObject} from 'utils/object';

import {EnableItem} from './EnableItem';

// need https://secure.aftership.io/json/bootstrap add OutForDelivery
const Items = [
	{tag: 'InfoReceived', title: 'INFO-RECEIVED_C'},
	{tag: 'InTransit', title: 'IN-TRANSIT_C'},
	{tag: 'OutForDelivery', title: 'OUT-FOR-DELIVERY_C'},
	{tag: 'AvailableForPickup', title: 'AVAILABLEFORPICKUP_C'},
	{tag: 'Delivered', title: 'DELIVERED_C'},
	{tag: 'Exception', title: 'EXCEPTION_C'},
	{tag: 'AttemptFail', title: 'ATTEMPT-FAIL_C'},
];

const EnableNotificationList: React.FC<{
	tab: number;
	opened: boolean;
	close: () => void;
	updateTriggers: (args: any) => void;
	allin1Trigger: {
		enable_email_to_subscriber: string[];
		enable_sms_to_subscriber: string[];
		getSubscribeTriggers: () => void;
	};
	featureSlugs?: string[];
}> = ({
	tab = 0,
	opened,
	close,
	updateTriggers,
	allin1Trigger,
	featureSlugs = [FeatureSlugs.All_In_One_Tracking_Notification_Billing],
}) => {
	const {t} = useTranslation();
	const {setFieldValue} = useFormikContext();
	const isAllinone = useIsAllinoneEmail();
	const {can: btpCanEdit} = useCanAction(
		RBAC_ACTION.EDIT,
		RBAC_RESOURCE.TRACKING_PAGE
	);
	const {can: notificationCanEdit} = useCanAction(
		RBAC_ACTION.EDIT,
		RBAC_RESOURCE.NOTIFICATIONS_FLOW
	);
	const {getSubscribeTriggers} = allin1Trigger;
	const {data} = useGetOrganizationOwner();
	const ownerEmail = data?.email;

	const [selected, setSelected] = useState(tab);
	const {data: initialValues} = useUserNotificationTriggers();
	const [notificationList, setNotificationList] = useState(initialValues);
	useEffect(() => {
		setNotificationList(initialValues);
	}, [initialValues]);

	useEffect(() => {
		const format = (list: Record<TRACKING_STATUS, boolean> | undefined) =>
			Object.keys(list || []).filter(v => list?.[v as TRACKING_STATUS]);

		if (!isAllinone) {
			setFieldValue(
				'user.enable_email_to_subscriber',
				format(notificationList?.email_to_subscriber)
			);
			setFieldValue(
				'user.enable_sms_to_subscriber',
				format(notificationList?.sms_to_subscriber)
			);
		}
		setFieldValue(
			'user.enable_messenger_to_subscriber',
			format(notificationList?.messenger_to_subscriber)
		);
	}, [notificationList, isAllinone]);

	const tabs = [
		{
			id: 'email',
			content: t('EMAILS_9790b', 'Emails'),
			panelID: 'email_to_subscriber',
		},
		{
			id: 'sms',
			content: t('SMS_4cecb', 'SMS'),
			panelID: 'sms_to_subscriber',
		},
	];

	const {panelID} = tabs[selected];
	const getEnableValue = (tag: string, panelID: string) =>
		get(notificationList, `${panelID}.${tag}`) || false;
	const enableChange = (enable: boolean, path: string) => {
		const notificationListCopy = cloneDeep(notificationList) || {};
		set(notificationListCopy, path, enable);
		updateTriggers({
			diff: differenceObject(notificationListCopy, notificationList),
			...notificationListCopy,
		});
		setNotificationList({
			...notificationList,
			...notificationListCopy,
		} as IUserNotificationTriggers);
	};
	return (
		<Modal
			title={
				<>
					<span>{t('MANAGE_TRI_1a529')}</span>
					<BillingBadgeTooltip
						featureSlugs={featureSlugs}
						customStyle={{
							marginLeft: '4px',
						}}
					/>
				</>
			}
			open={opened}
			onClose={close}
		>
			{(!notificationCanEdit || !btpCanEdit) && (
				<div style={{marginBottom: '16px'}}>
					<Banner status="critical">
						No permission. You can contact the organization owner{' '}
						<b>{ownerEmail}</b> to update your role to access{' '}
						<b>
							{!isAllinone
								? `${RBAC_RESOURCE.TRACKING_PAGE}/:edit`
								: `${RBAC_RESOURCE.NOTIFICATIONS_FLOW}/:edit`}
						</b>
						.
					</Banner>
				</div>
			)}
			<BillingLockPage
				featureSlugs={featureSlugs}
				customStyle={{
					height: '800px',
				}}
				targetClassName=".Polaris-Frame__Main"
			>
				<div className="notification-trigger-list">
					<div className="notification-trigger-list-inner">
						<Card>
							<div className="describe">
								{t('KEEP_YOUR_8e812')}
							</div>
							<Tabs
								tabs={tabs}
								selected={selected}
								onSelect={setSelected}
							/>
							{!isAllinone || selected === 2 ? (
								<div className="list">
									{Items.map(v => (
										<EnableItem
											disabled={!btpCanEdit}
											panelID={panelID}
											tag={v.tag}
											key={`${panelID}-${v.tag}`}
											title={t(v.title)}
											value={getEnableValue(
												v.tag,
												panelID
											)}
											onChange={(value: boolean) => {
												enableChange(
													value,
													`${panelID}.${v.tag}`
												);
											}}
										/>
									))}
								</div>
							) : (
								<>
									<SubscriberFlows
										subscriberType={tabs[selected].id}
										onUpdateStatus={() => {
											getSubscribeTriggers();
										}}
									/>
								</>
							)}
						</Card>
					</div>
				</div>
			</BillingLockPage>
		</Modal>
	);
};

/**
 * 如果连续切换的两个页面的 form 是同一个，form 的值会丢失，必须完全去掉此 form 才能解决 （unmount 没有用）
 * FIXME: remove redux form
 */
export default (props: any) => {
	const options = useModal(NOTIFICATION_TRIGER_LIST);
	if (!options.opened) {
		return null;
	}

	return (
		<EnableNotificationList
			tab={options.option.tab}
			opened={options.opened}
			close={options.close}
			updateTriggers={props.updateTriggers}
			allin1Trigger={options.option.allin1Trigger}
		/>
	);
};
