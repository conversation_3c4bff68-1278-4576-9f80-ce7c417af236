import {useAuth} from '@aftership/automizely-product-auth';
import {Modal} from '@shopify/polaris';
import {
	PlanGroupLevelEnum,
	usePlanTrialUnlockModal,
	useGetPlanTrial,
	useRegisterExplorePlanTrialCallback,
	useBillingGlobalState,
} from 'aftershipBillingUi';
import clsx from 'clsx';
import React, {
	useEffect,
	useState,
	useContext,
	useMemo,
	useCallback,
} from 'react';
import {useTranslation} from 'react-i18next';
import {useHistory, useLocation} from 'react-router';

import {
	TourContext,
	Tours,
	GuideExploreTransitTimeSteps,
	GuideExploreNotificationsSettingSteps,
	GuideTrackingPagesRecommendationsSteps,
	GuideTrackingPagesEmbeddedSteps,
	GuideTrackingPagesCustomDomainSteps,
} from 'components/Tour/TourContext';
import {GUIDE_EXPLORE_FEATURES_URL_HASH} from 'constants/GuideExploreFeatures';
import {
	NOTIFICATION_FLOW,
	TRANSIT_TIME_REPORT_CODE,
	RECOMMENDATION_AFTERSHIP_AI,
	TRACKING_PAGE_IFRAME,
	CONFIG_CUSTOM_DOMAIN_SSL,
} from 'constants/billings/features';
import {GUIDE_EXPLORE_FEATURES_SHOW} from 'constants/localstorageKeys';
import {usePremiumTrialGuidesConfig} from 'hooks';
import {useIsTrial} from 'hooks/billings/useIsFreeUser';
import {usePlanTrialAvailable} from 'hooks/billings/usePlanTrialAvailable';
import {
	useActiveSubscription,
	useGetTrackingQuota,
} from 'hooks/billings/useSubscription';
import useGetOrganizationOwner from 'hooks/useGetOrganizationOwner';
import useLocalStorageWithOrgId from 'hooks/useLocalStorageWithOrgId';
import {useIsAllinoneEmail} from 'pages/Notifications/Setting/hooks/useIsAllinoneEmail';
import {gaImpr, gaClick} from 'utils/gtag';

import NotificationsSettingTour from '../Tour/onboardingTours/ExploreFeatures/NotificationsSetting';
import TrackingPagesCustomDomainTour from '../Tour/onboardingTours/ExploreFeatures/TrackingPagesCustomDomain';
import TrackingPagesEmbeddedTour from '../Tour/onboardingTours/ExploreFeatures/TrackingPagesEmbedded';
import TrackingPagesRecommendationsTour from '../Tour/onboardingTours/ExploreFeatures/TrackingPagesRecommendations';
import TransitTimeTour from '../Tour/onboardingTours/ExploreFeatures/TransitTime';

import contImg1 from './assets/cont_1.png';
import contImg2 from './assets/cont_2.png';
import contImg3 from './assets/cont_3.png';
import contImg4 from './assets/cont_4.png';
import contImg5 from './assets/cont_5.png';
import {ReactComponent as IconDone} from './assets/done.svg';
import labelImg1 from './assets/label_1.svg';
import labelImg2 from './assets/label_2.svg';
import labelImg3 from './assets/label_3.svg';
import labelImg4 from './assets/label_4.svg';
import labelImg5 from './assets/label_5.svg';
import {ReactComponent as IconPraise} from './assets/praise.svg';
import styles from './index.module.scss';

const itemsData = [
	{
		img: contImg1,
		labelImg: labelImg1,
		label: 'TRANSIT_TI_a6794',
		title: 'MAKE_SMART_1ee02',
		text: 'OPTIMIZE_Y_d1980',
		link: '/dashboard/transit-time',
		tour: Tours.guideExploreTransitTimeStep1,
		tourName: GuideExploreTransitTimeSteps.guideExploreTransitTimeStep1,
		hasPraise: true,
		featureCode: TRANSIT_TIME_REPORT_CODE, // GA
	},
	{
		img: contImg2,
		labelImg: labelImg2,
		label: 'ADVANCED_E_4d14f',
		title: 'REDUCE_YOU_5efd0',
		text: 'REDUCE_CUS_dc746',
		link: '/notifications/setting',
		tour: Tours.guideExploreNotificationsSettingStep1,
		tourName:
			GuideExploreNotificationsSettingSteps.guideExploreNotificationsSettingStep1,
		hasPraise: true,
		featureCode: NOTIFICATION_FLOW, // GA
	},
	{
		img: contImg3,
		labelImg: labelImg3,
		label: 'AI_PRODUCT_4e4f2',
		title: 'CAPTURE_RE_bd76f',
		text: 'TURN_TRACK_09a40',
		link: '/tracking-pages',
		tour: Tours.guideExploreTrackingPagesRecommendationsStep1,
		tourName:
			GuideTrackingPagesRecommendationsSteps.guideExploreTrackingPagesRecommendationsStep1,
		hasPraise: true,
		featureCode: RECOMMENDATION_AFTERSHIP_AI, // GA
	},
	{
		img: contImg4,
		labelImg: labelImg4,
		label: 'EMBEDDED_T_e2213',
		title: 'CREATE_A_S_33db3',
		text: 'EMBEDDING_8e12f',
		link: '/embedded-pages',
		tour: Tours.guideExploreTrackingPagesEmbeddedStep1,
		tourName:
			GuideTrackingPagesEmbeddedSteps.guideExploreTrackingPagesEmbeddedStep1,
		featureCode: TRACKING_PAGE_IFRAME, // GA
	},
	{
		img: contImg5,
		labelImg: labelImg5,
		label: 'TRACKING_P_03cb1',
		title: 'ENHANCE_YO_0e464',
		text: 'USE_YOUR_O_99e30',
		link: '/tracking-pages',
		tour: Tours.guideExploreTrackingPagesCustomDomainStep1,
		tourName:
			GuideTrackingPagesCustomDomainSteps.guideExploreTrackingPagesCustomDomainStep1,
		featureCode: CONFIG_CUSTOM_DOMAIN_SSL, // GA
	},
];

const premiumTrialModalIgnorePathPrefix = '/pricing';

const ExploreFeaturesModal = () => {
	const [selectIndex, setSelectIndex] = useState(0);
	const {replace, push} = useHistory();
	const {t} = useTranslation();
	const isAllInOne = useIsAllinoneEmail();
	const {initTour} = useContext(TourContext);

	const {data: guideData} = usePremiumTrialGuidesConfig();

	const itemData = itemsData?.[selectIndex] || {};
	const isNotificationTour = itemData?.featureCode === NOTIFICATION_FLOW;
	const contImg = itemData?.img;
	const contTitle = t(itemData?.title);
	const contText = t(itemData?.text);
	const contLink =
		isAllInOne && isNotificationTour ? '/ens/flows' : itemData?.link;

	const tour = itemData?.tour as Tours;
	const tourName = itemData?.tourName;

	const isHideTour = isAllInOne && isNotificationTour;
	const featureCode = itemData?.featureCode;

	const transitFeatures = guideData?.transitFeatures;

	const isDoneMap = useMemo(() => {
		return [
			transitFeatures?.dashboardTransitTime,
			transitFeatures?.notificationsSetting,
			transitFeatures?.trackingPagesRecommendations,
			transitFeatures?.trackingPagesEmbedded,
			transitFeatures?.trackingPagesCustomDomain,
		];
	}, [transitFeatures]);

	useEffect(() => {
		const autoSelectIndex = isDoneMap?.findIndex(
			(val, index) => !isDoneMap[index]
		);

		setSelectIndex(autoSelectIndex > -1 ? autoSelectIndex : 0);
	}, [isDoneMap]);

	useEffect(() => {
		if (featureCode) {
			gaImpr('E10237', {
				extraParams: {
					features: featureCode,
				},
			});
		}
	}, [featureCode]);

	const showWelcome = !(
		Object.values(guideData?.transitFeatures || {})?.findIndex(i => i) > -1
	);

	return (
		<Modal
			open
			onClose={() => {
				replace('#');
			}}
			titleHidden
			title=""
			noScroll
		>
			<div>
				{showWelcome ? (
					<div className={styles.welcomeText}>
						{t('WELCOME_TO_aaf30')}
					</div>
				) : null}

				<div className={styles.title}>{t('LETS_EXPL_7cc47')}</div>

				<div className={styles.box}>
					<div className={styles.items}>
						{itemsData.map((item, index) => {
							const key = `${index}-${item.label}`;

							return (
								<div
									key={key}
									className={clsx([
										styles.item,
										selectIndex === index
											? styles.itemActive
											: null,
									])}
									onClick={() => {
										setSelectIndex(index);
									}}
									tabIndex={index}
									role="button"
								>
									<img src={item.labelImg} alt={item.label} />
									<div className={styles.itemText}>
										{t(item.label)}
									</div>
									{!isDoneMap?.[index] && item.hasPraise ? (
										<IconPraise />
									) : null}
									{isDoneMap?.[index] ? <IconDone /> : null}
								</div>
							);
						})}
					</div>
					<div className={styles.cont}>
						<img src={contImg} alt="cont img" />
						<div className={styles.contTitle}>{contTitle}</div>
						<div className={styles.contText}>{contText}</div>
						<button
							onClick={() => {
								contLink && push(contLink);
								if (isHideTour) {
									return;
								}
								setTimeout(() => {
									// eslint-disable-next-line no-underscore-dangle
									window.__GUIDE_EXPLORE_FEATURES_MODE__ =
										true;

									initTour(tour, tourName);
								}, 1200);

								gaClick('E10238', {
									extraParams: {
										features: featureCode,
									},
								});
							}}
							type="button"
						>
							Explore this feature
						</button>
					</div>
				</div>
			</div>
		</Modal>
	);
};

const ExploreFeaturesModalWrap = () => {
	const history = useHistory();
	const location = useLocation();

	const {initialized} = useBillingGlobalState();
	const canUsePlanTrial = usePlanTrialAvailable();

	const {subscription} = useActiveSubscription({onlyPaid: true});
	const isTrialUser = useIsTrial();
	const {planTrials} = useGetPlanTrial();

	const {data: owner} = useGetOrganizationOwner();
	const [{user}] = useAuth();
	const isOwner = owner && user && owner.id === user.account_id;

	const {data: guideData} = usePremiumTrialGuidesConfig();
	const [canShowUnlockModal, setCanShowUnlockModal] =
		useLocalStorageWithOrgId<boolean>(GUIDE_EXPLORE_FEATURES_SHOW, true);

	const canOpenPlanTrial = Boolean(
		initialized &&
			isOwner &&
			canUsePlanTrial &&
			!isTrialUser &&
			(!subscription || subscription.plan.group.level < 50000) &&
			(planTrials || []).length < 1 &&
			// history.location.pathname === '/' &&
			guideData?.hidePopup === false
	);

	const [isOpen, setIsOpen] = useState(false);

	const {canCreatePlanTrial} = useGetPlanTrial({
		lowestUnlockPlanLevel: PlanGroupLevelEnum.Essentials,
	});

	const onModalClose = useCallback(() => {
		setCanShowUnlockModal(false);
	}, [setCanShowUnlockModal]);

	const {open: openPlanTrialUnlockModal} = usePlanTrialUnlockModal({
		lowestUnlockPlanLevel: PlanGroupLevelEnum.Essentials,
		onModalClose,
	});

	const quotaData = useGetTrackingQuota();

	const reachQuotaLimit = useMemo(() => {
		return quotaData?.quota >= 500;
	}, [quotaData?.quota]);

	useRegisterExplorePlanTrialCallback({
		id: 'as_guide_explore',
		callback: () => {
			const isFLowEditorPath =
				history.location?.pathname?.includes('ens/flow-editor');
			if (isFLowEditorPath) {
				return;
			}
			history.replace(GUIDE_EXPLORE_FEATURES_URL_HASH);
		},
	});

	useEffect(() => {
		const isInPricingPage = location.pathname.includes(
			premiumTrialModalIgnorePathPrefix
		);
		if (
			canOpenPlanTrial &&
			canCreatePlanTrial &&
			canShowUnlockModal &&
			reachQuotaLimit &&
			!isInPricingPage
		) {
			openPlanTrialUnlockModal({
				promotion: {
					type: 'plan-trial',
					id: 'explore-features-modal',
					featureCode: 'auto-open',
				},
			});
		}
	}, [
		canCreatePlanTrial,
		canOpenPlanTrial,
		canShowUnlockModal,
		openPlanTrialUnlockModal,
		location.pathname,
		reachQuotaLimit,
	]);

	useEffect(() => {
		setIsOpen(history.location.hash === GUIDE_EXPLORE_FEATURES_URL_HASH);

		const unlisten = history.listen(newLocation => {
			if (newLocation.hash === GUIDE_EXPLORE_FEATURES_URL_HASH) {
				setIsOpen(true);

				// eslint-disable-next-line no-underscore-dangle
				window.__GUIDE_EXPLORE_FEATURES_MODE__ = false;
			} else {
				setIsOpen(false);
			}
		});

		return () => {
			unlisten();
		};
	}, []);

	return (
		<>
			{isOpen ? (
				<ExploreFeaturesModal />
			) : (
				<>
					<TransitTimeTour />
					<NotificationsSettingTour />
					<TrackingPagesRecommendationsTour />
					<TrackingPagesEmbeddedTour />
					<TrackingPagesCustomDomainTour />
				</>
			)}
		</>
	);
};

export default ExploreFeaturesModalWrap;
