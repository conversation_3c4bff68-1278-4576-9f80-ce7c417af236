:global(.Polaris-Modal-Dialog__Modal):has(.title) {
	max-width: 900px;

	:global(.Polaris-Modal-CloseButton) {
		--p-icon: #8c9196;
		margin: 10px 2px 0 0;
		:global(.Polaris-Icon) {
			width: 16px;
			height: 16px;
		}
	}
}

.welcomeText {
	margin: 32px 0 -24px;
	font-size: 24px;
	font-weight: 400;
	line-height: 32px;
	text-align: center;
}

.title {
	margin: 32px 0 20px;
	font-size: 24px;
	font-weight: 600;
	line-height: 32px;

	text-align: center;
}

.box {
	display: flex;
	margin: 0 32px 32px;
}

.item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin: 0 0 8px;
	padding: 8px 12px;
	width: 280px;
	border-radius: 4px;
	cursor: pointer;

	&.itemActive,
	&:hover {
		background-color: #f1f2f3;
	}
}

.itemText {
	flex: 1;
	margin: 0 8px;
}

.cont {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: 0 0 0 16px;
	padding: 24px;
	width: 540px;
	min-height: 400px;
	border-radius: 4px;
	line-height: 0;
	background-color: #f5f5f5;
	text-align: center;

	img {
		margin: 0 0 16px;
		max-width: 100%;
		min-height: 210.5px;
		object-fit: contain;
		object-position: center;
	}

	.contTitle {
		margin: 0 0 4px;
		font-size: 14px;
		font-weight: 700;
		line-height: 20px;
	}

	.contText {
		margin: 0 0 17px;
		font-size: 14px;
		font-weight: 400;
		line-height: 20px;
	}

	button {
		border: none;
		width: 180px;
		height: 44px;
		padding: 12px 24px;
		border-radius: 4px;
		background-color: #ff6b2b;
		cursor: pointer;

		color: #fff;
		font-size: 14px;
		font-weight: 500;
		line-height: 20px;
		text-align: center;
	}
}
