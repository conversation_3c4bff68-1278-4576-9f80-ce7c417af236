import {Card, Icon, Stack, TextStyle} from '@shopify/polaris';
import {
	CircleInformationMajor,
	CancelSmallMinor,
	CircleTickMajor,
} from '@shopify/polaris-icons';
import React, {useMemo} from 'react';

import {classnames} from 'utils/classnames';

import styles from '../Notification.module.scss';

interface NotificationFCProps {
	type: 'info' | 'success';
	title: React.ReactNode;
	content: React.ReactNode;
	footer?: React.ReactNode;
	onClose?: () => void;
}

export const NotificationFC: React.FC<NotificationFCProps> = props => {
	const icon = useMemo(() => {
		if (props.type === 'success') {
			return CircleTickMajor;
		}
		return CircleInformationMajor;
	}, [props.type]);

	return (
		<div
			className={classnames(
				styles.notification,
				props.type === 'info' && styles.info,
				props.type === 'success' && styles.success
			)}
		>
			<Card
				title={
					<div className={styles.title}>
						<Stack spacing="tight" alignment="center">
							<div style={{width: 16, height: 16}}>
								<Icon source={icon} />
							</div>
							<TextStyle variation="strong">
								{props.title}
							</TextStyle>
						</Stack>
					</div>
				}
				actions={[
					{
						onAction: props.onClose,
						content: (
							<div className={styles.closeBtn}>
								<Icon source={CancelSmallMinor} />
							</div>
						) as any as string,
					},
				]}
			>
				<Card.Section fullWidth>
					<p className={styles.content}>{props.content}</p>
				</Card.Section>
				{props.footer && <Card.Section>{props.footer}</Card.Section>}
			</Card>
		</div>
	);
};
