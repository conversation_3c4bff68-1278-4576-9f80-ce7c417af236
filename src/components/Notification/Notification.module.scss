:global {
	#notification-container {
		position: fixed;
		z-index: 1003;
		right: 0px;
		top: 80px;
	}
}
.notificationWrapper {
	position: relative;
}
.info {
	svg {
		fill: #00a0ac;
	}
}
.success {
	.title {
		color: #007f5f;
	}

	svg {
		fill: #007f5f;
	}
}
.notification {
	position: relative;
	right: 20px;
	width: 340px;
	margin-bottom: 30px;
	animation: right-in 0.2s;
	transition: all 0.3s ease-in-out;
	.title {
		font-size: 16px;
	}
	.closeBtn {
		svg {
			fill: rgba(92, 95, 98, 1) !important;
		}
	}
	.content {
		padding: 0px 45px;
	}
	:global {
		.Polaris-Card__Section--fullWidth {
			padding-top: 8px;
		}
	}
}

@keyframes right-in {
	0% {
		right: 0;
	}
	100% {
		right: 20px;
	}
}
