// @ts-nocheck
import {AppProvider} from '@shopify/polaris';
import en from '@shopify/polaris/locales/en.json';
import React from 'react';
import ReactDOM from 'react-dom';
import uuid from 'uuid/v4';

import styles from './Notification.module.scss';
import {NotificationFC} from './components/NotificationFC';

interface NotificationParams {
	title: React.ReactNode;
	content: React.ReactNode;
	footer?: React.ReactNode;
	duration?: number;
	onClose?: () => void;
}

const Notification = {
	show: (params: NotificationParams & {type: 'info' | 'success'}) => {
		Notification.destroy();
		const id = uuid();
		let container = document.getElementById('notification-container');
		if (!container) {
			container = document.createElement('div');
			container.id = 'notification-container';
			document.body.appendChild(container);
		}
		const root = document.createElement('div');
		root.id = id;
		root.className = styles.notificationWrapper;
		container.appendChild(root);

		const onClose = () => {
			Notification.destroy(id);
			if (params.onClose) {
				params.onClose();
			}
		};

		ReactDOM.render(
			<AppProvider i18n={en}>
				<NotificationFC {...params} onClose={onClose} />
			</AppProvider>,
			root
		);

		setTimeout(() => {
			onClose();
		}, params.duration || 5000);

		return id;
	},
	info(params: NotificationParams) {
		Notification.show({...params, type: 'info'});
	},
	success(params: NotificationParams) {
		Notification.show({...params, type: 'success'});
	},
	destroy: (id?: string) => {
		const container = document.getElementById('notification-container');
		if (container) {
			if (id) {
				const ele = document.getElementById(id);
				if (ele) {
					ReactDOM.unmountComponentAtNode(ele);
					ele.parentNode?.removeChild(ele);
				}
			} else {
				while (
					container.firstChild &&
					container.firstChild instanceof HTMLElement
				) {
					ReactDOM.unmountComponentAtNode(container.firstChild);
					container.firstChild.remove();
				}
			}
		}
	},
};

export default Notification;
