import {Card} from '@shopify/polaris';
import {CTA, useBookDemo} from 'AftershipNavigation';
import React from 'react';
import {useTranslation} from 'react-i18next';

import BookDemoBar from 'components/BookDemoBar';

import styles from './FeatureStatement.module.scss';
import FeatureStatementItem from './FeatureStatementItem';

interface Props {
	items: {
		id: string;
		title: string;
		imgUrl: string;
		desc: string;
		action?: {
			content: string;
			onClick: () => void;
		};
	}[];
	isAnalytics?: boolean;
}

const FeatureStatementCard: React.FC<Props> = ({
	items,
	isAnalytics = false,
}) => {
	const {t} = useTranslation();
	const {hasBookDemo, openBookDemoModal} = useBookDemo();
	return (
		<Card
			title={
				isAnalytics
					? t('HOW_THESERE_bc8e4', 'How these reports help you')
					: t('HOW_IT_WORKS', 'How it works')
			}
		>
			<div className={styles.container}>
				{items.map(item => (
					<FeatureStatementItem
						key={item.id}
						id={item.id}
						imgUrl={item.imgUrl}
						title={item.title}
						desc={item.desc}
						action={item.action}
						isAnalytics={isAnalytics}
					/>
				))}
			</div>
			{hasBookDemo && (
				<BookDemoBar
					onClick={() => {
						openBookDemoModal(
							CTA.UNLOCK_ADVANCED_FEATURES,
							'Unlock advanced features'
						);
					}}
					content={
						isAnalytics
							? t(
									'SEE_HOW_TH_6c3de',
									'See how these reports could help you and your company.'
							  )
							: undefined
					}
				/>
			)}
		</Card>
	);
};

export default FeatureStatementCard;
