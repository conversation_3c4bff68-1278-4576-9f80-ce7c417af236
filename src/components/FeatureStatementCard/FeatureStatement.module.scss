.container {
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding: 36px;
	flex-wrap: nowrap;
	margin: auto;

	.item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 330px;
		max-width: 330px;
		border-radius: 8px;
		font-weight: 400;
		font-size: 14px;
		line-height: 20px;
		text-align: center;
		color: #202223;
		margin-left: 8px;

		.title {
			margin-top: 12px;
			font-weight: 700;
			font-size: 14px;
			line-height: 20px;
			text-align: center;
		}

		.hero {
			position: relative;
			width: 100%;
			min-height: 210px;
			border-radius: 8px;
			overflow: hidden;

			img {
				display: block;
				width: 100%;
				border-radius: 8px;
			}
		}

		.desc {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			width: 100%;
			height: 100%;
			flex-direction: column;
			backdrop-filter: blur(8px);
			text-align: center;
			z-index: 10;
			border-radius: 8px;
			background: rgba(255, 255, 255, 0.8);

			.btn {
				margin-top: 16px;
			}
		}

		.looseDesc {
			justify-content: space-around;
			border: 1px solid #E1E3E5;
		}
	}
}

@media screen and (max-width: 1368px) {
	.container {
		.item {
			.title {
				height: 24px;
				margin-bottom: 18px;
			}

			.desc {
				margin-bottom: 18px;
			}
		}
	}
}