import {Button} from '@shopify/polaris';
import React, {useState} from 'react';

import styles from './FeatureStatement.module.scss';

interface Props {
	id: string;
	title: string;
	imgUrl: string;
	desc: string;
	isAnalytics?: boolean;
	action?: {
		content: string;
		onClick: () => void;
	};
}

const FeatureStatementItem: React.FC<Props> = ({
	id,
	imgUrl,
	title,
	desc,
	action,
	isAnalytics,
}) => {
	const [showDesc, setShowDesc] = useState(false);
	return (
		<div className={styles.item} key={id}>
			<div
				className={styles.hero}
				onMouseEnter={() => {
					setShowDesc(true);
				}}
			>
				{showDesc && (
					<div
						className={`${styles.desc} ${
							isAnalytics ? styles.looseDesc : ''
						}`}
						onMouseLeave={() => {
							setShowDesc(false);
						}}
					>
						<div
							style={{
								margin: '12px 20px 0px 20px',
							}}
						>
							{desc}
						</div>
						{action && (
							<div className={styles.btn}>
								<Button onClick={action.onClick}>
									{action.content}
								</Button>
							</div>
						)}
					</div>
				)}
				<img src={imgUrl} alt={title} />
			</div>
			<div className={styles.title}>{title}</div>
		</div>
	);
};

export default FeatureStatementItem;
