import {TFunction} from 'react-i18next';

import BenchmarkCompare from 'img/featureStatement/BenchmarkCompare.png';
import BenchmarkInsight from 'img/featureStatement/BenchmarkInsight.png';
import BenchmarkPerformance from 'img/featureStatement/BenchmarkPerformance.png';
import accuracy from 'img/featureStatement/accuracy.png';
import carrierPerformance from 'img/featureStatement/carrierPerformance.png';
import deliveryRate from 'img/featureStatement/deliveryRate.png';
import distributions from 'img/featureStatement/distributions.png';
import easeTrack from 'img/featureStatement/easeTrack.jpg';
import exceptionInsights from 'img/featureStatement/exceptionInsights.png';
import exceptionOvertime from 'img/featureStatement/exceptionOvertime.png';
import exceptionRates from 'img/featureStatement/exceptionRates.png';
import insights from 'img/featureStatement/insights.png';
import notificationFunnel from 'img/featureStatement/notificationFunnel.png';
import notificationPerformance from 'img/featureStatement/notificationPerformance.png';
import notificationRevenue from 'img/featureStatement/notificationRevenue.png';
import ontimeRate from 'img/featureStatement/ontimeRate.png';
import orderFulfillmentVisibility from 'img/featureStatement/orderFulfillmentVisibility.jpg';
import orderToDeliveryOverTime from 'img/featureStatement/orderToDeliveryOverTime.png';
import orderToDeliveryProcessTime from 'img/featureStatement/orderToDeliveryProcessTime.png';
import orderToDeliveryTimeDistribution from 'img/featureStatement/orderToDeliveryTimeDistribution.png';
import realTimeUpdates from 'img/featureStatement/realTimeUpdates.jpg';
import realtimeTracking from 'img/featureStatement/realtimeTracking.png';
import recommendation from 'img/featureStatement/recommendation.png';
import reviewAnalytics from 'img/featureStatement/reviewAnalytics.png';
import reviewDetail from 'img/featureStatement/reviewDetail.png';
import reviewTotal from 'img/featureStatement/reviewTotal.png';
import shipmentsInsights from 'img/featureStatement/shipmentsInsights.png';
import shipmentsOverview from 'img/featureStatement/shipmentsOverview.png';
import shippingEfficiency from 'img/featureStatement/shippingEfficiency.png';
import shippingInfo from 'img/featureStatement/shippingInfo.png';
import shippingVolume from 'img/featureStatement/shippingVolume.png';
import trackingPageAssets from 'img/featureStatement/trackingPageAssets.png';
import trackingPageTotal from 'img/featureStatement/trackingPageTotal.png';

export enum FeatureStatementCardType {
	shipments = 'shipments',
	order_to_delivery_dashboard = 'order_to_delivery_dashboard',
	shipments_report = 'shipments_report',
	transit_time_report = 'transit_time_report',
	analytics_on_time_shipment = 'analytics_on_time_shipment',
	exception_shipments_report = 'exception_shipments_report',
	notification_report = 'notification_report',
	tracking_page_report = 'tracking_page_report',
	tracking_pages_shipment_reviews = 'tracking_pages_shipment_reviews',
	orders = 'orders',
	benchmarks_dashboard = 'benchmarks_dashboard',
	shipment_events_report = 'shipment_events_report',
}

export const getTranslateStatements = (
	t: TFunction<'translation', undefined>
) => ({
	[FeatureStatementCardType.shipments]: [
		{
			id: 'shipment-1',
			imgUrl: shippingInfo,
			title: t('GAIN_FULL_5a2f8'),
			desc: t('GAIN_FULL_87707'),
		},
		{
			id: 'shipment-2',
			imgUrl: realtimeTracking,
			title: t('GET_REAL_T_7d1e1'),
			desc: t('AFTER_SHIP_ca365'),
		},
		{
			id: 'shipment-3',
			imgUrl: accuracy,
			title: t('CUSTOMIZE_b1fca'),
			desc: t('FILTER_BY_5f600'),
		},
	],
	[FeatureStatementCardType.order_to_delivery_dashboard]: [
		{
			id: 'order-to-delivery-1',
			imgUrl: orderToDeliveryOverTime,
			title: t('VISUALIZEP_bed2c'),
			desc: t('IDENTIFYAN_83603'),
		},
		{
			id: 'order-to-delivery-2',
			imgUrl: orderToDeliveryTimeDistribution,
			title: t('ENSUREDELI_08700'),
			desc: t('MAKESUREYO_86b44'),
		},
		{
			id: 'order-to-delivery-3',
			imgUrl: orderToDeliveryProcessTime,
			title: t('TRACKYOURW_cd951'),
			desc: t('THEPROCESS_3800e'),
		},
	],
	[FeatureStatementCardType.shipments_report]: [
		{
			id: 'shipment-report-1',
			imgUrl: shipmentsOverview,
			title: t('GAIN_AN_OV_f1917'),
			desc: t('KEEP_TRACK_1f042'),
		},
		{
			id: 'shipment-report-2',
			imgUrl: shipmentsInsights,
			title: t('GET_INSIGH_f655a'),
			desc: t('GAIN_GREAT_1d1b0'),
		},
		{
			id: 'shipment-report-3',
			imgUrl: shippingVolume,
			title: t('COMPARE_SH_14af6'),
			desc: t('SEE_WHICH_549b1'),
		},
	],
	[FeatureStatementCardType.transit_time_report]: [
		{
			id: 'transit-time-report-1',
			imgUrl: shippingEfficiency,
			title: t('TRACK_YOUR_1c265'),
			desc: t('CUSTOMERS_2d9e4'),
		},
		{
			id: 'transit-time-report-2',
			imgUrl: insights,
			title: t('COMPARE_CA_8241a'),
			desc: t('EASILY_FIL_7eb39'),
		},
		{
			id: 'transit-time-report-3',
			imgUrl: deliveryRate,
			title: t('SEE_YOUR_C_09009'),
			desc: t('DELIVERING_7e1ac'),
		},
	],
	[FeatureStatementCardType.analytics_on_time_shipment]: [
		{
			id: 'on-time-report-1',
			imgUrl: ontimeRate,
			title: t('MONITOR_YO_ade49'),
			desc: t('YOUR_CUSTO_1a67d'),
		},
		{
			id: 'on-time-report-2',
			imgUrl: carrierPerformance,
			title: t('COMPARE_CA_ba864'),
			desc: t('EASILY_GAI_03324'),
		},
		{
			id: 'on-time-report-3',
			imgUrl: distributions,
			title: t('BE_AWARE_O_246aa'),
			desc: t('WE_AUTOMAT_3ea8a'),
		},
	],
	[FeatureStatementCardType.exception_shipments_report]: [
		{
			id: 'exception-report-1',
			imgUrl: exceptionOvertime,
			title: t('EVALUATE_T_d788d'),
			desc: t('LOST_DAMA_b5407'),
		},
		{
			id: 'exception-report-2',
			imgUrl: exceptionInsights,
			title: t('GET_A_DETA_56a4f'),
			desc: t('SEE_EVERYT_dade6'),
		},
		{
			id: 'exception-report-3',
			imgUrl: exceptionRates,
			title: t('AVOID_RISK_5892c'),
			desc: t('EASILY_VIE_cfab8'),
		},
	],
	[FeatureStatementCardType.notification_report]: [
		{
			id: 'notification-report-1',
			imgUrl: notificationPerformance,
			title: t('REVIEW_CUS_542af'),
			desc: t('SEE_HOW_CU_ad0ce'),
		},
		{
			id: 'notification-report-2',
			imgUrl: notificationFunnel,
			title: t('OPTIMIZE_Y_161a5'),
			desc: t('LEARN_WHIC_b51c6'),
		},
		{
			id: 'notification-report-3',
			imgUrl: notificationRevenue,
			title: t('TRACK_REVE_9512b'),
			desc: t('DETERMINE_e6eaa'),
		},
	],
	[FeatureStatementCardType.tracking_page_report]: [
		{
			id: 'tracking-page-report-1',
			imgUrl: trackingPageTotal,
			title: t('MEASURE_CU_68774'),
			desc: t('IMPROVE_TH_b8f1f'),
		},
		{
			id: 'tracking-page-report-2',
			imgUrl: trackingPageAssets,
			title: t('OPTIMIZE_F_1fd12'),
			desc: t('YOUR_TRACK_0b8d4'),
		},
		{
			id: 'tracking-page-report-3',
			imgUrl: recommendation,
			title: t('PERFECT_YO_93292'),
			desc: t('SEE_WHICH_bf472'),
		},
	],
	[FeatureStatementCardType.tracking_pages_shipment_reviews]: [
		{
			id: 'tracking-page-reviews-report-1',
			imgUrl: reviewTotal,
			title: t('KEEP_TRACK_76cec'),
			desc: t('GAIN_CLEAR_87608'),
		},
		{
			id: 'tracking-page-reviews-report-2',
			imgUrl: reviewDetail,
			title: t('DEEP_DIVE_cbcca'),
			desc: t('FILTER_REV_fee3a'),
		},
		{
			id: 'tracking-page-reviews-report-3',
			imgUrl: reviewAnalytics,
			title: t('ANALYZE_RE_f94b1'),
			desc: t('FILTER_BY_93802'),
		},
	],
	[FeatureStatementCardType.orders]: [
		{
			id: 'orders-1',
			imgUrl: orderFulfillmentVisibility,
			title: t('Unlock full order fulfillment visibility'),
			desc: t(
				'Stay informed of the shipping progress for each order. Easily locate and check up on unfulfilled orders that might need your attention.'
			),
		},
		{
			id: 'orders-2',
			imgUrl: easeTrack,
			title: t('Track shipments with ease'),
			desc: t(
				'No more endlessly searching for shipments. View all the shipments related to an order on a single page.'
			),
		},
		{
			id: 'orders-3',
			imgUrl: realTimeUpdates,
			title: t('Get real-time shipment updates'),
			desc: t(
				'See real-time updates, estimated delivery dates, and checkpoint info for each shipment right on the order details page.'
			),
		},
	],
	[FeatureStatementCardType.benchmarks_dashboard]: [
		{
			id: 'benchmarks-report-1',
			imgUrl: BenchmarkPerformance,
			title: t('CHECK_YOUR_d82bf', 'Check your performance at a glance'),
			desc: t(
				'QUICKLY_SE_8c6ea',
				'Quickly see your performance for key post-purchase metrics. Compare your results with industry peers, or across all industries.'
			),
		},
		{
			id: 'benchmarks-report-2',
			imgUrl: BenchmarkCompare,
			title: t('SEE_HOW_YO_57748', 'See how you compare to your peers'),
			desc: t(
				'PERCENTILE_589db',
				'Percentiles can help you better understand your performance and set improvement goals based on your peers.'
			),
		},
		{
			id: 'benchmarks-report-3',
			imgUrl: BenchmarkInsight,
			title: t(
				'FIND_INSIG_c78dd',
				'Find insights from detailed breakdowns'
			),
			desc: t(
				'DETAILED_B_91f02',
				'Detailed breakdowns into your metrics provide context and insights that help you pinpoint areas for improvement.'
			),
		},
	],
});
