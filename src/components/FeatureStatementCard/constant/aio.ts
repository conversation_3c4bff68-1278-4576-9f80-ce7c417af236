import {TFunction} from 'react-i18next';

import BenchmarkCompare from 'img/featureStatement/BenchmarkCompare.png';
import BenchmarkInsight from 'img/featureStatement/BenchmarkInsight.png';
import BenchmarkPerformance from 'img/featureStatement/BenchmarkPerformance.png';
import accuracy from 'img/featureStatement/accuracy.png';
import carrierPerformance from 'img/featureStatement/aio/carrierPerformance.png';
import deliverySpeeds from 'img/featureStatement/aio/deliverySpeeds.png';
import exceptionInsights from 'img/featureStatement/aio/exceptionInsights.png';
import exceptionOvertime from 'img/featureStatement/aio/exceptionOvertime.png';
import exceptionRates from 'img/featureStatement/aio/exceptionRates.png';
import firstAttemptDeliveryRate from 'img/featureStatement/aio/firstAttemptDeliveryRate.png';
import ontimeRate from 'img/featureStatement/aio/onTimeRate.png';
import optimizePerformance from 'img/featureStatement/aio/optimizePerformance.png';
import potentialDelays from 'img/featureStatement/aio/potentialDelays.png';
import reviewEngagement from 'img/featureStatement/aio/reviewEngagement.png';
import shipmentEventCarrier from 'img/featureStatement/aio/shipmentEventCarrier.png';
import shipmentEventRegional from 'img/featureStatement/aio/shipmentEventRegional.png';
import shipmentEventTrend from 'img/featureStatement/aio/shipmentEventTrend.png';
import shipmentsBreakdown from 'img/featureStatement/aio/shipmentsBreakdown.png';
import shipmentsInsights from 'img/featureStatement/aio/shipmentsInsights.png';
import shipmentsOverview from 'img/featureStatement/aio/shipmentsOverview.png';
import shippingEfficiency from 'img/featureStatement/aio/shippingEfficiency.png';
import trackRevenue from 'img/featureStatement/aio/trackRevenue.png';
import easeTrack from 'img/featureStatement/easeTrack.jpg';
import orderFulfillmentVisibility from 'img/featureStatement/orderFulfillmentVisibility.jpg';
import orderToDeliveryOverTime from 'img/featureStatement/orderToDeliveryOverTime.png';
import orderToDeliveryProcessTime from 'img/featureStatement/orderToDeliveryProcessTime.png';
import orderToDeliveryTimeDistribution from 'img/featureStatement/orderToDeliveryTimeDistribution.png';
import realTimeUpdates from 'img/featureStatement/realTimeUpdates.jpg';
import realtimeTracking from 'img/featureStatement/realtimeTracking.png';
import recommendation from 'img/featureStatement/recommendation.png';
import reviewAnalytics from 'img/featureStatement/reviewAnalytics.png';
import reviewDetail from 'img/featureStatement/reviewDetail.png';
import reviewTotal from 'img/featureStatement/reviewTotal.png';
import shippingInfo from 'img/featureStatement/shippingInfo.png';
import trackingPageAssets from 'img/featureStatement/trackingPageAssets.png';
import trackingPageTotal from 'img/featureStatement/trackingPageTotal.png';

import {FeatureStatementCardType} from '.';

export const getAllInOneTranslateStatements = (
	t: TFunction<'translation', undefined>
) => ({
	[FeatureStatementCardType.shipments]: [
		{
			id: 'shipment-1',
			imgUrl: shippingInfo,
			title: t('GAIN_FULL_5a2f8'),
			desc: t('GAIN_FULL_87707'),
		},
		{
			id: 'shipment-2',
			imgUrl: realtimeTracking,
			title: t('GET_REAL_T_7d1e1'),
			desc: t('AFTER_SHIP_ca365'),
		},
		{
			id: 'shipment-3',
			imgUrl: accuracy,
			title: t('CUSTOMIZE_b1fca'),
			desc: t('FILTER_BY_5f600'),
		},
	],
	[FeatureStatementCardType.order_to_delivery_dashboard]: [
		{
			id: 'order-to-delivery-1',
			imgUrl: orderToDeliveryOverTime,
			title: t('MONITOR_YO_16ec2'),
			desc: t('CHECK_THAT_0758d'),
		},
		{
			id: 'order-to-delivery-2',
			imgUrl: orderToDeliveryTimeDistribution,
			title: t('IDENTIFY_A_4f4f8'),
			desc: t('BY_ANALYZI_05182'),
		},
		{
			id: 'order-to-delivery-3',
			imgUrl: orderToDeliveryProcessTime,
			title: t('ANALYZE_YO_7253a'),
			desc: t('PROCESSING_4b494'),
		},
	],
	[FeatureStatementCardType.shipments_report]: [
		{
			id: 'shipment-report-1',
			imgUrl: shipmentsOverview,
			title: t('UNLOCK_KEY_68414'),
			desc: t('GAIN_A_BET_fcac1'),
		},
		{
			id: 'shipment-report-2',
			imgUrl: shipmentsInsights,
			title: t('GAIN_A_COM_50c15'),
			desc: t('EASILY_VIE_25fb5'),
		},
		{
			id: 'shipment-report-3',
			imgUrl: shipmentsBreakdown,
			title: t('EVALUATE_Y_ac629'),
			desc: t('DEEP_DIVE_5b8e2'),
		},
	],
	[FeatureStatementCardType.transit_time_report]: [
		{
			id: 'transit-time-report-1',
			imgUrl: shippingEfficiency,
			title: t('EXPLORE_TR_7b046'),
			desc: t('KEEP_TRACK_1bf7e'),
		},
		{
			id: 'transit-time-report-2',
			imgUrl: deliverySpeeds,
			title: t('COMPARE_CA_8241a'),
			desc: t('MAKE_INFOR_fd2f3'),
		},
		{
			id: 'transit-time-report-3',
			imgUrl: firstAttemptDeliveryRate,
			title: t('ENSURE_SLA_e15e7'),
			desc: t('ENSURE_YOU_5bf85'),
		},
	],
	[FeatureStatementCardType.analytics_on_time_shipment]: [
		{
			id: 'on-time-report-1',
			imgUrl: ontimeRate,
			title: t('MONITOR_KE_44e7b'),
			desc: t('QUICKLY_SE_3a6ab'),
		},
		{
			id: 'on-time-report-2',
			imgUrl: carrierPerformance,
			title: t('COMPARE_CA_ba864'),
			desc: t('TAKE_A_CLO_e37fc'),
		},
		{
			id: 'on-time-report-3',
			imgUrl: potentialDelays,
			title: t('EVALUATE_T_a5914'),
			desc: t('COMPARE_DE_dd4ab'),
		},
	],
	[FeatureStatementCardType.exception_shipments_report]: [
		{
			id: 'exception-report-1',
			imgUrl: exceptionOvertime,
			title: t('KEEP_TRACK_14f25'),
			desc: t('EXCEPTIONS_ce850'),
		},
		{
			id: 'exception-report-2',
			imgUrl: exceptionInsights,
			title: t('IDENTIFY_P_9747e'),
			desc: t('SEE_WHERE_dcf05'),
		},
		{
			id: 'exception-report-3',
			imgUrl: exceptionRates,
			title: t('EVALUATE_C_1f980'),
			desc: t('EASILY_EVA_b095a'),
		},
	],
	[FeatureStatementCardType.notification_report]: [
		{
			id: 'notification-report-1',
			imgUrl: reviewEngagement,
			title: t('REVIEW_CUS_542af'),
			desc: t('SEE_HOW_CU_ad0ce'),
		},
		{
			id: 'notification-report-2',
			imgUrl: optimizePerformance,
			title: t('OPTIMIZE_Y_ceac9'),
			desc: t('LEARN_WHIC_b51c6'),
		},
		{
			id: 'notification-report-3',
			imgUrl: trackRevenue,
			title: t('TRACK_REVE_9512b'),
			desc: t('DETERMINE_e6eaa'),
		},
	],
	[FeatureStatementCardType.tracking_page_report]: [
		{
			id: 'tracking-page-report-1',
			imgUrl: trackingPageTotal,
			title: t('MEASURE_CU_68774'),
			desc: t('IMPROVE_TH_b8f1f'),
		},
		{
			id: 'tracking-page-report-2',
			imgUrl: trackingPageAssets,
			title: t('OPTIMIZE_F_1fd12'),
			desc: t('YOUR_TRACK_0b8d4'),
		},
		{
			id: 'tracking-page-report-3',
			imgUrl: recommendation,
			title: t('PERFECT_YO_93292'),
			desc: t('SEE_WHICH_bf472'),
		},
	],
	[FeatureStatementCardType.tracking_pages_shipment_reviews]: [
		{
			id: 'tracking-page-reviews-report-1',
			imgUrl: reviewTotal,
			title: t('KEEP_TRACK_76cec'),
			desc: t('GAIN_CLEAR_87608'),
		},
		{
			id: 'tracking-page-reviews-report-2',
			imgUrl: reviewDetail,
			title: t('DEEP_DIVE_cbcca'),
			desc: t('FILTER_REV_fee3a'),
		},
		{
			id: 'tracking-page-reviews-report-3',
			imgUrl: reviewAnalytics,
			title: t('ANALYZE_RE_f94b1'),
			desc: t('FILTER_BY_93802'),
		},
	],
	[FeatureStatementCardType.orders]: [
		{
			id: 'orders-1',
			imgUrl: orderFulfillmentVisibility,
			title: t('Unlock full order fulfillment visibility'),
			desc: t(
				'Stay informed of the shipping progress for each order. Easily locate and check up on unfulfilled orders that might need your attention.'
			),
		},
		{
			id: 'orders-2',
			imgUrl: easeTrack,
			title: t('Track shipments with ease'),
			desc: t(
				'No more endlessly searching for shipments. View all the shipments related to an order on a single page.'
			),
		},
		{
			id: 'orders-3',
			imgUrl: realTimeUpdates,
			title: t('Get real-time shipment updates'),
			desc: t(
				'See real-time updates, estimated delivery dates, and checkpoint info for each shipment right on the order details page.'
			),
		},
	],
	[FeatureStatementCardType.benchmarks_dashboard]: [
		{
			id: 'benchmarks-report-1',
			imgUrl: BenchmarkPerformance,
			title: t('CHECK_YOUR_d82bf', 'Check your performance at a glance'),
			desc: t(
				'QUICKLY_SE_8c6ea',
				'Quickly see your performance for key post-purchase metrics. Compare your results with industry peers, or across all industries.'
			),
		},
		{
			id: 'benchmarks-report-2',
			imgUrl: BenchmarkCompare,
			title: t('SEE_HOW_YO_57748', 'See how you compare to your peers'),
			desc: t(
				'PERCENTILE_589db',
				'Percentiles can help you better understand your performance and set improvement goals based on your peers.'
			),
		},
		{
			id: 'benchmarks-report-3',
			imgUrl: BenchmarkInsight,
			title: t(
				'FIND_INSIG_c78dd',
				'Find insights from detailed breakdowns'
			),
			desc: t(
				'DETAILED_B_91f02',
				'Detailed breakdowns into your metrics provide context and insights that help you pinpoint areas for improvement.'
			),
		},
	],
	[FeatureStatementCardType.shipment_events_report]: [
		{
			id: 'shipment-events-report-1',
			imgUrl: shipmentEventTrend,
			title: t(
				'TRACK_TREN_05e59',
				'Track trends in your shipment events'
			),
			desc: t(
				'MONITOR_YO_2804b',
				'Monitor your shipment events over time to spot patterns, pinpoint issues, and measure the impact of improvements to your logistics.'
			),
		},
		{
			id: 'shipment-events-report-2',
			imgUrl: shipmentEventRegional,
			title: t('EXPLORE_EV_1b3d4', 'Explore events in each region'),
			desc: t(
				'IDENTIFY_P_edf8c',
				'Identify problematic areas and make shipping adjustments with a breakdown of events by state.'
			),
		},
		{
			id: 'shipment-events-report-3',
			imgUrl: shipmentEventCarrier,
			title: t('COMPARE_CA_ba864', 'Compare carrier performance'),
			desc: t(
				'SEE_WHICH_20f2e',
				'See which events your carriers regularly encounter and make data-driven decisions to enhance your shipping strategy.'
			),
		},
	],
});
