import {Button} from '@shopify/polaris';
import React from 'react';

import style from './BannerForPushCenter.module.scss';

interface Props {
	content: string | JSX.Element;
	action: {
		content: string;
		action: any;
	};

	secondaryAction: {
		content: string;
		action: any;
	};
}

export const BannerForPushCenter = ({
	content,
	action,
	secondaryAction,
}: Props) => {
	return (
		<div
			className={style.banner_wrapper}
			style={{borderBottom: '1px solid #E0B3B2'}}
		>
			<div style={{marginRight: '24px'}}>{content}</div>
			<div style={{display: 'flex', alignItems: 'center'}}>
				<div style={{marginRight: '12px'}}>
					<Button plain onClick={secondaryAction.action}>
						{secondaryAction.content}
					</Button>
				</div>
				<Button onClick={action.action}>{action.content}</Button>
			</div>
		</div>
	);
};
