import {useAuth} from '@aftership/automizely-product-auth';
import {
	DisplayText,
	Modal as ShopifyModal,
	Stack,
	TextStyle,
} from '@shopify/polaris';
import React from 'react';
import {Trans, useTranslation} from 'react-i18next';
import {matchPath, useLocation} from 'react-router';
import {useLocalStorage} from 'react-use';

import {useTrackingPageSettingsQuery} from '@graphql/generated';
import {I18nLink} from 'components/I18n';
import Modal from 'components/Modal';
import {PRODUCT_RECOMMENDATION_SCOPE_MODAL_VISIBLE} from 'constants/localstorageKeys';
import {useOrganizationId} from 'hooks';
import {useShopifyConnections} from 'hooks/connection';
import modalIllustration from 'img/products/illustration.png';
import {getGoogleFormUrl} from 'utils/contactUs';
import history from 'utils/history';

export default function CheckScopeModal() {
	const {t} = useTranslation();
	const [{user}] = useAuth();
	const organizationId = useOrganizationId();
	const [, setVisible] = useLocalStorage(
		PRODUCT_RECOMMENDATION_SCOPE_MODAL_VISIBLE + organizationId,
		true
	);

	const location = useLocation();

	const match = matchPath<{pageId: string}>(location.pathname, {
		path: '/tracking-pages/:pageId/edit',
	});

	const {data: trackingPage} = useTrackingPageSettingsQuery(
		{trackingPageId: match?.params.pageId},
		{
			enabled:
				Boolean(match?.params.pageId) &&
				!match?.params.pageId.includes('mock'),
		}
	);

	const {data: shopifyScopeConnections} = useShopifyConnections();

	const upgradeItem = shopifyScopeConnections?.find(
		item => !item?.app?.options?.scope?.includes('read_product_listings')
	);

	const force =
		/tracking-pages\/(.*)?\/customize/.test(location.pathname) &&
		trackingPage?.trackingPageSettings.trackingPage?.themeName === 'sake';

	if (!upgradeItem) return null;

	const onUpgrade = () => {
		window.location.assign(
			process.env.UPDATE_SHOPIFY_SCOPE_URL +
				`?shop=${String(upgradeItem?.app?.key)}.myshopify.com`
		);
	};

	const onClose = () => {
		if (force) {
			history.replace({
				pathname: location.pathname.replace(
					/(tracking-pages\/(.*)?)\/customize/,
					'$1'
				),
			});
		}
		setVisible(false);
	};

	if (!force) return null;

	const caoncatLink = getGoogleFormUrl({email: user?.email});

	return (
		<Modal
			title=""
			titleHidden
			open
			primaryAction={{
				content: t('UPDATE_PER_e82d8'),
				onAction: onUpgrade,
			}}
			secondaryActions={[
				{
					content: t('NOT_NOW_1871a'),
					onAction: onClose,
				},
			]}
			onClose={onClose}
		>
			<ShopifyModal.Section>
				<Stack vertical spacing="extraTight">
					<Stack.Item />
					<Stack distribution="center">
						<DisplayText>
							<TextStyle variation="strong">
								{t('UPDATE_SHO_65b49')}
							</TextStyle>
						</DisplayText>
					</Stack>
					<Stack.Item />
					<Stack spacing="tight" distribution="center">
						<img
							style={{width: 18}}
							src="https://assets.aftership.com/apps/svg/shopify-automizely-conversions.svg"
							alt={t('SHOPIFY_36aae')}
						/>
						<TextStyle>
							{upgradeItem?.app?.options?.description}
						</TextStyle>
					</Stack>
					<Stack.Item />
					<Stack.Item />
					<Stack distribution="center">
						<img
							style={{width: 375}}
							alt=""
							src={modalIllustration}
						/>
					</Stack>

					<Stack vertical>
						<TextStyle>{t('UPDA_b9ec3')}</TextStyle>
						<TextStyle>
							{/* t('IF_YOU_HAV_b5d0f') */}
							<Trans
								i18nKey="IF_YOU_HAV_b5d0f"
								components={{
									lnk: (
										<I18nLink url={caoncatLink} external />
									),
								}}
							/>
						</TextStyle>
					</Stack>
				</Stack>
			</ShopifyModal.Section>
		</Modal>
	);
}
