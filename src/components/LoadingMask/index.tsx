import {Spinner} from '@shopify/polaris';
import React, {FC} from 'react';

interface IProps {
	loading?: boolean;
}
const LoadingMask: FC<IProps> = ({loading}) => {
	if (!loading) {
		return null;
	}
	return (
		<>
			<div
				style={{
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					position: 'absolute',
					flexDirection: 'column',
					backdropFilter: 'blur(1px)',
					textAlign: 'center',
					zIndex: 8,
					top: 0,
					left: 0,
					width: '100%',
					height: '100%',
					background: '#fff',
					opacity: 0.45,
				}}
			/>
			<div
				style={{
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					position: 'absolute',
					flexDirection: 'column',
					backdropFilter: 'blur(1px)',
					textAlign: 'center',
					zIndex: 9,
					top: 0,
					left: 0,
					width: '100%',
					height: '100%',
				}}
			>
				<Spinner size="large" />
			</div>
		</>
	);
};
export default LoadingMask;
