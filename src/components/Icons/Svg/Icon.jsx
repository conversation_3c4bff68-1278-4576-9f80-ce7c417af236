import React from 'react';
import PropTypes from 'prop-types';
import {Icon as PolarisIcon} from '@shopify/polaris';
import {ReactComponent as IconInstagram} from 'icons/Instagram.svg';
import {ReactComponent as IconStore} from 'icons/Store.svg';
import {ReactComponent as IconDelivered} from 'icons/Delivered.svg';
import {ReactComponent as IconException} from 'icons/Exception.svg';
import {ReactComponent as IconExpired} from 'icons/Expired.svg';
import {ReactComponent as IconAttemptFailed} from 'icons/AttemptFailed.svg';
import {ReactComponent as IconInfoReceived} from 'icons/InfoReceived.svg';
import {ReactComponent as IconInTransit} from 'icons/InTransit.svg';
import {ReactComponent as IconOutForDelivery} from 'icons/OutForDelivery.svg';
import {ReactComponent as IconPending} from 'icons/Pending.svg';
import {ReactComponent as IconMarkAsDelivered} from 'icons/MarkAsDelivered.svg';
import {ReactComponent as IconAvailableForPickup} from 'icons/AvailableForPickup.svg';
import './icon.css';

const IconsMap = {
	Store: IconStore,
	Delivered: IconDelivered,
	Exception: IconException,
	Expired: IconExpired,
	AttemptFailed: IconAttemptFailed,
	InfoReceived: IconInfoReceived,
	InTransit: IconInTransit,
	OutForDelivery: IconOutForDelivery,
	Pending: IconPending,
	MarkAsDelivered: IconMarkAsDelivered,
	Instagram: IconInstagram,
	AvailableForPickup: IconAvailableForPickup,
};

const SvgIcon = ({type, width, height, className}) => {
	const Icon = IconsMap[type];
	if (!Icon) return null;
	return (
		<div style={{width, height}} className={`CustomSvgIcon ${className}`}>
			<PolarisIcon
				source={() => <Icon width={width} height={height} />}
			/>
		</div>
	);
};

SvgIcon.propTypes = {
	type: PropTypes.string,
	className: PropTypes.string,
	width: PropTypes.number,
	height: PropTypes.number,
};

SvgIcon.defaultProps = {
	type: '',
	className: '',
	width: 13,
	height: 13,
};

export default SvgIcon;
