import {Pagination, TextField} from '@shopify/polaris';
import {isInteger} from 'lodash';
import React, {useEffect, useRef, useState, useMemo} from 'react';
import ReactDOM from 'react-dom';
import {useMount} from 'react-use';

import {numberWithCommas} from 'utils/number';
import './index.css';

let uniqueId = 0;
const getUniqueId = () => 'pagination' + uniqueId++;

export function useComponentId() {
	// https://github.com/facebook/react/issues/14490 distinct in component but not generate in every rendering
	const idRef = useRef('');
	if (!idRef.current) {
		idRef.current = getUniqueId();
	}
	return idRef.current;
}

const isIntNumber = (v: string) => {
	if (v === '' || isInteger(Number(v))) {
		return true;
	}
	return false;
};

const PaginationInput = ({
	total,
	current,
	onChange,
}: {
	total: number;
	current: number;
	onChange: (page: number) => any;
}) => {
	const [value, setValue] = useState(String(current));
	useEffect(() => {
		setValue(String(current));
	}, [current]);
	return (
		// eslint-disable-next-line jsx-a11y/no-static-element-interactions
		<div
			className="pagination-input"
			onKeyDown={event => {
				if (event.key === 'Enter') {
					if (isIntNumber(value) && Number(value) <= total) {
						onChange(Number(value) || 1);
					}
				}
			}}
		>
			<TextField
				labelHidden
				error={Number(value) > total}
				label=""
				onBlur={() => {
					if (isIntNumber(value) && Number(value) <= total) {
						onChange(Number(value) || 1);
					}
				}}
				onChange={v => {
					if (isIntNumber(v)) {
						setValue(v);
					}
				}}
				value={String(value)}
			/>
			<span className="total">/ {numberWithCommas(total)}</span>
		</div>
	);
};

export interface IPaginationProps
	extends React.ComponentProps<typeof Pagination> {
	total?: number;
	current?: number;
	onChange?: (page: number) => any;
}

export default ({
	total = 0,
	current = 0,
	onChange,
	...rest
}: IPaginationProps) => {
	const randomId = useComponentId();
	const inputDiv = useRef(document.createElement('div'));
	useMount(() => {
		const previousButton = document.querySelector(
			`#${randomId} .Polaris-ButtonGroup__Item`
		)!;
		if (previousButton && total && current) {
			previousButton.after(inputDiv.current);
		}
	});

	const element = ReactDOM.createPortal(
		<PaginationInput
			total={total}
			current={current}
			onChange={onChange || (() => {})}
		/>,
		inputDiv.current
	);

	return (
		<div id={randomId}>
			<Pagination {...rest} />
			{element}
		</div>
	);
};
