import {Popover, <PERSON><PERSON>, <PERSON>ack, DatePicker} from '@shopify/polaris';
import {CalendarMinor} from '@shopify/polaris-icons';
import {isEqual} from 'lodash';
import moment from 'moment-timezone';
import React, {useCallback, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {DateOptionList, RangeOptions} from './components/DateOptionList';
import DatePickerTextField from './components/DatePickerTextField';
import styles from './index.module.scss';
import {DEFAULT_DATE_RANGE_OPTIONS, formatDateRange} from './utils';

const DateRangeSelector = ({
	start,
	end,
	onDateChange,
	isSelected,
	isOnlyShowRange,
	rangeOptions = DEFAULT_DATE_RANGE_OPTIONS,
	disableDatesBefore,
	disableDatesAfter,
}: {
	start: Date;
	end: Date;
	onDateChange: ({start, end}: {start: Date; end: Date}) => void;
	isSelected?: boolean;
	isOnlyShowRange?: boolean;
	rangeOptions?: RangeOptions[];
	disableDatesBefore?: Date;
	disableDatesAfter?: Date;
}) => {
	const {t} = useTranslation();
	const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
	const [errorMessage, setErrorMessage] = useState('');
	const [{month, year}, setDate] = useState({
		month: start.getMonth(),
		year: start.getFullYear(),
	});
	const [selectedDate, setSelectedDate] = useState({start, end});

	const dateLabel = useMemo(() => {
		if (!isSelected) {
			return t('SELECT_RANGE_fhoqs2');
		}
		const dateRange = `${moment(start).format('MMM D, YYYY')} - ${moment(
			end
		).format('MMM D, YYYY')}`;

		if (isOnlyShowRange) {
			return dateRange;
		}
		const selectedRangeValue = rangeOptions.find(val =>
			isEqual(val.range, formatDateRange({start, end}))
		)?.labelI18nKey;

		return selectedRangeValue ? t(selectedRangeValue) : dateRange;
	}, [end, isSelected, isOnlyShowRange, rangeOptions, start, t]);

	const togglePopoverActive = useCallback(
		() => setIsDatePickerOpen(!isDatePickerOpen),
		[isDatePickerOpen]
	);

	const ref = useCallback(node => {
		if (node) {
			const container = node.closest(
				'.Polaris-PositionedOverlay'
			) as HTMLDivElement;
			container.style.zIndex = '521';
		}
	}, []);

	return (
		<Popover
			active={isDatePickerOpen}
			fullHeight
			onClose={togglePopoverActive}
			activator={
				<div className={styles.buttonContainer}>
					<Button
						fullWidth
						onClick={togglePopoverActive}
						icon={CalendarMinor}
					>
						{dateLabel}
					</Button>
				</div>
			}
			preferredAlignment="left"
			preferredPosition="below"
		>
			<Popover.Pane>
				<div
					ref={ref}
					role="button"
					tabIndex={0}
					onClick={e => e.stopPropagation()}
					className={styles.datePickerContainer}
				>
					<div className={styles.rangeOptions}>
						<DateOptionList
							selectedDate={selectedDate}
							setSelectedDate={setSelectedDate}
							setDate={setDate}
							rangeOptions={rangeOptions}
						/>
					</div>
					<div className={styles.datePicker}>
						<Stack vertical>
							<DatePickerTextField
								selectedDate={selectedDate}
								setSelectedDate={setSelectedDate}
								setDate={setDate}
								errorMessage={errorMessage}
								setErrorMessage={setErrorMessage}
								disableDatesBefore={disableDatesBefore}
								disableDatesAfter={disableDatesAfter}
							/>
							<DatePicker
								month={month}
								year={year}
								selected={selectedDate}
								onChange={value => {
									setSelectedDate(value);
								}}
								onMonthChange={(month, year) =>
									setDate({month, year})
								}
								disableDatesBefore={disableDatesBefore}
								disableDatesAfter={disableDatesAfter}
								allowRange
								multiMonth
							/>
						</Stack>
					</div>
				</div>
			</Popover.Pane>
			<Popover.Pane fixed>
				<div style={{padding: 20}}>
					<Stack distribution="trailing">
						<Button onClick={togglePopoverActive}>
							{t('CANCEL_4a39b')}
						</Button>
						<Button
							onClick={() => {
								togglePopoverActive();
								onDateChange(selectedDate);
							}}
							primary
							disabled={errorMessage !== ''}
						>
							{t('APPLY_5cf2b')}
						</Button>
					</Stack>
				</div>
			</Popover.Pane>
		</Popover>
	);
};

export default DateRangeSelector;
