import moment from 'moment-timezone';

import {
	generateDateRangeOption,
	generateFutureDateRangeOption,
} from 'utils/day';

export const formatDateRange = ({start, end}: {start: Date; end: Date}) => {
	return {
		start: moment(start).format('YYYY-MM-DD'),
		end: moment(end).format('YYYY-MM-DD'),
	};
};

export const DEFAULT_DATE_RANGE_OPTIONS = [
	{
		labelI18nKey: 'TODAY_71f3fa',
		value: 'Today',
		range: formatDateRange(generateDateRangeOption(0)),
	},
	{
		labelI18nKey: 'YESTERDAY_71f3fa',
		value: 'Yesterday',
		range: formatDateRange(
			generateDateRangeOption(1, {includeToday: false})
		),
	},
	{
		labelI18nKey: 'LAST_7_71f3fa',
		value: 'Last 7 days',
		range: formatDateRange(generateDateRangeOption(6)),
	},
	{
		labelI18nKey: 'LAST_30_71f3fa',
		value: 'Last 30 days',
		range: formatDateRange(generateDateRangeOption(29)),
	},
	{
		labelI18nKey: 'LAST_60_71f3fa',
		value: 'Last 60 days',
		range: formatDateRange(generateDateRangeOption(59)),
	},
	{
		labelI18nKey: 'LAST_90_71f3fa',
		value: 'Last 90 days',
		range: formatDateRange(generateDateRangeOption(89)),
	},
	{
		labelI18nKey: 'LAST_120_71f3fa',
		value: 'Last 120 days',
		range: formatDateRange(generateDateRangeOption(119)),
	},
];

export const ALL_DATE_RANGE_OPTIONS = [
	{
		labelI18nKey: 'NEXT_30_DAYS_43745',
		value: 'Next 30 days',
		range: formatDateRange(generateFutureDateRangeOption(30)),
	},
	{
		labelI18nKey: 'NEXT_7_DAYS_353b7',
		value: 'Next 7 days',
		range: formatDateRange(generateFutureDateRangeOption(7)),
	},
	{
		labelI18nKey: 'TOMORROW_9916d',
		value: 'Tomorrow',
		range: formatDateRange(generateFutureDateRangeOption(1)),
	},
	...DEFAULT_DATE_RANGE_OPTIONS,
];

export const COMMON_DATE_FILTER_OPTIONS = [
	{
		labelI18nKey: 'LAST_7_71f3fa',
		value: 'Last 7 days',
		range: formatDateRange(generateDateRangeOption(6)),
	},
	{
		labelI18nKey: 'LAST_30_71f3fa',
		value: 'Last 30 days',
		range: formatDateRange(generateDateRangeOption(29)),
	},
	{
		labelI18nKey: 'LAST_60_71f3fa',
		value: 'Last 60 days',
		range: formatDateRange(generateDateRangeOption(59)),
	},
	{
		labelI18nKey: 'LAST_90_71f3fa',
		value: 'Last 90 days',
		range: formatDateRange(generateDateRangeOption(89)),
	},
	{
		labelI18nKey: 'LAST_120_71f3fa',
		value: 'Last 120 days',
		range: formatDateRange(generateDateRangeOption(119)),
	},
];

export const ORDER_DATE_FILTER_OPTIONS = [
	...COMMON_DATE_FILTER_OPTIONS,
	{
		labelI18nKey: 'Last 180 days',
		value: 'Last 180 days',
		range: formatDateRange(generateDateRangeOption(179)),
	},
];

export const EDD_DATE_FILTER_OPTIONS = [
	{
		labelI18nKey: 'NEXT_30_DAYS_43745',
		value: 'Next 30 days',
		range: formatDateRange(generateFutureDateRangeOption(30)),
	},
	{
		labelI18nKey: 'NEXT_7_DAYS_353b7',
		value: 'Next 7 days',
		range: formatDateRange(generateFutureDateRangeOption(7)),
	},
	...COMMON_DATE_FILTER_OPTIONS,
];
