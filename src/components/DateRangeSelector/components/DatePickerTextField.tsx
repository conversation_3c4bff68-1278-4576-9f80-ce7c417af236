import {TextField, Stack, InlineError, Icon} from '@shopify/polaris';
import {ArrowRightMinor} from '@shopify/polaris-icons';
import moment from 'moment-timezone';
import React, {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {toOrgMoment} from 'utils/day';

import {formatDateRange} from '../utils';

type IProps = {
	selectedDate: {
		start: Date;
		end: Date;
	};
	setSelectedDate: React.Dispatch<
		React.SetStateAction<{
			start: Date;
			end: Date;
		}>
	>;
	setDate: React.Dispatch<
		React.SetStateAction<{
			month: number;
			year: number;
		}>
	>;
	errorMessage: string;
	setErrorMessage: (args: string) => void;
	disableDatesBefore?: Date;
	disableDatesAfter?: Date;
	otherChangeFunc?: () => void;
};

export default function DatePickerTextField({
	selectedDate,
	setSelectedDate,
	setDate,
	errorMessage,
	setErrorMessage,
	disableDatesBefore,
	disableDatesAfter,
	otherChangeFunc,
}: IProps) {
	const {t} = useTranslation();
	const {start, end} = formatDateRange(selectedDate);
	const [startDateText, setStartDateText] = useState(start);
	const [endDateText, setEndDateText] = useState(end);

	const isValid = (value: string) =>
		toOrgMoment(value, 'YYYY-MM-DD', true).isValid();

	const handleStartTextChange = useCallback(
		value => {
			setStartDateText(value);
			if (isValid(value)) {
				const startDate = toOrgMoment(value).toDate();
				setSelectedDate(state => ({
					...state,
					start: startDate,
				}));
				setDate({
					year: startDate.getFullYear(),
					month: startDate.getMonth(),
				});
				otherChangeFunc && otherChangeFunc();
			}
		},
		[setDate, setSelectedDate, otherChangeFunc]
	);
	const handleEndTextChange = useCallback(
		value => {
			setEndDateText(value);
			if (isValid(value)) {
				setSelectedDate(state => ({
					...state,
					end: toOrgMoment(value).toDate(),
				}));
				otherChangeFunc && otherChangeFunc();
			}
		},
		[setEndDateText, setSelectedDate, otherChangeFunc]
	);

	useEffect(() => {
		setErrorMessage('');
		if (moment(end).isBefore(moment(start))) {
			setErrorMessage(t('START_DAT_84360'));
		}
		if (
			disableDatesBefore &&
			moment(start).isBefore(moment(disableDatesBefore))
		) {
			setErrorMessage(
				t('START_DATE_98791823', {
					date: moment(disableDatesBefore).format(
						moment.HTML5_FMT.DATE
					),
				})
			);
		}
		if (
			disableDatesAfter &&
			moment(end).isAfter(moment(disableDatesAfter))
		) {
			setErrorMessage(
				t('END_DATE_981231', {
					date: moment(disableDatesAfter).format(
						moment.HTML5_FMT.DATE
					),
				})
			);
		}
		setStartDateText(start);
		setEndDateText(end);
	}, [disableDatesAfter, disableDatesBefore, end, setErrorMessage, start, t]);

	return (
		<Stack vertical spacing="extraTight">
			<div
				style={{
					display: 'flex',
					alignItems: 'center',
				}}
			>
				<div style={{width: '100%'}}>
					<TextField
						label=""
						onChange={handleStartTextChange}
						value={startDateText}
						placeholder="YYYY-MM-DD"
						error={Boolean(errorMessage)}
					/>
				</div>
				<div
					style={{
						width: '16px',
						margin: '0 12px',
					}}
				>
					<Icon source={ArrowRightMinor} color="subdued" />
				</div>
				<div style={{width: '100%'}}>
					<TextField
						label=""
						onChange={handleEndTextChange}
						value={endDateText}
						placeholder="YYYY-MM-DD"
						error={Boolean(errorMessage)}
					/>
				</div>
			</div>
			<InlineError fieldID="null" message={errorMessage} />
		</Stack>
	);
}
