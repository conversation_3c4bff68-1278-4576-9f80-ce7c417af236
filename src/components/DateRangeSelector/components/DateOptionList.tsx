import {OptionList} from '@shopify/polaris';
import {isEqual} from 'lodash';
import React, {useCallback} from 'react';
import {useTranslation} from 'react-i18next';

import {toOrgMoment} from 'utils/day';

import {formatDateRange} from '../utils';

export interface RangeOptions {
	labelI18nKey?: string;
	label?: string;
	value: string;
	range: {
		start: string;
		end: string;
	};
}

export const DateOptionList = ({
	selectedDate,
	setSelectedDate,
	setDate,
	rangeOptions,
	otherChangeFunc,
	setSelectedRangeOptions,
}: {
	selectedDate: {
		start: Date;
		end: Date;
	};
	setSelectedDate: React.Dispatch<
		React.SetStateAction<{
			start: Date;
			end: Date;
		}>
	>;
	setDate: React.Dispatch<
		React.SetStateAction<{
			month: number;
			year: number;
		}>
	>;
	rangeOptions: RangeOptions[];
	otherChangeFunc?: () => void;
	setSelectedRangeOptions?: (val: string) => void;
}) => {
	const {t} = useTranslation();
	const selectedRangeValue =
		rangeOptions.find(val =>
			isEqual(val.range, formatDateRange(selectedDate))
		)?.value || '';

	const onChange = useCallback(
		(value: string[]) => {
			const selectedRange = rangeOptions.find(
				val => val.value === value[0]
			)?.range;
			setSelectedRangeOptions && setSelectedRangeOptions(value[0]);
			const startDate = toOrgMoment(selectedRange?.start).toDate();
			setSelectedDate({
				start: startDate,
				end: toOrgMoment(selectedRange?.end).toDate(),
			});
			setDate({
				year: startDate.getFullYear(),
				month: startDate.getMonth(),
			});
			otherChangeFunc && otherChangeFunc();
		},
		[rangeOptions, setDate, setSelectedDate, otherChangeFunc]
	);

	return (
		<OptionList
			options={rangeOptions.map(option => ({
				...option,
				label: option.labelI18nKey
					? t(option.labelI18nKey)
					: option.label,
			}))}
			selected={[selectedRangeValue]}
			onChange={onChange}
		/>
	);
};
