import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {withTranslation} from 'react-i18next';
import {DATE_WITH_DASH} from 'constants/Date';
import {
	dayMomentjs as dayjs,
	getDateRangeOptions,
	generateDateRangeOption,
	dayMomentjs,
	to3rdPartyMoment,
} from 'utils/day';
import DateRangePicker from './DateRangePicker';

const makeDefaultValue = (
	start = dayjs().subtract(29, 'day').toDate(),
	end = dayjs().toDate(),
	rangeOptions = getDateRangeOptions()
) => {
	return {
		start,
		end,
		month: start.getMonth(),
		year: start.getFullYear(),
		select: getSelectFromRange(rangeOptions, {start, end}),
		starting: dayjs(start).format(DATE_WITH_DASH),
		ending: dayjs(end).format(DATE_WITH_DASH),
	};
};

const validateDate = (start, end) => {
	if (to3rdPartyMoment(end).isBefore(to3rdPartyMoment(start))) {
		return 'The end date must be greater than the start date.';
	}
	return '';
};

class DateRangePickerContainer extends PureComponent {
	static propTypes = {
		onChange: PropTypes.func.isRequired,
		// eslint-disable-next-line
		start: PropTypes.instanceOf(Date),
		// eslint-disable-next-line
		end: PropTypes.instanceOf(Date),
		// eslint-disable-next-line react/forbid-prop-types
		rangeOptions: PropTypes.array,
		disableDatesAfter: PropTypes.instanceOf(Date),
		minDate: PropTypes.instanceOf(Date),
		open: PropTypes.bool,
		isButtonHidden: PropTypes.bool,
		onClose: PropTypes.func,
		// eslint-disable-next-line react/no-unused-prop-types
		timezone: PropTypes.string,
		disabled: PropTypes.bool,
		t: PropTypes.func.isRequired,
		withoutPopover: PropTypes.bool,
		isMenuPicker: PropTypes.bool,
		onSelectChange: PropTypes.func,
		onStartingChange: PropTypes.func,
		onEndingChange: PropTypes.func,
	};

	static defaultProps = {
		start: dayMomentjs().subtract(30, 'days').toDate(),
		end: dayMomentjs().subtract(1, 'day'),
		rangeOptions: getDateRangeOptions(),
		open: false,
		minDate: null,
		disableDatesAfter: generateDateRangeOption(0).endDate,
		isButtonHidden: false,
		disabled: false,
		onClose: () => {},
		withoutPopover: false,
		isMenuPicker: false,
		onSelectChange: () => {},
		onStartingChange: () => {},
		onEndingChange: () => {},
		timezone: undefined,
	};

	constructor(props) {
		super(props);

		this.state = {
			isOpen: false,
			value: '',
			start: null,
			end: null,
			timezone: null,
			endDateError: '',
		};
	}

	/*
	 * user actively changed datepicker values
	 * affects:
	 * 1. select box on the top
	 * 2. input box starting & ending values
	 */
	onDateRangeChange = ({start, end}) => {
		this.props.onSelectChange();

		const {options} = this.state;
		const endDateError = validateDate(start, end);
		const select = getSelectFromRange(options, {
			start,
			end,
		});
		this.adjustOptionsForSelect(select);
		const starting = dayjs(start).format(DATE_WITH_DASH);
		const ending = dayjs(end).format(DATE_WITH_DASH);
		this.setState(prevState => ({
			value: {
				...prevState.value,
				start,
				end,
				select,
				starting,
				ending,
			},
			endDateError,
		}));
	};

	/*
	 *
	 * user input input boxes, only invoke changes when it's a valid date value
	 * affects:
	 * 1. select box on the top
	 * 2. year and month
	 * 3. datepicker range
	 */
	onRangeInputChange = ({starting, ending}) => {
		const endDateError = validateDate(starting, ending);
		if (!dayjs(starting).isValid() || !dayjs(ending).isValid()) {
			this.setState(prevState => ({
				value: {
					...prevState.value,
					starting,
					ending,
				},
				endDateError,
			}));
			return;
		}

		const start = dayjs(starting).toDate();
		const end = dayjs(ending).toDate();
		const {options} = this.state;
		const select = getSelectFromRange(options, {
			start,
			end,
		});
		this.adjustOptionsForSelect(select);
		const {month, year} = {
			month: start.getMonth(),
			year: start.getFullYear(),
		};

		this.setState({
			value: {
				starting,
				ending,
				select,
				month,
				year,
				start,
				end,
			},
			endDateError,
		});
	};

	onStartingChange = starting => {
		this.props.onStartingChange();
		this.onRangeInputChange({
			starting,
			ending: this.state.value.ending,
		});
	};

	onEndingChange = ending => {
		this.props.onEndingChange();
		this.onRangeInputChange({
			starting: this.state.value.starting,
			ending,
		});
	};

	/*
	 * user actively changed the select form field in the popover
	 * affects:
	 * 1. month & year
	 * 2. datepicker range values
	 * 3. input box starting & ending values
	 */
	onSelectChange = select => {
		this.props.onSelectChange();
		let {start, end, starting, ending, month, year} = this.state.value;
		if (isRangedSelect(this.props.rangeOptions, select)) {
			({start, end} = getRangeFromSelect(
				this.props.rangeOptions,
				select
			));
		}
		({month, year} = {
			month: start.getMonth(),
			year: start.getFullYear(),
		});
		starting = dayjs(start).format(DATE_WITH_DASH);
		ending = dayjs(end).format(DATE_WITH_DASH);
		this.adjustOptionsForSelect(select);
		this.setState({
			value: {
				select,
				start,
				end,
				starting,
				ending,
				month,
				year,
			},
		});
	};

	onMonthYearChange = ({month, year}) => {
		this.setState(prevState => ({
			value: {
				...prevState.value,
				month,
				year,
			},
		}));
	};

	onToggle = isOpen => {
		if (isOpen) {
			this.setState({
				isOpen: true,
			});
			return;
		}

		const {start, end} = this.state;

		this.setState({
			isOpen: false,
			value: makeDefaultValue(start, end, this.props.rangeOptions),
		});

		this.props.onClose();
	};

	onCommit = ({start, end}) => {
		this.setState(
			prevState => ({
				start,
				end,
				activatorText: getActivatorText(prevState.options, {
					start,
					end,
				}),
				isOpen: false,
			}),
			() => {
				this.props.onChange({
					start,
					end,
				});
			}
		);
	};

	syncPropsToState(nextProps) {
		const prevState = this.state;
		if (
			!prevState.start ||
			!prevState.end ||
			nextProps.start.valueOf() !== prevState.start.valueOf() ||
			nextProps.end.valueOf() !== prevState.end.valueOf() ||
			nextProps.timezone !== prevState.timezone
		) {
			const options = nextProps.rangeOptions;
			const activatorText = getActivatorText(options, {
				start: nextProps.start,
				end: nextProps.end,
			});
			const select = getSelectFromRange(options, {
				start: nextProps.start,
				end: nextProps.end,
			});
			const state = {
				...prevState,
				activatorText,
				start: nextProps.start,
				end: nextProps.end,
				timezone: nextProps.timezone,
				value: {
					start: nextProps.start,
					starting: dayjs(nextProps.start).format(DATE_WITH_DASH),
					end: nextProps.end,
					ending: dayjs(nextProps.end).format(DATE_WITH_DASH),
					select,
					year: nextProps.start.getFullYear(),
					month: nextProps.start.getMonth(),
				},
				options:
					select === 'custom'
						? [
								{
									label: this.props.t('CUSTOM_071f1'),
									value: 'custom',
									range: null,
								},
								...options.filter(
									option => option.value !== 'custom'
								),
						  ]
						: options,
			};
			this.setState(state);
		}
	}

	UNSAFE_componentWillReceiveProps(nextProps) {
		this.syncPropsToState(nextProps);
	}

	UNSAFE_componentWillMount() {
		this.syncPropsToState(this.props);
	}

	adjustOptionsForSelect(select) {
		// no need to do anything
		if (
			select === 'custom' &&
			this.props.rangeOptions.find(opt => opt.value === 'custom')
		) {
			return;
		}

		const {options} = this.state;

		if (
			select === 'custom' &&
			!this.props.rangeOptions.find(opt => opt.value === 'custom')
		) {
			this.setState({
				options: [
					{
						label: this.props.t('CUSTOM_39a61'),
						value: 'custom',
						range: null,
					},
				].concat(options),
			});
			return;
		}

		// remove dynamic added `custom` field in the dropdown
		this.setState(prevState => ({
			options: prevState.options.filter(option => option.range),
		}));
	}

	render() {
		const isOpen = this.props.open || this.state.isOpen;
		return (
			<DateRangePicker
				value={this.state.value}
				endDateError={this.state.endDateError}
				isOpen={isOpen}
				minDate={this.props.minDate}
				disableDatesAfter={this.props.disableDatesAfter}
				disabled={this.props.disabled}
				isButtonHidden={this.props.isButtonHidden}
				activatorText={this.state.activatorText}
				onDateRangeChange={this.onDateRangeChange}
				onMonthYearChange={this.onMonthYearChange}
				onStartingChange={this.onStartingChange}
				onEndingChange={this.onEndingChange}
				onSelectChange={this.onSelectChange}
				onCommit={this.onCommit}
				onToggle={this.onToggle}
				options={this.props.rangeOptions.map(({label, value}) => ({
					label,
					value,
				}))}
				withoutPopover={this.props.withoutPopover}
				isMenuPicker={this.props.isMenuPicker}
			/>
		);
	}
}

export default withTranslation()(DateRangePickerContainer);

function isRangedSelect(options, select) {
	return Boolean(getRangeFromSelect(options, select));
}

function getRangeFromSelect(options, select) {
	const option = options.find(opt => opt.value === select);
	if (option) {
		return option.range;
	}

	return null;
}

function getSelectFromRange(options, range) {
	const startDate = dayjs(range.start);
	const endDate = dayjs(range.end);
	const option = options.find(opt => {
		if (!opt.range) return null;
		return (
			dayjs(opt.range.end).isSame(endDate, 'day') &&
			dayjs(opt.range.start).isSame(startDate, 'day')
		);
	});
	// should be today and matches the delta so trigger the select
	if (option) {
		return option.value;
	}
	return 'custom';
}

export function getActivatorText(options, {start, end}) {
	const value = getSelectFromRange(options, {start, end});
	const option = options.find(opt => opt.value === value);
	if (option && option.range) {
		return option.label;
	}

	return `${dayjs(start).format('MMM D, YYYY')} - ${dayjs(end).format(
		'MMM D, YYYY'
	)}`;
}
