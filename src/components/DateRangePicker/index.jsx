import React from 'react';
import {connect} from 'react-redux';
import {parse, stringify} from 'query-string';
import moment from 'moment-timezone';
import {updateRangeRequest} from 'actions/dashboard';
import {getDateRange} from 'selectors/dashboard';
import {to3rdPartyMoment} from 'utils/day';
import {DATE} from 'constants/Date';

import {useUserTimeZone} from 'hooks';
import {AS_ANALYTICS_GLOBAL_FILTER} from 'constants/Analytics';
import Container from './Container';
import './DateRangePicker.scss';
/**
 * onChange
 * @param {Date} start
 * @param {Date} end
 */
const onChange =
	(dispatch, ownProps) =>
	({start, end}) => {
		sessionStorage.setItem(
			AS_ANALYTICS_GLOBAL_FILTER,
			stringify({
				...parse(sessionStorage.getItem(AS_ANALYTICS_GLOBAL_FILTER)),
				'start-date': moment(start).format(DATE),
				'end-date': moment(end).format(DATE),
			})
		);
		if (!ownProps.noSyncToSearch && !ownProps.notSyncUrl) {
			dispatch(updateRangeRequest(start, end));
		}
		ownProps.onChange && ownProps.onChange({start, end});
	};

const ContainerWrapper = props => {
	const timezone = useUserTimeZone();
	return <Container {...props} timezone={timezone} />;
};
export default connect(
	(state, ownProps) => {
		const date = getDateRange(state);
		let {start} = date;
		let {end} = date;
		const minDate = to3rdPartyMoment(ownProps.minDate);
		if (ownProps?.minDate) {
			if (start && ownProps.minDate && start < minDate.toDate()) {
				start = minDate.add(1, 'day').toDate();
			}
			if (start && end && start > end) {
				end = start;
			}
		}
		const disableDatesAfter = to3rdPartyMoment().toDate();
		return {
			start,
			end,
			disableDatesAfter,
			...ownProps,
		};
	},
	(dispatch, ownProps) => ({
		onChange: onChange(dispatch, ownProps),
	})
)(ContainerWrapper);
