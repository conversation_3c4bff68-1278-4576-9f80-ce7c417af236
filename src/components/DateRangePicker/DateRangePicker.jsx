import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {ArrowRightMinor, CalendarMinor} from '@shopify/polaris-icons';

import {
	Popover,
	Button,
	FormLayout,
	TextField,
	Stack,
	DatePicker,
	Select,
	Icon,
} from '@shopify/polaris';
import {withTranslation} from 'react-i18next';
import {to3rdPartyMoment} from 'utils/day';

class Date<PERSON>ange<PERSON><PERSON> extends PureComponent {
	static propTypes = {
		value: PropTypes.shape({
			start: PropTypes.instanceOf(Date).isRequired,
			end: PropTypes.instanceOf(Date).isRequired,
			month: PropTypes.number.isRequired,
			year: PropTypes.number.isRequired,
			select: PropTypes.string.isRequired,
			starting: PropTypes.string.isRequired,
			ending: PropTypes.string.isRequired,
		}).isRequired,
		options: PropTypes.arrayOf(
			PropTypes.shape({
				label: PropTypes.string.isRequired,
				value: PropTypes.string.isRequired,
			})
		).isRequired,
		isOpen: PropTypes.bool.isRequired,
		minDate: PropTypes.instanceOf(Date),
		disableDatesAfter: PropTypes.instanceOf(Date),
		activatorText: PropTypes.string.isRequired,
		onSelectChange: PropTypes.func.isRequired,
		onStartingChange: PropTypes.func.isRequired,
		onEndingChange: PropTypes.func.isRequired,
		onDateRangeChange: PropTypes.func.isRequired,
		onMonthYearChange: PropTypes.func.isRequired,
		onCommit: PropTypes.func.isRequired,
		onToggle: PropTypes.func.isRequired,
		isButtonHidden: PropTypes.bool,
		disabled: PropTypes.bool,
		endDateError: PropTypes.string,
		t: PropTypes.func.isRequired,
		withoutPopover: PropTypes.bool.isRequired,
		isMenuPicker: PropTypes.bool.isRequired,
	};

	static defaultProps = {
		isButtonHidden: false,
		disabled: false,
		minDate: null,
		disableDatesAfter: null,
		endDateError: '',
	};

	onCommit = () => {
		let {start} = this.props.value;
		if (this.props.minDate) {
			const startDate = to3rdPartyMoment(this.props.value.start);
			const minDate = to3rdPartyMoment(this.props.minDate);
			if (startDate.diff(minDate) < 0 && !this.props.isMenuPicker) {
				// should not early than minDate
				start = minDate.add(1, 'day').format('YYYY-MM-DD');
			}
		}
		this.props.onCommit({
			start: start,
			end: this.props.value.end,
		});
	};

	onClose = () => {
		this.props.onToggle(false);
	};

	onOpen = () => {
		this.props.onToggle(true);
	};

	onSelectChange = select => {
		this.props.onSelectChange(select);
	};

	onStartingChange = starting => {
		this.props.onStartingChange(starting);
	};

	onEndingChange = ending => {
		this.props.onEndingChange(ending);
	};

	onDateRangeChange = ({start, end}) => {
		this.props.onDateRangeChange({
			start,
			end,
		});
	};

	onMonthYearChange = (month, year) => {
		this.props.onMonthYearChange({
			month,
			year,
		});
	};

	render() {
		const divStyle = {
			visibility: this.props.isButtonHidden ? 'hidden' : 'visible',
		};

		const pickerContext = (
			<>
				{/* disclaimer: have to hardcode DatePicker wrapper's max-width after Polaris v5+ */}
				<div
					style={{maxWidth: 600}}
					className="date-range-picker-wrapper"
				>
					<Popover.Pane sectioned>
						<Stack vertical>
							<FormLayout>
								{!this.props.isMenuPicker && (
									<Select
										label={this.props.t('DATE_RANGE_bf6dc')}
										options={
											this.props.value.select === 'custom'
												? this.props.options
												: this.props.options.filter(
														option =>
															option.value !==
															'custom'
												  )
										}
										onChange={this.onSelectChange}
										value={this.props.value.select}
									/>
								)}
								<FormLayout.Group condensed>
									<div
										className={
											this.props.isMenuPicker
												? 'date-range-input-group'
												: ''
										}
										style={{
											display: 'flex',
											alignItems: 'center',
											marginBottom: this.props
												.isMenuPicker
												? '12px'
												: 'unset',
										}}
									>
										<div style={{width: '100%'}}>
											<TextField
												label={
													!this.props.isMenuPicker
														? this.props.t(
																'STARTING_affdc'
														  )
														: undefined
												}
												error={
													(this.props.isMenuPicker &&
														this.props
															.endDateError) ||
													false
												}
												onChange={this.onStartingChange}
												value={
													this.props.value.starting
												}
												placeholder="YYYY-MM-DD"
											/>
										</div>
										{this.props.isMenuPicker ? (
											<div
												style={{
													width: '16px',
													margin: '0 12px',
												}}
											>
												<Icon
													source={ArrowRightMinor}
													color="subdued"
												/>
											</div>
										) : (
											<div style={{margin: '0 8px'}} />
										)}
										<div style={{width: '100%'}}>
											<TextField
												label={
													!this.props.isMenuPicker
														? this.props.t(
																'ENDING_cb4f9'
														  )
														: undefined
												}
												onChange={this.onEndingChange}
												error={
													(!this.props.isMenuPicker &&
														this.props
															.endDateError) ||
													// this.props.endDateError ||
													false
												}
												value={this.props.value.ending}
												placeholder="YYYY-MM-DD"
											/>
										</div>
									</div>
								</FormLayout.Group>
							</FormLayout>
							<DatePicker
								month={this.props.value.month}
								year={this.props.value.year}
								selected={{
									start: this.props.value.start,
									end: this.props.value.end,
								}}
								onChange={this.onDateRangeChange}
								onMonthChange={this.onMonthYearChange}
								disableDatesBefore={this.props.minDate}
								disableDatesAfter={this.props.disableDatesAfter}
								allowRange
								multiMonth
							/>
						</Stack>
					</Popover.Pane>
					<Popover.Pane fixed sectioned>
						<Stack distribution="trailing">
							<Stack.Item fill={!this.props.isMenuPicker}>
								<Button onClick={this.onClose}>
									{this.props.t('CANCEL_4db5e')}
								</Button>
							</Stack.Item>
							<Stack.Item>
								<Button
									onClick={this.onCommit}
									primary
									disabled={this.props.endDateError}
								>
									{this.props.t('APPLY_ff7ba')}
								</Button>
							</Stack.Item>
						</Stack>
					</Popover.Pane>
				</div>
			</>
		);
		return this.props.withoutPopover ? (
			pickerContext
		) : (
			<Popover
				active={this.props.isOpen}
				fullHeight
				onClose={this.onClose}
				activator={
					<div style={divStyle}>
						<Button
							fullWidth
							disabled={this.props.disabled}
							onClick={
								this.props.isOpen ? this.onClose : this.onOpen
							}
							icon={CalendarMinor}
						>
							{this.props.activatorText}
						</Button>
					</div>
				}
			>
				{pickerContext}
			</Popover>
		);
	}
}

export default withTranslation()(DateRangePcker);
