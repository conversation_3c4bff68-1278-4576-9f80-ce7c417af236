// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Text Field Render should render correctly 1`] = `
<DateRangePcker
  activatorText="Dec 12, 2018 - Dec 13, 2018"
  isButtonHidden={false}
  isOpen={true}
  minDate={null}
  onCommit={[Function]}
  onDateRangeChange={[Function]}
  onEndingChange={[Function]}
  onMonthYearChange={[Function]}
  onSelectChange={[Function]}
  onStartingChange={[Function]}
  onToggle={[Function]}
  options={
    Array [
      Object {
        "label": "Custom",
        "value": "custom",
      },
      Object {
        "label": "Yesterday",
        "value": "yesterday",
      },
      Object {
        "label": "Last 7 days",
        "value": "last7days",
      },
      Object {
        "label": "Last 30 days",
        "value": "last30days",
      },
    ]
  }
  value={
    Object {
      "end": 2018-12-12T23:59:59.999Z,
      "ending": "2018-12-13",
      "month": 11,
      "select": "custom",
      "start": 2018-12-12T00:00:00.000Z,
      "starting": "2018-12-12",
      "year": 2018,
    }
  }
/>
`;
