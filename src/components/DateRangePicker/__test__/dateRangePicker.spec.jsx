import React from 'react';
import {shallow} from 'enzyme';
import {dayjsOnlyDate, getDateRangeOptions} from 'utils/day';
import DateRangePickerContainer from '../Container';

describe('Text Field Render', () => {
	xit('should render correctly', () => {
		const start = dayjsOnlyDate('2018-12-12T00:00:00Z').startOf('day');
		const end = dayjsOnlyDate('2018-12-12T23:59:59Z').endOf('day');

		const defaultProps = {
			onChange: () => {},
			start: start
				.add(-start.toDate().getTimezoneOffset() / 60, 'hour')
				.toDate(),
			end: end
				.add(-end.toDate().getTimezoneOffset() / 60, 'hour')
				.toDate(),

			open: true,
			isButtonHidden: false,
			onClose: () => {},
			timezone: '+00:00',
		};

		const wrapper = shallow(<DateRangePickerContainer {...defaultProps} />);
		expect(wrapper).toMatchSnapshot();
	});

	it('should return correct filter options', () => {
		const options = getDateRangeOptions();
		expect(options.length).toBe(10);
		expect(options[0].label).toBe('Today');
		expect(options[1].label).toBe('Yesterday');
		expect(options[2].label).toBe('Last 7 days');
		expect(options[3].label).toBe('Last 30 days');
		expect(options[4].label).toBe('Last 60 days');
		expect(options[5].label).toBe('Last 90 days');
		expect(options[6].label).toBe('Last 120 days');

		expect(options[0].value).toBe('today');
		expect(options[1].value).toBe('yesterday');
		expect(options[2].value).toBe('last 7 days');
		expect(options[3].value).toBe('last 30 days');
		expect(options[4].value).toBe('last 60 days');
		expect(options[5].value).toBe('last 90 days');
		expect(options[6].value).toBe('last 120 days');
	});
});
