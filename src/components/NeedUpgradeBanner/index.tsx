import {Banner, BannerProps} from '@shopify/polaris';
import React from 'react';
import {useDispatch} from 'react-redux';

import {openModal} from 'actions/modals';
import {SUBSCRIPTION_MODAL} from 'constants/Modals';
import {useIsAvailableFeature} from 'hooks/billings';
import {useGetNearestPlanByFeatureCode} from 'hooks/billings/useChoosePlan';
import ga from 'utils/ga';

interface Props {
	featureCode: string;
	content: string;
	hasMarginBottom?: boolean;
	status?: BannerProps['status'];
}

export default function NeedUpgradeBanner({
	featureCode,
	content,
	hasMarginBottom,
	status = 'warning',
}: Props) {
	const dispatch = useDispatch();
	const [hasCode] = useIsAvailableFeature(featureCode);
	const plan = useGetNearestPlanByFeatureCode(featureCode);
	if (hasCode) {
		return null;
	}
	return (
		<>
			<Banner
				status={status}
				action={{
					content: 'Upgrade now',
					onAction: () => {
						dispatch(
							openModal(SUBSCRIPTION_MODAL, undefined, {
								planCode: plan ? plan.code : undefined,
								featureCodes: [featureCode],
							})
						);
						dispatch(
							ga.planSubscribeIntention({
								title: localStorage.getItem('title') || '',
								entry: 'banner',
							})
						);
					},
				}}
			>
				{content}
			</Banner>
			<div
				style={{
					marginBottom: hasMarginBottom ? '2rem' : '0',
				}}
			/>
		</>
	);
}
