import React from 'react';

import FeatureLockCard from 'components/FeatureLockCard';
import isCompany from 'utils/isCompany';

import FeatureLockController from './FeatureLockController';

import './index.css';

interface IProps {
	style?: React.CSSProperties;
	children?: React.ReactNode;
	featureCode: string;
}

export const FeatureLockMask = (props: IProps) => {
	return isCompany() ? (
		<>{props.children}</>
	) : (
		<FeatureLockController featureCode={props.featureCode}>
			{({needUpgrade, targetPlan}) => {
				if (!needUpgrade) return <>{props.children}</>;
				return (
					<div className="page-wrapper" style={props.style}>
						{props.children}
						<div className="lock-layout">
							<div
								style={{
									boxShadow:
										'0px 0px 4px rgba(0, 0, 0, 0.1), 0px 8px 40px rgba(0, 0, 0, 0.2)',
								}}
							>
								<FeatureLockCard
									plan={targetPlan}
									featureCode={props.featureCode}
								/>
							</div>
						</div>
					</div>
				);
			}}
		</FeatureLockController>
	);
};
