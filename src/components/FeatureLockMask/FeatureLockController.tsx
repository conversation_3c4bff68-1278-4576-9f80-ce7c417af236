import {
	IPlan,
	useBillingGlobalState,
	useGetPlanTrial,
} from 'aftershipBillingUi';
import React from 'react';

import SpinLoading from 'components/SpinLoading';
import {ANALYTICS_ON_TIME_SHIPMENT} from 'constants/billings/features';
import {useFeatureValidator, useTargetUpgradePlan} from 'hooks/billings';
import useExtraPermissions from 'hooks/billings/useExtraPermissions';
import {usePlanTrialAvailable} from 'hooks/billings/usePlanTrialAvailable';

interface Props {
	featureCode: string;
	children: (options: {
		onUpgrade: () => void;
		needUpgrade: boolean;
		targetPlan?: IPlan;
	}) => React.ReactNode;
}

export default function NeedUpgradeComponent({featureCode, children}: Props) {
	const {planTrials} = useGetPlanTrial();
	const canUsePlanTrial = usePlanTrialAvailable();
	const extraPermissionData = useExtraPermissions();
	const canCreatePlanTrial =
		(planTrials || []).length <= 0 && canUsePlanTrial;

	const targetPlan = useTargetUpgradePlan(
		featureCode,
		canCreatePlanTrial ? 50000 : undefined
	);
	const [action, hasFeature] = useFeatureValidator(featureCode);

	// TODO: Will remove the extra permission after trial feature online
	const hasOnTimeReportPermission =
		hasFeature ||
		extraPermissionData.data?.currentOrganization?.[
			ANALYTICS_ON_TIME_SHIPMENT
		];
	const {initialized} = useBillingGlobalState();

	return initialized ? (
		<>
			{children({
				onUpgrade: action,
				needUpgrade:
					featureCode === ANALYTICS_ON_TIME_SHIPMENT
						? !hasOnTimeReportPermission
						: !hasFeature,
				targetPlan,
			})}
		</>
	) : (
		<SpinLoading />
	);
}
