import React from 'react';
import {useTranslation} from 'react-i18next';

import emailBrand from 'img/emptyState.png';
import {openLiveChat} from 'utils/contactUs';

import style from './index.module.scss';

export default function ErrorState() {
	const {t} = useTranslation();

	return (
		<div className={style.emptyState}>
			<img src={emailBrand} alt={t('EMPTY_7fd76')} />
			<p
				className={style.head}
				style={{fontSize: 16, fontWeight: 500, lineHeight: '24px'}}
			>
				{t('SOMETHING_aeca9')}
			</p>
			<p
				className={style.subhead}
				style={{
					fontSize: 14,
					fontWeight: 400,
					lineHeight: '20px',
					color: '#6D7175',
				}}
			>
				{t('REFRESH_TH_7c72a')}
			</p>
			<div className={style.buttonGroup}>
				<div
					role="button"
					tabIndex={0}
					className={style.refreshBtn}
					onClick={() => {
						location.reload();
					}}
				>
					{t('REFRESH_PA_5efa5')}
				</div>
				<div
					role="button"
					tabIndex={0}
					className={style.contactBtn}
					onClick={() => openLiveChat()}
				>
					{t('CONTACT_SU_345d2')}
				</div>
			</div>
		</div>
	);
}
