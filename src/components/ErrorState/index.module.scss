.emptyState {
	width: 320;
	display: flex;
	flex-direction: column;
	align-items: center;
	font-family: 'SF Pro Text', sans-serif;
	img {
		width: 269px;
		height: 269px;
		margin-top: 160px;
	}
	.head {
		margin: 36px 0 5px;
		font-size: 16;
		font-weight: 500;
		line-height: 24px;
	}
	.subhead {
		font-size: 14;
		font-weight: 400;
		line-height: 20px;
		color: #6d7175;
	}
	.buttonGroup {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		margin-top: 20px;
		font-size: 14px;
		font-weight: 500;
		.refreshBtn {
			padding: 8px 16px;
			height: 36px;
			background: #5a67cb;
			border: none;
			color: #fff;
			border-radius: 4px;
			cursor: pointer;
			&:hover {
				opacity: 0.8;
			}
		}
		.contactBtn {
			background-color: #f6f6f7;
			color: #202223;
			padding: 8px 16px;
			height: 36px;
			border: 1px solid #8c9196;
			box-sizing: border-box;
			border-radius: 4px;
			margin-left: 12px;
			cursor: pointer;
			&:hover {
				opacity: 0.5;
			}
		}
	}
}
