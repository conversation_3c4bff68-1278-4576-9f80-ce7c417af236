.hbspt_container {
	width: 100%;
}
.hbspt {
	:global {
		&,
		form,
		form fieldset {
			width: 100%;
		}

		// fields
		form fieldset {
			display: grid;
			column-gap: 16px;
			padding: 0;
			margin: 0;
			border: none;
		}
		.form-columns-2 {
			grid-template-columns: 1fr 1fr;
			align-items: baseline;
		}

		ul {
			list-style: none;
			padding: 0;
			margin: 0;
		}

		.hs-form-field {
			display: flex;
			flex-direction: column;
			margin-bottom: 16px;

			label {
				display: inline-block;
				margin-bottom: 8px;
			}
			.input {
				flex: 1 1 auto;
				width: 100%;
				font-weight: 400;
				line-height: 2.4rem;
				border: none;
				text-transform: none;
				letter-spacing: normal;
				position: relative;
				display: flex;
				align-items: center;
				color: var(--p-text);
				cursor: text;

				select,
				textarea,
				input[type='text'],
				input[type='email'],
				input[type='tel'] {
					width: 100%;
					margin: 0;
					padding: 0.5rem 1.2rem;
					border-radius: 4px;
					border: 1px solid var(--p-border-subdued);
					min-height: 3.6rem;

					background: none;
					color: var(--p-text);
					caret-color: var(--p-text);
					&:focus {
						box-shadow: 0 0 0 2px var(--p-focused);
						outline: none;
					}

					&.error {
						background-color: var(
							--p-surface-critical-subdued
						) !important;
						border: 1px solid var(--p-border-critical) !important;
					}
				}
				select {
					-webkit-appearance: none;
					appearance: none;
					background: none;
					place-content: '';
					background-image: linear-gradient(
							45deg,
							transparent 50%,
							gray 50%
						),
						linear-gradient(135deg, gray 50%, transparent 50%);

					background-position: calc(100% - 20px) calc(1em + 2px),
						calc(100% - 15px) calc(1em + 2px),
						calc(100% - 2.5em) 0.5em;
					background-size: 5px 5px, 5px 5px, 1px 1.5em;
					background-repeat: no-repeat;
				}

				textarea {
					min-height: 140px;
					margin-bottom: 16px;
				}
			}

			.hs-form-booleancheckbox-display {
				display: flex;
				input[type='checkbox'] {
					margin-top: 6px;
					border-radius: 50%;
					cursor: pointer;
				}

				span > p {
					margin-left: 8px;
					font-size: 14px;
					color: var(--p-text);
				}
			}
		}

		.legal-consent-container {
			.hs-richtext {
				color: var(--p-text);
				a {
					text-decoration: none;
					color: var(--p-interactive) !important;
					&:hover {
						text-decoration: underline;
					}
				}
				p {
					display: none;
				}
			}
		}

		// error tips
		ul.hs-error-msgs {
			margin: 0.4rem 0 0 0;
			padding: 0;
			li {
				margin: 0;
				padding: 0;
				font-size: 14px;
				color: var(--p-text-critical);
				fill: var(--p-icon-critical);

				&::before {
					content: '!\20DD';
					margin-right: 6px;
				}
			}
		}

		.hs-form-required {
			padding-left: 4px;
			color: var(--p-icon-critical);
		}

		.hs_error_rollup {
			display: none;
		}
		.hs-richtext {
			color: #374759;
			a {
				color: #202223;
				&:focus {
					color: #5a67cb;
				}
			}
		}
		.hs-dependent-field {
			.hs-error-msgs {
				position: static;
			}
		}
		.hs_recaptcha {
			display: none;
		}
		.submit-btn {
			margin-top: -12px;
			display: block;
			margin-left: 86%;
			cursor: pointer;
			width: 80px;
			height: 36px;
			background: #5a67cb;
			border: none;
			border-radius: 4px;
			color: white;
			font-weight: 500;
			font-size: 14px;
			line-height: 20px;
			text-align: center;
		}
	}
}

@media only screen and (max-width: 576px) {
	.hbspt {
		:global {
			.form-columns-2 {
				grid-template-columns: 1fr;
			}
		}
	}
}
