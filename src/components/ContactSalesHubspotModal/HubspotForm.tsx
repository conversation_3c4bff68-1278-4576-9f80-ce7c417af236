// @ts-nocheck
import {useAuth} from '@aftership/automizely-product-auth';
import {SkeletonBodyText} from '@shopify/polaris';
import React, {FC, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';

import {addToast} from 'actions/toast';
import {HUBSPOT_CONTACT_SUPPORT_MODAL} from 'constants/Modals';
import {useModal} from 'hooks';
import {loadResource} from 'utils/loadResource';

import styles from './HubspotForm.module.scss';

interface IUserInfo {
	firstname: string | undefined;
	lastname: string | undefined;
	email: string | undefined;
	company: string | undefined;
}
interface IHubspotConfig {
	region: string;
	portalId: string;
	formId: string;
}

interface IProps {
	hubspotFormConfig: IHubspotConfig;
	userInfo: IUserInfo;
}
const HubspotForm: FC<IProps> = ({hubspotFormConfig, userInfo}) => {
	const {t} = useTranslation();
	const {region, formId, portalId} = hubspotFormConfig;

	const dispatch = useDispatch();
	const [isLoading, setIsLoading] = useState(true);
	const ref = useRef<HTMLDivElement>(null);
	const {close} = useModal(HUBSPOT_CONTACT_SUPPORT_MODAL);
	const [{user}] = useAuth();
	const userEmail = user?.email;

	useEffect(() => {
		loadResource('js', 'https://js.hsforms.net/forms/v2.js').then(() => {
			setTimeout(() => {
				setIsLoading(false);
			}, 600);
			window?.hbspt?.forms.create({
				region,
				portalId,
				formId,
				target: '#hsforms',
				cssRequired: 'no style',
				submitButtonClass: 'submit-btn',
				groupErrors: true,
			});
		});
	}, []);

	const eventHandler = (event: MessageEvent) => {
		if (event.data.type === 'hsFormCallback' && event.data.id === formId) {
			if (event.data.eventName === 'onFormReady') {
				Object.entries(userInfo).forEach(([key, val]) => {
					const element = document.querySelector(`#${key}-${formId}`);
					if (element) {
						(element as any).value = val;
						element.dispatchEvent(
							new Event('input', {bubbles: true})
						);
					}
				});
			}
			if (event.data.eventName === 'onFormSubmitted') {
				close();
				dispatch(addToast({message: t('SUBMITTED_d331f')}));
			}
		}
	};
	useEffect(() => {
		if (userEmail) {
			window.addEventListener('message', eventHandler);
			return () => {
				window.removeEventListener('message', eventHandler);
			};
		}
		return () => {};
	}, [userEmail]);

	return (
		<>
			<div id={formId} className={styles.hbspt_container} ref={ref}>
				{!isLoading && (
					<div style={{marginTop: '8px', marginBottom: '20px'}}>
						{t('TALK_TO_OU_0f72a')}
					</div>
				)}
				<div id="hsforms" className={styles.hbspt}>
					<SkeletonBodyText lines={5} />
				</div>
			</div>
		</>
	);
};

export default HubspotForm;
