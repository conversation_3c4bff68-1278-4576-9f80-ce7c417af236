import {useAuth} from '@aftership/automizely-product-auth';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {useWindowSize} from 'react-use';

import Modal from 'components/Modal';
import {HUBSPOT_CONTACT_SUPPORT_MODAL} from 'constants/Modals';
import {useModal} from 'hooks';

import HubspotForm from './HubspotForm';

const ContactSalesHubspotModal = () => {
	const {t} = useTranslation();
	const {close, opened} = useModal(HUBSPOT_CONTACT_SUPPORT_MODAL);
	const hubspotFormConfig = {
		region: 'na1',
		portalId: '19866549',
		formId: '423f9f8b-fb46-4b55-a91d-edc5e501f381',
	};
	const size = useWindowSize();

	const [{user, organization}] = useAuth();
	const userInfo = {
		firstname: user?.given_name,
		lastname: user?.family_name,
		email: user?.email,
		company: organization?.name,
	};

	return (
		<Modal
			title={t('CONTACT_SA_80f08')}
			noScroll
			limitHeight={size.height < 800}
			open={opened}
			onClose={close}
		>
			<Modal.Section>
				<HubspotForm
					hubspotFormConfig={hubspotFormConfig}
					userInfo={userInfo}
				/>
			</Modal.Section>
		</Modal>
	);
};

export default ContactSalesHubspotModal;
