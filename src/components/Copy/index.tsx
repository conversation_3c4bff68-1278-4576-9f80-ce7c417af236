import {Icon} from '@shopify/polaris';
import {DuplicateMinor} from '@shopify/polaris-icons';
import copy from 'copy-to-clipboard';
import React from 'react';
import {useDispatch} from 'react-redux';

import {addToast} from 'actions/toast';
import {classnames} from 'utils/classnames';

import styles from './index.module.scss';

interface CopyProps {
	value: string;
	message: string;
	addition?: React.ReactNode;
}

export function Copy({
	value,
	message,
	children,
	addition,
}: React.PropsWithChildren<CopyProps>) {
	const dispatch = useDispatch();
	const copyValue = (e: React.MouseEvent) => {
		e.stopPropagation();
		copy(value);
		dispatch(addToast({message}));
	};
	return (
		<div className={classnames(styles.copyContainer)}>
			<div className={styles.copyContent}>{children}</div>
			<div
				className={styles.copyIcon}
				onClick={copyValue}
				tabIndex={0}
				role="button"
			>
				<div>
					<Icon source={DuplicateMinor} color="base" />
				</div>
			</div>
			{addition}
		</div>
	);
}
