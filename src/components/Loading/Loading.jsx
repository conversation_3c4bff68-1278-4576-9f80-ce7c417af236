import React, {PureComponent} from 'react';

import PropTypes from 'prop-types';

import {Loading as PolarisLoading} from '@shopify/polaris';

export class Loading extends PureComponent {
	static propTypes = {
		loading: PropTypes.bool,
	};

	static defaultProps = {
		loading: false,
	};

	render() {
		const {loading} = this.props;
		return loading ? <PolarisLoading /> : null;
	}
}

export default Loading;
