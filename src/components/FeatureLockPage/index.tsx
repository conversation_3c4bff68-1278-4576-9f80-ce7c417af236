import React from 'react';

import EmptyStateCard from 'components/EmptyStateCard';
import FeatureStatementCard from 'components/FeatureStatementCard';
import {PageWithPermission} from 'components/Page';
import {RBAC_ACTION} from 'constants/RbacAction';

interface Props {
	Icon: React.FunctionComponent<
		React.SVGProps<SVGSVGElement> & {
			title?: string | undefined;
		}
	>;
	title: string;
	cardHeader: string;
	cardDesc: string;
	btnGroup: {
		primaryAction: {
			onClick: () => void;
			content: string;
		};
		secondaryAction?:
			| {
					onClick: () => void;
					content: string;
			  }
			| undefined;
	};
	featureStatements: {
		id: string;
		title: string;
		imgUrl: string;
		desc: string;
		action?: {
			content: string;
			onClick: () => void;
		};
	}[];
}
const FeatureLockPage: React.FC<Props> = ({
	Icon,
	title,
	cardHeader,
	cardDesc,
	btnGroup,
	featureStatements,
}) => {
	return (
		<PageWithPermission
			title={title}
			hasPermission={ability =>
				ability.can(RBAC_ACTION.VIEW, 'aftership/analytics')
			}
			fullWidth
		>
			<EmptyStateCard
				Icon={Icon}
				header={cardHeader}
				desc={cardDesc}
				btnGroup={btnGroup}
			/>
			<FeatureStatementCard items={featureStatements} isAnalytics />
		</PageWithPermission>
	);
};

export default FeatureLockPage;
