import {Button} from '@shopify/polaris';
import {useBookDemo} from 'AftershipNavigation';
import React from 'react';
import {useTranslation} from 'react-i18next';

import avatar from './assets/bookDemoAvatar.png';
import csmAvatar from './assets/csmAvatar.png';

const BookDemoBar = ({
	onClick,
	content,
}: {
	onClick: () => void;
	content?: string;
}) => {
	const {customerProfile} = useBookDemo();
	const {t} = useTranslation();
	const isCsmCustomer = customerProfile.contactType === 'CSM';
	return (
		<div
			style={{
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				height: '144px',
				backgroundColor: '#F6F6F7',
				padding: '0 20px',
				borderRadius: '0 0 8px 8px',
			}}
		>
			<div
				style={{
					display: 'flex',
					flexDirection: 'column',
					alignItems: 'center',
					justifyContent: 'center',
				}}
			>
				{isCsmCustomer ? (
					<img
						src={csmAvatar}
						alt="avatar"
						style={{width: '128px'}}
					/>
				) : (
					<img
						src={avatar}
						alt="csmAvatar"
						style={{width: '128px'}}
					/>
				)}
				<div
					style={{
						color: '#6D7175',
						fontSize: '12px',
						fontWeight: 400,
					}}
				>
					{t('WERE_HERE_498fa')}
				</div>
			</div>
			<div
				style={{
					display: 'flex',
					flexDirection: 'column',
					alignItems: 'start',
					marginLeft: '32px',
				}}
			>
				<div style={{fontSize: '16px', fontWeight: 600}}>
					{t('WANT_TO_LE_2a151')}
				</div>
				<div
					style={{
						fontWeight: 400,
						fontSize: '14px',
						marginTop: '4px',
					}}
				>
					{content ?? t('SEE_HOW_AF_6e24c')}
				</div>
			</div>
			<div style={{marginLeft: 'auto'}}>
				<Button onClick={onClick}>{t('REQUEST_DE_b7ee7')}</Button>
			</div>
		</div>
	);
};

export default BookDemoBar;
