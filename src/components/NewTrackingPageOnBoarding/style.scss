// .<PERSON><PERSON>-<PERSON><PERSON>-Dialog__Modal {
// 	max-width: 93.6rem;
// 	height: 50.7rem;
// }

.Container {
	width: 100%;
	height: 100%;
	display: flex;

	&__Img,
	&__<PERSON><PERSON> {
		display: inline-block;
	}

	&__Img {
		width: 53.8rem;
		height: 100%;
		background: rgba(196, 196, 196, 0.3);
		position: relative;

		.<PERSON><PERSON><PERSON> {
			position: absolute;
			width: 37rem;
			top: 5rem;
			left: 6.4rem;
			z-index: 10;
		}
		.Title {
			position: absolute;
			width: 12.4rem;
			right: 6rem;
			bottom: 6rem;
			z-index: 20;
		}
	}

	&__Handler {
		width: calc(100% - 53.8rem);
		height: 100%;
		padding: 0 4rem;
		color: #202223;
		position: relative;

		.Label {
			margin-top: 11.5rem;
			font-weight: 600;
			font-size: 2.8rem;
			line-height: 3.3rem;
		}

		.TrackingPage {
			margin-top: 1.2rem;
		}

		.SwitchHandler {
			display: flex;
			margin-top: 3rem;
			justify-content: space-between;
		}

		.ButtonHandler {
			position: absolute;
			right: 2rem;
			bottom: 2rem;
		}
	}
}
