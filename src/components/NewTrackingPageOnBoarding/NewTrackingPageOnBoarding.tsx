// Libs
import React, {useState} from 'react';
import {Button} from '@shopify/polaris';
import {useMutation} from '@apollo/client';
// Components
import Modal from 'components/Modal';
import Switch from 'components/Switch';
// Interface & Types
import {UpdateResult} from 'pages/TrackingPagesList/components/Card/ShopifyTrackLinkCard';
// Utils
import {updateShopifyTrackingStatus} from 'pages/TrackingPagesList/graphql/mutation.shopify';
// Assets
import Title from './assets/Title.png';
import Browsers from './assets/Browsers.png';
import './style.scss';

type Props = unknown;

const NewTrackingPageOnBoarding = (props: Props) => {
	const [visible, setVisible] = useState(true);
	const [shopifyLinkStatus, setShopifyLinkStatus] = useState(true);
	const [updateShopifyTrack] = useMutation<UpdateResult>(
		updateShopifyTrackingStatus,
		{
			onCompleted: () => {
				setVisible(false);
			},
		}
	);

	// const connections = useSelector(getAllConnections);
	// const afterShipCollections = useSelector(getAfterShipConnections);

	const onConfirm = () => {
		return shopifyLinkStatus
			? updateShopifyTrack({
					variables: {
						status: shopifyLinkStatus,
					},
			  })
			: setVisible(false);
	};

	return (
		<div className="NewTrackingPageOnBoarding">
			<Modal open={visible} title="" onClose={() => {}}>
				<div className="Container">
					<div className="Container__Img">
						<img className="Browsers" src={Browsers} />
						<img className="Title" src={Title} />
					</div>
					<div className="Container__Handler">
						<h1 className="Label">
							Your branded tracking page is ready!
						</h1>

						<p className="TrackingPage">Tracking page URL:</p>
						<div className="SwitchHandler">
							<span>Update Shopify with tracking link</span>
							<Switch
								value={shopifyLinkStatus}
								onChange={val => setShopifyLinkStatus(val)}
							/>
						</div>

						<div className="ButtonHandler">
							<Button primary onClick={onConfirm}>
								Got it
							</Button>
						</div>
					</div>
				</div>
			</Modal>
		</div>
	);
};

export default NewTrackingPageOnBoarding;
