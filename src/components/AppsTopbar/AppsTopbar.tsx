import {Heading, Icon, Stack, TextStyle} from '@shopify/polaris';
import {ChevronLeftMinor} from '@shopify/polaris-icons';
import React, {ReactNode} from 'react';
import {useTranslation} from 'react-i18next';
import {useHistory} from 'react-router';

import styles from './AppsTopbar.module.scss';

interface IAppsTopbarProps {
	logo: ReactNode;
	title: string;
}

const AppsTopbar = (props: IAppsTopbarProps) => {
	const {logo, title} = props;
	const history = useHistory();
	const {t} = useTranslation();

	return (
		<div className={styles['apps-topbar']}>
			<Stack alignment="center">
				<Stack alignment="center" spacing="tight">
					<button onClick={() => history.push('/apps')} type="button">
						<Icon source={ChevronLeftMinor} color="base" />
					</button>
					{logo}
					<Heading>{title}</Heading>
				</Stack>
				<Stack.Item fill />
				<TextStyle variation="subdued">
					{t('BY_AFTER_SHI_0aeab')}
				</TextStyle>
			</Stack>
		</div>
	);
};

export default AppsTopbar;
