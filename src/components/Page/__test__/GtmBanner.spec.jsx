import React from 'react';
import {Banner} from '@shopify/polaris';
import {createWithAppProvider as mountApp} from '@helper/mount';

describe('GtmBanner', () => {
	beforeAll(() => {
		jest.mock('utils/history', () => ({
			location: {
				pathname: '/dashboard/notification',
			},
		}));
	});

	it('should shows correct props when everything is ok', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);

		setBannerProps({
			status: 'success',
			action: {
				content: 'content',
				disabled: false,
			},
			icon: 'http://test.img',
			active: true,
			routes: ['/dashboard/notification'],
		});
		const comp = mountApp(<GtmBanner />);
		expect(comp.find(Banner).props()).toMatchInlineSnapshot(`
		Object {
		  "action": Object {
		    "content": "content",
		    "disabled": false,
		    "onAction": [Function],
		  },
		  "children": "Use your finance report to get detailed
		information about your business. Let us know
		what you think",
		  "icon": "http://test.img",
		  "onDismiss": undefined,
		  "secondaryAction": undefined,
		  "status": "success",
		  "title": undefined,
		}
	`);
	});

	it('should fallback to default view when parse error', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);
		setBannerProps({
			status: {error: 'errorStatus'},
			action: {
				content: '',
				disabled: false,
			},
			active: true,
			routes: ['/dashboard/notification'],
		});
		const comp = mountApp(<GtmBanner />);
		expect(comp.find(Banner).props()).toMatchInlineSnapshot(`
		Object {
		  "children": <p>
		    Use your finance report to get detailed
		information about your business. Let us know
		what you think
		  </p>,
		  "onDismiss": [Function],
		}
	`);
	});

	it('should show empty when active is false', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);
		setBannerProps({
			status: {error: 'errorStatus'},
			action: {
				content: '',
				disabled: false,
			},
			active: false,
			routes: ['/dashboard/notification'],
		});
		const comp = mountApp(<GtmBanner />);
		expect(comp.contains(Banner)).toBeFalsy();
	});

	it('should clear state when input clear key', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);
		setBannerProps({
			status: {error: 'errorStatus'},
			action: {
				content: '',
				disabled: false,
			},
			active: true,
			routes: ['/dashboard/notification'],
		});
		setBannerProps({clear: true});
		const comp = mountApp(<GtmBanner />);
		expect(comp.contains(Banner)).toBeFalsy();
	});

	it('should not show banner when routes is not match', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);
		setBannerProps({
			status: 'success',
			action: {
				content: 'content',
				disabled: false,
			},
			icon: 'http://test.img',
			active: true,
			routes: ['/dashboard/shipment'],
		});
		const comp = mountApp(<GtmBanner />);
		expect(comp.contains(Banner)).toBeFalsy();
	});

	it('should not show close button when canClose is not true', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);
		setBannerProps({
			status: 'success',
			action: {
				content: 'content',
				disabled: false,
			},
			icon: 'http://test.img',
			active: true,
			canClose: false,
		});
		const comp = mountApp(<GtmBanner />);
		expect(comp.find(Banner).props().onDismiss).toBeUndefined();
	});

	it('should not show banner when user click close button', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);
		setBannerProps({
			status: 'success',
			action: {
				content: 'content',
				disabled: false,
			},
			icon: 'http://test.img',
			active: true,
			canClose: true,
		});
		const comp = mountApp(<GtmBanner />);
		comp.find(Banner)
			.props()
			.onDismiss();
		comp.update();
		expect(comp.contains(Banner)).toBeFalsy();
		setBannerProps({
			status: 'success',
			active: true,
			canClose: true,
		});
		comp.update();
		expect(comp.contains(Banner)).toBeFalsy();
	});

	it('should show banner when routes is empty(means show banner in all routes)', async () => {
		const {default: GtmBanner, setBannerProps} = await import(
			'../GtmBanner'
		);

		setBannerProps({
			status: 'success',
			action: {
				content: 'content',
				disabled: false,
			},
			icon: 'http://test.img',
			active: true,
		});
		const comp = mountApp(<GtmBanner />);
		expect(comp.find(Banner).props()).toMatchInlineSnapshot(`
		Object {
		  "action": Object {
		    "content": "content",
		    "disabled": false,
		    "onAction": [Function],
		  },
		  "children": "Use your finance report to get detailed
		information about your business. Let us know
		what you think",
		  "icon": "http://test.img",
		  "onDismiss": undefined,
		  "secondaryAction": undefined,
		  "status": "success",
		  "title": undefined,
		}
	`);
	});
});
