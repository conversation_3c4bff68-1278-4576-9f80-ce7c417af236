import React from 'react';
import {shallow} from 'enzyme';
import * as authModule from '@aftership/automizely-product-auth';
import {PAGE_BANNER} from 'constants/BannerNames';
import {withFullStore as mountApp} from '@helper/mount';

import Page from '../Page';

describe('Page component', () => {
	it('render <Page />.', () => {
		const wrapper = shallow(
			React.createElement(Page, {
				removeBannerCleanly: () => {},
			})
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('when unmount, will remove banner which id is PAGE_BANNNER.', () => {
		jest.spyOn(authModule, 'useAuth').mockReturnValue([
			{user: {}, organization: {}, loading: false},
		]);
		let bannerIdToRemove;
		const props = {
			removeBannerCleanly: id => {
				bannerIdToRemove = id;
			},
		};
		const o = mountApp(<Page {...props} />);
		o.unmount();
		expect(bannerIdToRemove).toBe(PAGE_BANNER);
	});
});
