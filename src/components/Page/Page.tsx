import {useRBACContext} from '@aftership/automizely-rbac-react';
import {capture} from '@aftership/datacat';
import * as Sentry from '@sentry/react';
import {Card, Page as PolarisPage, PageProps} from '@shopify/polaris';
import React, {useEffect} from 'react';
import {Trans, useTranslation} from 'react-i18next';
import {useLocation} from 'react-router';

import SpinLoading from 'components/SpinLoading';
import {PAGE_BANNER} from 'constants/BannerNames';
import useGetOrganizationOwner from 'hooks/useGetOrganizationOwner';
import {gaPageEnter, gaPageLeave} from 'utils/gtag';
import isCompany from 'utils/isCompany';

import GtmBanner from './GtmBanner';

interface IProps extends PageProps {
	removeBannerCleanly: (bannerId: string) => void;
	title: string;
	mediumWidth?: boolean;
	wrapper?: React.ComponentType;
	loading?: boolean;
	loadingComponent?: React.ReactNode;
	enteredEventTitle?: string;
}

const Page: React.FC<IProps> = ({
	removeBannerCleanly,
	title,
	wrapper,
	children,
	loading,
	mediumWidth,
	enteredEventTitle,
	loadingComponent = <SpinLoading.Fullscreen />,
	...rest
}) => {
	const location = useLocation();
	useEffect(() => {
		let enteredEvent: any = null;
		window.scrollTo(0, 0);
		// Delay to debounce to prevent push event repeatedly
		// when Page compontent mount and unmount quickly
		const delayPushEvent = setTimeout(() => {
			if (typeof title === 'string') {
				enteredEvent = {
					title,
					enter_time: Date.now(),
					page_url: location.pathname,
				};
			} else {
				enteredEvent = {
					title: enteredEventTitle,
					enter_time: Date.now(),
					page_url: location.pathname,
				};
			}

			gaPageEnter(enteredEvent);
			capture('$pageview');
		}, 0);

		return () => {
			if (enteredEvent) {
				gaPageLeave(enteredEvent);
			}

			removeBannerCleanly(PAGE_BANNER);
			clearTimeout(delayPushEvent);
		};
	}, []);

	const Wrapper = wrapper || React.Fragment;

	return (
		<Sentry.ErrorBoundary
			beforeCapture={scope => {
				scope.setLevel('fatal');
			}}
		>
			<Wrapper>
				<GtmBanner />
				{loading && loadingComponent}
				{!loading && Boolean(mediumWidth) && (
					<PolarisPage title={title} {...rest} fullWidth>
						<div style={{maxWidth: 1136, margin: 'auto'}}>
							{children}
						</div>
					</PolarisPage>
				)}
				{!loading && !mediumWidth && (
					<PolarisPage title={title} {...rest}>
						{children}
					</PolarisPage>
				)}
			</Wrapper>
		</Sentry.ErrorBoundary>
	);
};

interface PermissionPageProps extends IProps {
	hasPermission(
		ability: ReturnType<typeof useRBACContext>['ability']
	): boolean;
	fallback?: React.ReactNode;
}

interface NotPermissionPageProps extends PageProps {
	ownerEmail?: string;
}

export const NotPermissionPage: React.FC<NotPermissionPageProps> = ({
	title,
	breadcrumbs,
	ownerEmail,
}) => {
	const {t} = useTranslation();
	return (
		<PolarisPage title={title} breadcrumbs={breadcrumbs}>
			<Card>
				<div
					style={{
						padding: '109px 20px',
						textAlign: 'center',
					}}
				>
					<p
						style={{
							fontSize: '20px',
							lineHeight: '28px',
							fontWeight: 'bold',
						}}
					>
						{t('NO_PERMISSION_PAGE')}
					</p>
					<br />
					<p>
						{/** t('THIS_PAGE_IS_1231') */}
						<Trans
							i18nKey="THIS_PAGE_IS_1231"
							values={{ownerEmail}}
							components={{
								b: <b />,
							}}
						/>
					</p>
				</div>
			</Card>
		</PolarisPage>
	);
};

export const PermissionPage: React.FC<PermissionPageProps> = ({
	hasPermission,
	fallback,
	...rest
}) => {
	const {ability, fetched} = useRBACContext();
	const {data} = useGetOrganizationOwner();
	const ownerEmail = data?.email;

	if (isCompany()) {
		return <Page {...rest} />;
	}

	if (!fetched) {
		return (
			<>
				{fallback || (
					<div
						style={{
							height: '100vh',
						}}
					>
						<SpinLoading />
					</div>
				)}
			</>
		);
	}

	return hasPermission(ability) ? (
		<Page {...rest} />
	) : (
		<NotPermissionPage
			ownerEmail={ownerEmail}
			title={rest.title}
			breadcrumbs={rest.breadcrumbs}
		/>
	);
};

export default Page;
