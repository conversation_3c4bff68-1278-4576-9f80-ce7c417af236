import React from 'react';
import {Banner, Layout} from '@shopify/polaris';
import history from 'utils/history';
import './gtm-banner.css';

export interface BannerProps {
	id?: string;
	action?: Partial<{
		accessibilityLabel: string;
		content: string;
		disabled: boolean;
		external: boolean;
		id: string;
		url: string;
		loading: boolean;
	}>;
	icon?: string;
	secondaryAction?: Partial<{
		accessibilityLabel: string;
		content: string;
		external: boolean;
		id: string;
		url: string;
	}>;
	status?: 'success' | 'info' | 'warning' | 'critical';
	title?: string;
	active?: boolean;
	content?: string;
	routes?: string[];
	canClose?: boolean;
	isClosed?: boolean;
}

const defaultContent = `Use your finance report to get detailed
information about your business. Let us know
what you think`;

export function getBannerProps(key: keyof BannerProps, defaultValue: any) {
	try {
		const gtmBanner: BannerProps = JSON.parse(
			localStorage.getItem('gtmBanner') || '{}'
		);
		const raw = gtmBanner[key];
		if (raw === 'true') {
			return true;
		}
		if (raw === 'false') {
			return false;
		}
		return raw || defaultValue;
	} catch (err) {
		return defaultValue;
	}
}

export function setBannerProps({
	clear,
	...rest
}: BannerProps & {clear?: boolean} = {}) {
	if (clear) {
		localStorage.removeItem('gtmBanner');
	} else {
		localStorage.setItem('gtmBanner', JSON.stringify(rest));
	}
}

export function setBannerProp<T extends keyof BannerProps>(
	key: T,
	value: BannerProps[T]
) {
	try {
		const gtmBanner: BannerProps = JSON.parse(
			localStorage.getItem('gtmBanner') || '{}'
		);
		gtmBanner[key] = value;
		setBannerProps(gtmBanner);
	} catch (err) {
		// do nothing
	}
}

export function getAction() {
	const action = getBannerProps('action', undefined);
	if (action && action.content) {
		action.onAction = () => {
			window.dataLayer.push({
				event: 'gtmBannerClicked',
			});
		};
	}
	return action;
}

// for gtm usage
export default class GtmBanner extends React.PureComponent {
	state = {
		opened:
			getBannerProps('active', false) &&
			!getBannerProps('isClosed', false),
		hasError: false,
	};

	componentDidCatch() {
		console.error('errors happened, check your gtm code');
		this.setState({hasError: true});
	}

	getDefaultView() {
		return (
			<>
				<Layout>
					<Layout.Section>
						<Banner
							onDismiss={() => this.setState({opened: false})}
						>
							<p>{defaultContent}</p>
						</Banner>
					</Layout.Section>
				</Layout>
				<div style={{marginBottom: '2rem'}} />
			</>
		);
	}

	render() {
		const {hasError, opened} = this.state;
		const title = getBannerProps('title', undefined);
		const secondaryAction = getBannerProps('secondaryAction', undefined);
		const status = getBannerProps('status', 'info');
		const content = getBannerProps('content', defaultContent);
		const icon = getBannerProps('icon', undefined);
		const canClose = getBannerProps('canClose', false);
		const routes: string[] = getBannerProps('routes', []);
		const path = history.location.pathname;

		if (!opened || (routes.length > 0 && !routes.includes(path))) {
			return <div />;
		}

		if (hasError) {
			return this.getDefaultView();
		}

		return (
			<>
				<Layout>
					<Layout.Section>
						<div id="gtm-banner">
							<Banner
								action={getAction()}
								secondaryAction={secondaryAction}
								status={status}
								icon={icon}
								title={title}
								onDismiss={
									canClose
										? () => {
												setBannerProp('isClosed', true);
												this.setState({opened: false});
										  }
										: undefined
								}
							>
								{content}
							</Banner>
						</div>
					</Layout.Section>
				</Layout>
				<div style={{marginBottom: '2rem'}} />
			</>
		);
	}
}
