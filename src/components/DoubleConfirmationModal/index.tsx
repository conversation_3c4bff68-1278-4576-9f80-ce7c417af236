import {Modal, TextStyle} from '@shopify/polaris';
import React, {FC, useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';

import {removeBanner} from 'actions/banners';
import {MODAL_BANNER} from 'constants/BannerNames';
import {ModalBanner} from 'pages/components/Banner';

interface IProps {
	title: string;
	description: string;
	buttonText: string;
	destructive?: boolean;
	open: boolean;
	isLoading?: boolean;
	onClose: () => void;
	onConfirm: () => void;
}

export const DoubleConfirmationModal: FC<IProps> = ({
	title,
	description,
	buttonText,
	destructive,
	open,
	isLoading,
	onClose,
	onConfirm,
}) => {
	const {t} = useTranslation();
	const dispatch = useDispatch();

	useEffect(() => {
		return () => {
			dispatch(removeBanner(MODAL_BANNER));
		};
	}, [dispatch]);

	return (
		<Modal
			title={title}
			open={open}
			onClose={onClose}
			primaryAction={{
				content: buttonText,
				onAction: onConfirm,
				loading: isLoading,
				destructive,
			}}
			secondaryActions={[
				{
					content: t('CANCEL_ea478'),
					onAction: onClose,
				},
			]}
		>
			<Modal.Section>
				<ModalBanner hasMarginBottom hasDismissButton={false} />
				<TextStyle>{description}</TextStyle>
			</Modal.Section>
		</Modal>
	);
};
