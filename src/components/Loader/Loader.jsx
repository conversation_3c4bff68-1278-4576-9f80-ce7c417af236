import React from 'react';
import PropTypes from 'prop-types';

import {Spinner} from '@shopify/polaris';

import './Loader.css';

const Loader = props => {
	return (
		<div className="Loader" style={{height: props.defaultHeight}}>
			<Spinner size={props.size} />
		</div>
	);
};

Loader.propTypes = {
	defaultHeight: PropTypes.number,
	size: PropTypes.string,
};

Loader.defaultProps = {
	size: 'large',
	defaultHeight: 400,
};

export default Loader;
