:global(.Polaris-Modal-Dialog__Modal):has(.connectShopifyStoreModalContainer) {
	max-width: 500px;
	border-radius: 16px;
	:global(.Polaris-Modal-CloseButton) {
		--p-icon: #8c9196;
		margin: 10px 2px 0 0;
		:global(.Polaris-Icon) {
			width: 16px;
			height: 16px;
		}
	}
}

:global(div.Polaris-Modal-Dialog__Container):has(.connectShopifyStoreModalContainer) {
	z-index: 523;
}

.connectShopifyStoreModalContainer {
	padding: 45px 35px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 20px;
	text-align: center;
	.title {
		font-size: 24px;
		font-weight: 700;
		margin-bottom: 8px;
	}
	.description {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
		letter-spacing: 0.2px;
		margin-bottom: 8px;
	}
	:global(.<PERSON>is-But<PERSON>) {
		color: #fff;
		padding: 12px 24px;
		background: var(--Branding-Admin---Primary, #5a67cb);
		border: none;
	}
}
