import {Button, Modal} from '@shopify/polaris';
import React from 'react';
import {useTranslation} from 'react-i18next';

import {StoreIcon} from 'components/StoreIcon';
import {CONNECT_SHOPIFY_STORE_MODAL} from 'constants/Modals';
import {useModal} from 'hooks';

import styles from './index.module.scss';

export function ConnectShopifyStoreModal() {
	const {t} = useTranslation();
	const {opened, close} = useModal(CONNECT_SHOPIFY_STORE_MODAL);

	return (
		<Modal
			titleHidden
			title=""
			open={opened}
			onClose={() => {
				close();
			}}
		>
			<div className={styles.connectShopifyStoreModalContainer}>
				<StoreIcon platform="shopify" width="81px" height="90px" />
				<div>
					<p className={styles.title}>{t('CONNECT_SH_6ba07')}</p>
					<p className={styles.description}>
						{t('YOU_MUST_C_6f413')}
					</p>
				</div>
				<Button
					fullWidth
					onClick={() => {
						window.open(
							'https://apps.shopify.com/aftership',
							'_blank',
							'noopener'
						);
					}}
				>
					{t('CONNECT_ST_41d50')}
				</Button>
			</div>
		</Modal>
	);
}
