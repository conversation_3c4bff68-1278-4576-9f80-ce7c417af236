/* eslint-disable @typescript-eslint/no-explicit-any */
import {isArray} from 'lodash';

export function transItem(item: any): typeof item {
	if (!window.AFTERSHIP_TRANS) {
		return item;
	}
	if (typeof item === 'string') {
		return window.AFTERSHIP_TRANS(item) || item;
	}
	if (isArray(item)) {
		return item.map((val: any) => transItem(val));
	}

	let label;
	let name;
	let content;
	let title;
	let options;
	let children;
	let tags;
	if (item.labelI18nKey || item.label) {
		label =
			window.AFTERSHIP_TRANS(item.labelI18nKey || item.label) ||
			item.label;
	}
	if (item.nameI18nKey || item.name) {
		name =
			window.AFTERSHIP_TRANS(item.nameI18nKey || item.name) || item.name;
	}
	if (item.content || item.contentI18nKey) {
		content =
			window.AFTERSHIP_TRANS(item.contentI18nKey || item.content) ||
			item.content;
	}
	if (item.title) {
		title = window.AFTERSHIP_TRANS(item.title) || item.title;
	}
	if (item.options?.length) {
		options = item.options.map((option: Record<string, any>) =>
			transItem(option)
		);
	}
	if (item.children?.length) {
		children = item.children.map((child: Record<string, any>) =>
			transItem(child)
		);
	}
	if (item.tags?.length) {
		tags = item.tags.map((child: Record<string, any>) => transItem(child));
	}
	return {
		...item,
		label,
		name,
		content,
		title,
		options,
		children,
		tags,
	};
}

export function objectWithTrans<T extends Record<string, any>>(obj: T) {
	const newObj = {...obj};
	Object.keys(obj).forEach(key => {
		const translated: Record<string, unknown> = {};
		Object.defineProperty(newObj, key, {
			get() {
				if (!window.AFTERSHIP_TRANS) {
					return obj[key];
				}
				if (!translated[key]) {
					translated[key] = transItem(obj[key]);
				}
				return translated[key];
			},
		});
	});
	return newObj;
}
