import {transItem} from './objectWithTrans';

export function arrayWithTrans<T extends Record<string, any>>(items: T[]) {
	const newItems = items.map(item => ({...item}));
	items.forEach((_, key) => {
		const translated: (Record<string, unknown> | string)[] = [];
		Object.defineProperty(newItems, key, {
			get() {
				if (!window.AFTERSHIP_TRANS) {
					return items[key];
				}
				if (!translated[key]) {
					translated[key] = transItem(items[key]);
				}
				return translated[key];
			},
		});
	});
	return newItems;
}
