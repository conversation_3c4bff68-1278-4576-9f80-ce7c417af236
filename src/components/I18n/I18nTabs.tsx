import {Badge, Stack, Tabs, TabsProps, TextStyle} from '@shopify/polaris';
import React from 'react';
import {useTranslation} from 'react-i18next';

type Tab = Omit<TabsProps['tabs'][0], 'content'> & {
	content?: React.ReactNode;
	contentI18nKey: string;
	accessibilityLabelI18nKey?: string;
};

export interface I18nTabsProps extends Omit<TabsProps, 'tabs'> {
	tabs: Tab[];
}

export const I18nTabs = React.memo((props: I18nTabsProps) => {
	const {t} = useTranslation();
	return (
		<Tabs
			{...props}
			tabs={props.tabs.map(tab => ({
				...tab,
				content: tab.content ? tab.content : t(tab.contentI18nKey),
				accessibilityLabel: tab.accessibilityLabelI18nKey
					? t(tab.accessibilityLabelI18nKey)
					: tab.accessibilityLabel,
			}))}
		/>
	);
});

export interface II18nTabContentWithBadgeProps {
	content: string;
	badge: string;
}

export const I18nTabContentWithBadge = (
	props: II18nTabContentWithBadgeProps
) => {
	const {content, badge} = props;

	const {t} = useTranslation();

	return (
		<Stack spacing="extraTight">
			<TextStyle>{t(content)}</TextStyle>
			<Badge>{t(badge)}</Badge>
		</Stack>
	);
};
