import {Stack, Tag, TextField, Popover, ActionList} from '@shopify/polaris';
import {ReturnMinor} from '@shopify/polaris-icons';
import React, {useState} from 'react';

export function MultiTextField({
	label,
	values,
	setValue,
	placeholder,
	error,
}: {
	label?: string;
	values: string[];
	setValue: (value: string[]) => void;
	placeholder?: string;
	error?: string;
}) {
	const [input, setInput] = useState('');
	const [popoverActive, setPopoverActive] = useState(false);

	const validInput = Boolean(input) && !values.includes(input);

	const onChange = () => {
		const value = input?.trim?.();

		if (value) {
			if (!values.includes(value)) {
				setValue([...values, value]);
			}

			setInput('');
		}
	};

	const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
		if (event?.keyCode === 13) {
			onChange();
		}
	};

	return (
		<Stack vertical spacing="tight">
			<Popover
				active={popoverActive}
				onClose={() => {
					setPopoverActive(f => !f);
				}}
				activator={
					<div role="none" onKeyDown={handleKeyDown}>
						<TextField
							label={label}
							value={input}
							onChange={(value: string) => {
								setInput(value);
							}}
							onFocus={() => {
								setPopoverActive(true);
							}}
							onBlur={() => {
								setPopoverActive(false);
								onChange();
							}}
							placeholder={placeholder}
							error={error}
							autoComplete="off"
						/>
					</div>
				}
				fullWidth
				autofocusTarget="none"
			>
				{validInput ? (
					<ActionList
						items={[
							{
								content: `Enter "${input}"`,
								icon: ReturnMinor,
								onAction: () => {},
							},
						]}
					/>
				) : null}
			</Popover>
			{values.length > 0 && (
				<Stack spacing="tight">
					{values.map(tag => (
						<Tag
							key={tag}
							onRemove={() =>
								setValue(values.filter(item => item !== tag))
							}
						>
							{tag}
						</Tag>
					))}
				</Stack>
			)}
		</Stack>
	);
}
