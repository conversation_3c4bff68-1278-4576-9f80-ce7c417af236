/* eslint-disable jsx-a11y/no-static-element-interactions */
import {
	But<PERSON>,
	Icon,
	OptionList,
	Popover,
	Stack,
	Tag,
	TextStyle,
} from '@shopify/polaris';
import {DeleteMinor} from '@shopify/polaris-icons';
import {set, without} from 'lodash';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {FilterPropertyMap} from 'constants/ShipmentFilters';
import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {MultiChoiceList} from 'pages/Shipments/components/ShipmentFilter/AdvancedFilters/components/MultiFilter/MultiChoiceList';
import {
	FilterData,
	FilterPropertyName,
	Operator,
} from 'pages/Shipments/interfaces/Filter';
import {getCharCode} from 'utils/string';

import {moreFilterKeysNameMap, moreFilterKeysToI18nMap} from '../constants';
import {useGetFilterOptions} from '../hooks/useGetFilterOptions';
import {useMoreFilters} from '../hooks/useMoreFilters';
import {getOperatorDescription} from '../utils/analyticsUtils';

interface Props {
	filterLabel: string;
	query:
		| ReturnType<typeof transformTrackingAnalyticsQuery>['query']
		| FilterData
		| any;
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
	enabledCustomFieldsList: any;
	setEnabledCustomFieldsList: any;
	defaultOperator?: boolean;
	isSubscription?: boolean;
	filter: any;
}

export const MoreFilterCard = ({
	filterLabel,
	query,
	onChangeQuery,
	enabledCustomFieldsList,
	setEnabledCustomFieldsList,
	defaultOperator,
	isSubscription,
	filter,
}: Props) => {
	const {t} = useTranslation();

	const [popoverActive, setPopoverActive] = useState(false);
	const [operationSelectActive, setOperationSelectActive] = useState(false);

	const togglePopoverActive = useCallback(
		() => setPopoverActive(popoverActive => !popoverActive),
		[]
	);
	const toggleOperationSelectActive = useCallback(
		() =>
			setOperationSelectActive(
				operationSelectActive => !operationSelectActive
			),
		[]
	);

	const {
		getResultValues,
		deleteTargetTypeAdvanceFilterItem,
		getInitialAdvanceFilterItem,
		changeAdvanceFilterItemKeyValue,
	} = useMoreFilters({
		isSubscription: isSubscription,
		query: query,
		onChangeQuery: onChangeQuery,
		targetPath: 'basicFilter',
		propertyKey: filterLabel,
	});

	const options = useGetFilterOptions({
		filterKey: filterLabel,
		filter: filter,
	});
	const [currentSelectOperator, setCurrentSelectOperator] = useState(
		getInitialAdvanceFilterItem(filterLabel).operator
	);

	const [activeFieldValue, setActiveFieldValue] = useState<any>(
		getInitialAdvanceFilterItem(filterLabel).values || []
	);

	const activator = (
		<Button
			// disabled={!available}
			disclosure="down"
			textAlign="left"
			onClick={togglePopoverActive}
			fullWidth
		>
			Select values
		</Button>
	);

	const operationActivator = (
		<Button
			// disabled={!available}
			disclosure="down"
			textAlign="left"
			plain
			onClick={toggleOperationSelectActive}
			fullWidth
		>
			{getOperatorDescription(currentSelectOperator)}
		</Button>
	);
	useEffect(() => {}, [activeFieldValue]);
	const resultChecked = useMemo(() => {
		const checkKeysMap = {};

		if (activeFieldValue?.length) {
			activeFieldValue?.forEach((key: string) => {
				set(checkKeysMap, getCharCode(key), true);
			});
		}
		return checkKeysMap;
	}, [activeFieldValue]);

	return (
		<>
			<div
				style={{
					padding: '16px 12px',
					backgroundColor: '#F6F6F7',
					borderRadius: '8px',
					marginBottom: '16px',
				}}
			>
				<div
					style={{
						marginBottom: '14px',
						display: 'flex',
						justifyContent: 'space-between',
					}}
				>
					<div style={{display: 'flex'}}>
						<TextStyle>
							{t(
								moreFilterKeysToI18nMap[filterLabel],
								moreFilterKeysNameMap[filterLabel]
							)}
						</TextStyle>

						{!defaultOperator && (
							<Popover
								active={operationSelectActive}
								activator={operationActivator}
								onClose={toggleOperationSelectActive}
								fullWidth
								fixed
							>
								<div
									role="button"
									tabIndex={0}
									onClick={e => e.stopPropagation()}
									style={{whiteSpace: 'nowrap'}}
								>
									<OptionList
										options={
											FilterPropertyMap[
												FilterPropertyName.customFields
											].operators
										}
										selected={[]}
										onChange={value => {
											// 设置当前的operator
											setCurrentSelectOperator(
												value?.[0] as Operator
											);

											changeAdvanceFilterItemKeyValue({
												propertyLabel: filterLabel,
												currentOperator:
													value?.[0] as Operator,
											});

											setOperationSelectActive(false);
										}}
									/>
								</div>
							</Popover>
						)}
					</div>

					<div
						style={{cursor: 'pointer'}}
						onClick={() => {
							setEnabledCustomFieldsList(
								without(enabledCustomFieldsList, filterLabel)
							);
							deleteTargetTypeAdvanceFilterItem(filterLabel);
						}}
					>
						<Icon source={DeleteMinor} />
					</div>
				</div>
				<Popover
					active={popoverActive}
					activator={activator}
					onClose={togglePopoverActive}
					fullWidth
					fixed
				>
					<div
						style={{padding: '10px'}}
						onClick={e => {
							e.stopPropagation();
						}}
					>
						<MultiChoiceList
							options={options || []}
							hideValue
							placeholder={t('SEARCHVALU_9ed16', 'Search values')}
							withSearchInput
							onChange={handleValue => {
								const currentHandleOptionValue =
									options
										?.filter(({key}) => key === handleValue)
										.map(({label}) => label) || [];

								setActiveFieldValue(
									getResultValues(
										filterLabel,
										currentHandleOptionValue?.[0]
									)
								);

								changeAdvanceFilterItemKeyValue({
									propertyLabel: filterLabel,
									currentValues: getResultValues(
										filterLabel,
										currentHandleOptionValue?.[0]
									),
								});
							}}
							checkedKeysMap={resultChecked}
						/>
						<Popover.Pane fixed>
							<div style={{padding: '10px 0 0'}}>
								<Button
									plain
									disabled={!activeFieldValue.length}
									onClick={() => {
										setActiveFieldValue([]);
										changeAdvanceFilterItemKeyValue({
											propertyLabel: filterLabel,
											currentValues: [],
										});
									}}
								>
									{t('CLEAR_72975')}
								</Button>
							</div>
						</Popover.Pane>
					</div>
				</Popover>
				<div style={{marginTop: '4px', paddingRight: '8px'}}>
					<Stack spacing="tight">
						{activeFieldValue?.map((item: string) => {
							return (
								<Tag
									key={item}
									onRemove={() => {
										// 当前操作的值的字符串string表现
										const currentHandleOptionValue =
											options
												?.filter(
													({label}) => label === item
												)
												.map(({label}) => label) || [];

										setActiveFieldValue(
											getResultValues(
												filterLabel,
												currentHandleOptionValue?.[0]
											)
										);

										changeAdvanceFilterItemKeyValue({
											propertyLabel: filterLabel,
											currentValues: getResultValues(
												filterLabel,
												currentHandleOptionValue?.[0]
											),
										});
									}}
								>
									{item}
								</Tag>
							);
						})}
					</Stack>
				</div>
			</div>
		</>
	);
};
