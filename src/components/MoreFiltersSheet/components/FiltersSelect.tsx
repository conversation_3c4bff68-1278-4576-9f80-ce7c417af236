import {But<PERSON>, OptionList, Popover, Tooltip} from '@shopify/polaris';
import {difference} from 'lodash';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {useTranslation} from 'react-i18next';

import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {Operator} from 'pages/Shipments/interfaces/Filter';
import isCompany from 'utils/isCompany';

import {moreFilterKeysNameMap, moreFilterKeysToI18nMap} from '../constants';
import {useMoreFilters} from '../hooks/useMoreFilters';

interface Props {
	query: ReturnType<typeof transformTrackingAnalyticsQuery>['query'];
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
	enabledFiltersList: string[];
	setEnabledFiltersList: (value: string[]) => void;
	isSubscription?: boolean;
}

export const FiltersSelect = ({
	query,
	onChangeQuery,
	enabledFiltersList,
	setEnabledFiltersList,
	isSubscription,
}: Props) => {
	const {t} = useTranslation();
	const [popoverActive, setPopoverActive] = useState(false);

	const togglePopoverActive = useCallback(
		() => setPopoverActive(popoverActive => !popoverActive),
		[]
	);

	let moreFilterKeys = Object.keys(moreFilterKeysNameMap) || [];
	// 针对 Company console 暂时不支持 Shipping Method 和 Store
	moreFilterKeys = isCompany() ? moreFilterKeys.slice(0, 2) : moreFilterKeys;

	const {
		advanceFilterItemForPropertyKey,
		createTargetTypeAdvanceFilter,
		clearTargetTypeAdvanceFilter,
	} = useMoreFilters({
		isSubscription: isSubscription,
		query: query,
		onChangeQuery: onChangeQuery,
		targetPath: 'basicFilter',
		propertyKey: moreFilterKeys,
	});

	// 除去已经enable的customField
	const filtersList = useMemo(() => {
		const canEnabledFiltersList = difference(
			moreFilterKeys,
			enabledFiltersList
		);
		return canEnabledFiltersList.map(filterKey => {
			return {
				label: t(
					moreFilterKeysToI18nMap[filterKey],
					moreFilterKeysNameMap[filterKey]
				),
				value: filterKey,
			};
		});
	}, [enabledFiltersList]);

	const filtersValueList = filtersList.map(item => {
		return item.value;
	});

	const activator = !filtersValueList.length ? (
		<Tooltip content={t('ALLFILTERS_22bbc', 'All filters added')}>
			<Button
				onClick={togglePopoverActive}
				disabled={!filtersValueList.length}
			>
				{t('ADDFILTER_132c2', '+ Add filter')}
			</Button>
		</Tooltip>
	) : (
		<Button onClick={togglePopoverActive}>
			{t('ADDFILTER_132c2', '+ Add filter')}
		</Button>
	);

	useEffect(() => {
		if (!enabledFiltersList.length) {
			setTimeout(() => {
				setPopoverActive(true);
			}, 500);
		}
	}, [enabledFiltersList]);
	return (
		<>
			<div style={{display: 'flex', justifyContent: 'space-between'}}>
				<Popover
					active={popoverActive}
					activator={activator}
					autofocusTarget="first-node"
					onClose={togglePopoverActive}
				>
					<div style={{width: '150px'}}>
						<OptionList
							onChange={value => {
								setEnabledFiltersList([
									...enabledFiltersList,
									...value,
								]);

								createTargetTypeAdvanceFilter({
									propertyLabel: value[0] || '',
									operator: Operator.IS_ANY_OF,
									propertyKeyParam: value[0] || '',
								});
								setPopoverActive(false);
							}}
							options={filtersList}
							selected={[]}
						/>
					</div>
				</Popover>
				<Button
					disabled={!advanceFilterItemForPropertyKey?.length}
					plain
					onClick={() => {
						setEnabledFiltersList([]);

						clearTargetTypeAdvanceFilter();
					}}
				>
					{t('CLEARALL_262a3', 'Clear all')}
				</Button>
			</div>
		</>
	);
};
