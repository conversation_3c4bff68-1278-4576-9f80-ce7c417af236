import {cloneDeep, set} from 'lodash';

import {Operator} from 'pages/Shipments/interfaces/Filter';

import {labelStatusKeyMap} from '../constants';

export const getOperatorDescription = (operator: Operator) => {
	switch (operator) {
		case Operator.IS_ANY_OF:
			return 'is any of';

		case Operator.IS_NONE_OF:
			return 'is none of';

		default:
			return '';
	}
};

export const getFormattedQueryFilter = (queryFilter: any) => {
	const clonedQueryFilter = cloneDeep(queryFilter);
	clonedQueryFilter?.basicFilter?.forEach((filter: any, index: number) => {
		if (filter.propertyName === 'status') {
			set(
				clonedQueryFilter,
				`basicFilter[${index}].values`,
				filter?.values?.map((value: any) => {
					if (typeof value === 'string') {
						return {
							status: labelStatusKeyMap[value],
						};
					}
					return value;
				})
			);
		}
	});
	return clonedQueryFilter;
};
