import {useCallback, useMemo} from 'react';

import {
	TrackingStatus,
	Tracking_Facets_Include_Field,
} from '@graphql/generated';
import {useDatePickerRangeOptions} from 'pages/Dashboard/hooks/useDatePickerRangeOptions';
import {useTrackingFacetV2} from 'pages/Shipments/hooks';
import {toOrgMoment} from 'utils/day';
import {numberWithCommas} from 'utils/number';
import {getCharCode} from 'utils/string';

export const useGetFilterOptions = ({
	filterKey,
	filter,
}: {
	filterKey: string;
	filter: any;
}): {
	key: string;
	label: string;
	value?: string;
}[] => {
	const {minDate} = useDatePickerRangeOptions();

	// const {data: orderTagFacets} = useTrackingAnalyticsFacet({
	// 	filter: {
	// 		dateType: 'CREATED_AT',
	// 		startTime: toOrgMoment(minDate)
	// 			.add('1', 'day')
	// 			.startOf('day')
	// 			.format(),
	// 		endTime: toOrgMoment().endOf('day').format(),
	// 		organizationIds: filter.organizationIds,
	// 	} as any,
	// 	key: Tracking_Analytics_Facets_Include_Field.OrderTags as any,

	// 	includeField: Tracking_Analytics_Facets_Include_Field.OrderTags,
	// 	mounted: true,
	// });
	const queryParams = {
		filter: {
			dateType: 'CREATED_AT',
			startTime: toOrgMoment(minDate)
				.add('1', 'day')
				.startOf('day')
				.format(),
			endTime: toOrgMoment().endOf('day').format(),
			organizationIds: filter?.organizationIds,
		} as any,
		advancedFilters: {},
	};
	const {data: orderTagFacets} = useTrackingFacetV2({
		...queryParams,
		key: 'orderTags',
		includeField: Tracking_Facets_Include_Field.OrderTags,
		enabled: filterKey === 'order_tags',
	});

	const {data: storeIdsFacets} = useTrackingFacetV2({
		...queryParams,
		key: 'storeIds',
		includeField: Tracking_Facets_Include_Field.StoreIds,
		enabled: filterKey === 'store_ids',
	});

	const {data: shippingMethodFacets} = useTrackingFacetV2({
		...queryParams,
		key: 'shippingMethods',
		includeField: Tracking_Facets_Include_Field.ShippingMethod,
		enabled: filterKey === 'shipping_method',
	});
	const getSelectOptions = useCallback(arr => {
		return arr?.map((facet: any) => ({
			key: getCharCode(facet.label),
			label: facet.label,
			value: numberWithCommas(facet.value),
		}));
	}, []);
	const orderTagOptions = useMemo(() => {
		return getSelectOptions(orderTagFacets);
	}, [orderTagFacets]);

	const storeOptions = useMemo(() => {
		return getSelectOptions(storeIdsFacets);
	}, [storeIdsFacets]);

	const shippingMethodOptions = useMemo(() => {
		return getSelectOptions(shippingMethodFacets);
	}, [shippingMethodFacets]);

	switch (filterKey) {
		case 'order_tags':
			return orderTagOptions;
		case 'status':
			return [
				// - Info received
				// - In transit
				// - Out for delivery
				// - Available for pickup
				// - Delivered
				// - Exception
				// - Failed attempt
				// - Expired
				// - Pending

				{
					key: getCharCode('Info received'),
					label: 'Info received',
					value: TrackingStatus.InfoReceived,
				},
				{
					key: getCharCode('In transit'),
					label: 'In transit',
					value: TrackingStatus.InTransit,
				},
				{
					key: getCharCode('Out for delivery'),
					label: 'Out for delivery',
					value: TrackingStatus.OutForDelivery,
				},
				{
					key: getCharCode('Available for pickup'),
					label: 'Available for pickup',
					value: TrackingStatus.AvailableForPickup,
				},
				{
					key: getCharCode('Delivered'),
					label: 'Delivered',
					value: TrackingStatus.Delivered,
				},
				{
					key: getCharCode('Exception'),
					label: 'Exception',
					value: TrackingStatus.Exception,
				},
				{
					key: getCharCode('Failed attempt'),
					label: 'Failed attempt',
					value: TrackingStatus.AttemptFail,
				},
				{
					key: getCharCode('Expired'),
					label: 'Expired',
					value: TrackingStatus.Expired,
				},
				{
					key: getCharCode('Pending'),
					label: 'Pending',
					value: TrackingStatus.Pending,
				},
			];
		case 'store_ids':
			return storeOptions;
		case 'shipping_method':
			return shippingMethodOptions;
		default:
			return [];
	}
};
