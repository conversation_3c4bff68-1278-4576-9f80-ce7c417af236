/* eslint-disable no-nested-ternary */
import {cloneDeep, findIndex, isArray, isEmpty, isObject, set} from 'lodash';

import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {GetTrackingsProps} from 'pages/Shipments/interfaces';
import {BasicFilterData, Operator} from 'pages/Shipments/interfaces/Filter';

import {statusLabelKeyMap} from '../constants';

export const useMoreFilters = ({
	query,
	targetPath,
	propertyKey,
	onChangeQuery,
	isSubscription,
}: {
	query:
		| ReturnType<typeof transformTrackingAnalyticsQuery>['query']
		| GetTrackingsProps;
	targetPath: string;
	propertyKey: string | string[];
	isSubscription?: boolean;
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
}) => {
	const formatQueryFilterV2 = () => {
		if (isEmpty(query?.filterV2)) {
			return {
				basicFilter: [],
				combineFilter: [],
				customFilter: [],
			};
		}
		if (isObject(query?.filterV2)) {
			return query?.filterV2;
		}
		return JSON.parse(query.filterV2 as string);
	};

	const advanceFilter = formatQueryFilterV2();

	const defaultAdvanceFilterItem = {
		propertyName: '',
		operator: Operator.IS_ANY_OF,
		values: [],
	};
	const clonedAdvanceFilter = cloneDeep(formatQueryFilterV2());

	const advanceFilterItem = advanceFilter?.[targetPath] || [];

	const clonedAdvanceFilterItem = clonedAdvanceFilter?.[targetPath];

	const advanceFilterItemForPropertyKey = advanceFilterItem.filter(
		(item: BasicFilterData) => {
			return isArray(propertyKey)
				? propertyKey?.includes(item?.propertyName)
				: [propertyKey].includes(item?.propertyName);
		}
	);
	const deleteTargetTypeAdvanceFilterItem = (propertyLabel: string) => {
		const currentItemIndex = findIndex(
			advanceFilterItem,
			(item: BasicFilterData) => item?.propertyName === propertyLabel
		);

		clonedAdvanceFilter?.[targetPath]?.splice(currentItemIndex, 1);

		onChangeQuery({
			filterV2: isSubscription
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const clearTargetTypeAdvanceFilter = () => {
		set(clonedAdvanceFilter, targetPath, []);

		onChangeQuery({
			filterV2: isSubscription
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const createTargetTypeAdvanceFilter = ({
		operator,
		propertyKeyParam,
	}: {
		propertyLabel: string;
		propertyKeyParam: string;
		operator: Operator;
	}) => {
		clonedAdvanceFilterItem?.push({
			propertyName: `${propertyKeyParam || propertyKey}`,
			operator: operator,
		});

		onChangeQuery({
			filterV2: isSubscription
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const getInitialAdvanceFilterItem = (propertyLabel: string) => {
		if (propertyLabel === 'status') {
			const formattedResult = cloneDeep(
				advanceFilter[targetPath].filter((item: BasicFilterData) => {
					return item?.propertyName === propertyLabel;
				})?.[0] || defaultAdvanceFilterItem
			);
			set(
				formattedResult,
				'values',
				formattedResult.values?.map((item: any) => {
					return item?.status
						? statusLabelKeyMap[item?.status]
						: item;
				})
			);
			return formattedResult;
		}
		return (
			advanceFilter[targetPath].filter((item: BasicFilterData) => {
				return item?.propertyName === propertyLabel;
			})?.[0] || defaultAdvanceFilterItem
		);
	};

	const changeAdvanceFilterItemKeyValue = ({
		propertyLabel,
		currentOperator,
		currentValues,
	}: {
		propertyLabel: string;
		currentOperator?: Operator;
		currentValues?: string[];
	}) => {
		const currentItemIndex = findIndex(
			advanceFilterItem,
			(item: BasicFilterData) => item?.propertyName === propertyLabel
		);

		if (currentItemIndex !== -1) {
			if (currentOperator) {
				clonedAdvanceFilterItem[currentItemIndex].operator =
					currentOperator;
			}
			if (currentValues) {
				clonedAdvanceFilterItem[currentItemIndex].values =
					currentValues;
			}
		}

		onChangeQuery({
			filterV2: isSubscription
				? clonedAdvanceFilter
				: JSON.stringify(clonedAdvanceFilter),
		});
	};

	const getResultValues = (
		propertyLabel: string,
		currentHandleOptionValue: string
	) => {
		if (currentHandleOptionValue) {
			let resultChoices = [];
			const currentValues =
				getInitialAdvanceFilterItem(propertyLabel).values || [];

			resultChoices = !currentValues?.includes(currentHandleOptionValue)
				? [...currentValues, currentHandleOptionValue]
				: currentValues?.filter((item: string) => {
						return item !== currentHandleOptionValue;
				  });
			return resultChoices;
		}
		return [];
	};

	return {
		advanceFilterItem,
		advanceFilterItemForPropertyKey,
		getResultValues,
		deleteTargetTypeAdvanceFilterItem,
		clearTargetTypeAdvanceFilter,
		createTargetTypeAdvanceFilter,
		getInitialAdvanceFilterItem,
		changeAdvanceFilterItemKeyValue,
	};
};
