import {TrackingStatus} from '@graphql/generated';

export const moreFilterKeysNameMap: Record<string, string> = {
	status: 'Status',
	order_tags: 'Shopify tags',
	store_ids: 'Store',
	shipping_method: 'Shipping method',
};

export const moreFilterKeysToI18nMap: Record<string, string> = {
	status: 'STATUS_ec53a',
	order_tags: 'SHOPIFYTAG_b95d0',
	store_ids: 'STORE_2_f235f',
	shipping_method: 'SHIPPING_M_8eae4',
};

export const labelStatusKeyMap: Record<string, string> = {
	'Info received': TrackingStatus.InfoReceived,
	'In transit': TrackingStatus.InTransit,
	'Out for delivery': TrackingStatus.OutForDelivery,
	'Available for pickup': TrackingStatus.AvailableForPickup,
	Delivered: TrackingStatus.Delivered,
	Exception: TrackingStatus.Exception,
	'Failed attempt': TrackingStatus.AttemptFail,
	Expired: TrackingStatus.Expired,
	Pending: TrackingStatus.Pending,
};

export const statusLabelKeyMap: Record<string, string> = {
	InfoReceived: 'Info received',
	InTransit: 'In transit',
	OutForDelivery: 'Out for delivery',
	AvailableForPickup: 'Available for pickup',
	Delivered: 'Delivered',
	Exception: 'Exception',
	AttemptFail: 'Failed attempt',
	Expired: 'Expired',
	Pending: 'Pending',
};

export const defaultMergedNoValueDefaultFilter: Record<string, any> = {
	status: {
		propertyName: 'status',
		operator: 'is_none_of',
		values: [
			{
				status: 'InfoReceived',
			},
			{
				status: 'InTransit',
			},
			{
				status: 'OutForDelivery',
			},
			{
				status: 'AvailableForPickup',
			},
			{
				status: 'Delivered',
			},
			{
				status: 'Exception',
			},
			{
				status: 'AttemptFail',
			},
			{
				status: 'Expired',
			},
			{
				status: 'Pending',
			},
		],
	},
};
