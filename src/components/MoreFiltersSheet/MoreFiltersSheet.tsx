/* eslint-disable jsx-a11y/no-static-element-interactions */
import {<PERSON>ton, Scrollable, Sheet, TextStyle, Icon} from '@shopify/polaris';
import {ChevronLeftMinor, MobileCancelMajor} from '@shopify/polaris-icons';
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';

import {SHIPMENTS_REPORT} from 'constants/billings/features';
import {useFeatureValidator} from 'hooks/billings';
import {transformTrackingAnalyticsQuery} from 'pages/Dashboard/hooks';
import {GetTrackingsProps} from 'pages/Shipments/interfaces';
import {BasicFilterData} from 'pages/Shipments/interfaces/Filter';

import {FiltersSelect} from './components/FiltersSelect';
import {MoreFilterCard} from './components/MoreFilterCard';
import {moreFilterKeysNameMap} from './constants';
import {useMoreFilters} from './hooks/useMoreFilters';
import style from './index.module.scss';

interface Props {
	// TODO:有空将下面两个参数合并为type
	isSubscription?: boolean;
	open: boolean;
	title: string;
	onClose: () => void;
	query:
		| ReturnType<typeof transformTrackingAnalyticsQuery>['query']
		| GetTrackingsProps
		| any;
	onChangeQuery: (newQuery: Record<string, string | undefined>) => void;
	filter: any;
}
const MoreFiltersSheet = ({
	isSubscription,
	open,
	title,
	onClose,
	query,
	onChangeQuery,
	filter,
}: Props) => {
	const {t} = useTranslation();

	const [, available] = useFeatureValidator(SHIPMENTS_REPORT, () => {});

	const {advanceFilterItem} = useMoreFilters({
		isSubscription: isSubscription,
		query: query,
		onChangeQuery: onChangeQuery,
		targetPath: 'basicFilter',
		propertyKey: '',
	});
	const [enabledFiltersList, setEnabledFiltersList] = useState<string[]>(
		advanceFilterItem
			?.map((item: BasicFilterData) => {
				if (moreFilterKeysNameMap[item?.propertyName]) {
					return item?.propertyName;
				}
				return undefined;
			})
			.filter((item: string | undefined) => item !== undefined)
	);

	return (
		<Sheet open={open} onClose={onClose} accessibilityLabel="">
			<div
				style={{
					alignItems: 'center',
					borderBottom: '1px solid #E1E3E5',
					display: 'flex',
					justifyContent: 'space-between',
					padding: '16px 20px',
					fontSize: '16px',
				}}
			>
				<div style={{display: 'flex'}}>
					{isSubscription && (
						<div
							onClick={onClose}
							style={{cursor: 'pointer', marginRight: '4px'}}
						>
							<Icon source={ChevronLeftMinor} />
						</div>
					)}
					<div style={{marginRight: '4px'}}>
						<TextStyle variation="strong">{title}</TextStyle>
					</div>
				</div>
				<Button
					accessibilityLabel="Cancel"
					icon={MobileCancelMajor}
					onClick={onClose}
					plain
				/>
			</div>
			<div className={style.scrollable}>
				{enabledFiltersList?.length ? (
					<Scrollable style={{padding: '16px 20px', height: '100%'}}>
						{enabledFiltersList?.map(label => {
							return (
								<div key={label}>
									<MoreFilterCard
										isSubscription={isSubscription}
										filterLabel={label}
										query={query}
										onChangeQuery={onChangeQuery}
										enabledCustomFieldsList={
											enabledFiltersList
										}
										setEnabledCustomFieldsList={
											setEnabledFiltersList
										}
										defaultOperator
										filter={filter}
									/>
								</div>
							);
						})}
					</Scrollable>
				) : (
					<div
						style={{
							width: '100%',
							height: '100%',
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center',
							flexDirection: 'column',
							padding: '20px',
							textAlign: 'center',
						}}
					>
						<TextStyle>
							{t('NOFILTERAD_962a9', 'No filter added yet')}
						</TextStyle>
					</div>
				)}
			</div>
			{/* 在shipment没有custom */}
			{
				// 没有权限并且没有已有的custom
				!available && !enabledFiltersList.length ? (
					<></>
				) : (
					<div
						style={{
							borderTop: '1px solid #E1E3E5',
							padding: '16px 20px',
						}}
					>
						<FiltersSelect
							isSubscription={isSubscription}
							query={query}
							onChangeQuery={onChangeQuery}
							enabledFiltersList={enabledFiltersList}
							setEnabledFiltersList={setEnabledFiltersList}
						/>
					</div>
				)
			}
		</Sheet>
	);
};
export default MoreFiltersSheet;
