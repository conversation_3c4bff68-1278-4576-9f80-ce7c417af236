import React from 'react';
import PropTypes from 'prop-types';
import {Stack, Button} from '@shopify/polaris';
import ReactDOM from 'react-dom';
import history from 'utils/history';
import {useChoosePlan} from 'hooks/billings/useChoosePlan';

import './bottomFixedBanner.css';

const BottomButton = ({
	url,
	buttonName,
	isPrimary,
	pushEvent,
	needUpgrade,
	onClick,
	loading,
}) => {
	const {setOpen} = useChoosePlan();

	const handleClick = () => {
		if (needUpgrade) {
			setOpen();
		} else if (/^((https?):\/\/)/.test(url)) {
			window.open(url, '_blank', 'noopener');
		} else {
			history.push(url);
		}

		onClick();

		pushEvent();
	};

	return (
		url && (
			<Stack.Item>
				<Button
					loading={loading}
					onClick={handleClick}
					primary={isPrimary}
				>
					<span style={{whiteSpace: 'nowrap'}}>{buttonName}</span>
				</Button>
			</Stack.Item>
		)
	);
};

class BottomFixedBanner extends React.PureComponent {
	static propTypes = {
		content: PropTypes.any,
		status: PropTypes.string,
		buttonName: PropTypes.string,
		isPrimary: PropTypes.bool,
		needUpgrade: PropTypes.bool,
		url: PropTypes.string,
		onClick: PropTypes.func,
		pushEvent: PropTypes.func.isRequired,
		pushEventProps: PropTypes.shape({
			event: PropTypes.string,
		}),
	};

	static defaultProps = {
		content: '',
		buttonName: '',
		isPrimary: true,
		status: '',
		url: '',
		needUpgrade: false,
		pushEventProps: undefined,
		onClick: () => {},
	};

	componentDidMount() {
		document.body.classList.add('has-fixed-bottom-banner');
	}

	componentWillUnmount() {
		document.body.classList.remove('has-fixed-bottom-banner');
	}

	pushEvent = () => {
		// `url` could be an external link

		this.props.pushEventProps &&
			this.props.pushEvent(
				this.props.pushEventProps,
				'defaultUserInformation',
				['pageType']
			);
	};

	render() {
		const {
			content,
			url,
			buttonName,
			needUpgrade,
			onClick,
			status,
			isPrimary,
			loading,
			hasButton = false,
		} = this.props;
		if (!content) {
			return null;
		}
		/* if user selected plan and click [choose plan] button , /pricing/tracking will navigate to /pricing/tracking/checkout */
		if (history.location.pathname.includes('/pricing/tracking')) {
			return null;
		}
		if (history.location.pathname === '/settings/billing/unpaid-invoices') {
			return null;
		}
		const portal = document.getElementById('bottom-portal');
		const className = status
			? `c-bottom-fixed-banner ${status}`
			: 'c-bottom-fixed-banner';
		return (
			<section className={className}>
				<Stack spacing="loose" alignment="center">
					<Stack.Item>
						<div>{content}</div>
					</Stack.Item>
					{hasButton && (
						<BottomButton
							loading={loading}
							url={url}
							needUpgrade={needUpgrade}
							pushEvent={this.pushEvent}
							buttonName={buttonName}
							isPrimary={isPrimary}
							onClick={onClick}
						/>
					)}
				</Stack>
				{portal &&
					ReactDOM.createPortal(
						<div className="fixed-banner-portal" />,
						portal
					)}
			</section>
		);
	}
}

export default BottomFixedBanner;
