import {Text<PERSON>ty<PERSON>, Link, Button} from '@shopify/polaris';
import {CTA, useBookDemo} from 'AftershipNavigation';
import {
	useUnpaidInvoices,
	usePayUnpaidInvoices,
	FeeType,
	usePayment,
	PaymentMethodType,
	useIsPayWithShopify,
} from 'aftershipBillingUi';
import {push} from 'connected-react-router';
import qs from 'query-string';
import React from 'react';
import {Trans, useTranslation} from 'react-i18next';
import {useDispatch} from 'react-redux';
import {useLocation} from 'react-router';
import {useMount} from 'react-use';

import {pushEvent} from 'actions/googleTagManager';
import {addToast} from 'actions/toast';
import {
	useIsEnterprise,
	useIsTrialEnterprise,
} from 'hooks/billings/useChoosePlan';
import useShopifySubdomain from 'hooks/billings/useShopifySubdomain';
import {
	useSuspendedSubscription,
	useInvoicesTotal,
	useTrialSubscription,
} from 'hooks/billings/useSubscription';
import {to3rdPartyMoment, toOrgMoment} from 'utils/day';
import {gaClickFn, gaImpr, gaClick} from 'utils/gtag';

import BottomFixedBanner from './BottomFixedBanner';

const useTrialBanner = () => {
	const {t} = useTranslation();
	const location = useLocation();
	const {subscription: trialSubscription} = useTrialSubscription();
	const {openBookDemoModal, hasBookDemo} = useBookDemo(
		trialSubscription
			? {
					featureOfInterest: 'Trial bottom banner',
			  }
			: undefined
	);
	const choosePlanURL = qs.stringifyUrl({
		url: '/pricing/tracking',
		query: {
			promotion_type: 'subscribe',
			promotion_id: 'bottom_banner',
			path_from: location.pathname,
		},
	});

	useMount(() => {
		gaImpr('E10209');
	});
	if (trialSubscription) {
		const leftDays = Math.round(
			toOrgMoment(trialSubscription?.currentPeriod.endAt).diff(
				toOrgMoment(),
				'days',
				true
			)
		);
		const content = (
			<>
				<div
					style={{
						display: 'flex',
						justifyContent: 'center',
						alignItems: 'center',
					}}
				>
					<div>{t('LEFT_DAYS_2342', {leftDays})}</div>
					<div style={{marginLeft: '24px', flexShrink: 0}}>
						<span style={{marginRight: '16px'}}>
							<Button
								primary
								url={choosePlanURL}
								onClick={() => {
									gaClick('E10210');
								}}
							>
								{t('COMPARE_PLANS_23424')}
							</Button>
						</span>
						{hasBookDemo && (
							<span style={{marginRight: '16px'}}>
								<Button
									outline
									onClick={() => {
										openBookDemoModal(
											CTA.BOTTOM_BANNER,
											'Trial bottom banner'
										);
									}}
								>
									{
										(
											<span
												style={{
													color: 'white',
												}}
											>
												{t('CONTACT_is91jf')}
											</span>
										) as any
									}
								</Button>{' '}
							</span>
						)}
					</div>
				</div>
			</>
		);
		return {
			content: content,
			onClick: gaClickFn('E00021'),
			pushEventProps: {
				event: 'subscribeIntention',
				pagePosition: 'bottom',
			},
		};
	}
	return null;
};
const useTrialEnterpriseBanner = () => {
	const {t} = useTranslation();
	const {subscription: trialSubscription} = useTrialSubscription();
	const {openBookDemoModal} = useBookDemo(
		trialSubscription
			? {
					featureOfInterest: 'Trial bottom banner',
			  }
			: undefined
	);
	if (trialSubscription) {
		const leftDays = Math.round(
			toOrgMoment(trialSubscription?.scheduleCancelAt).diff(
				toOrgMoment(),
				'days',
				true
			)
		);
		const content = (
			<>
				<div
					style={{
						display: 'flex',
						justifyContent: 'center',
						alignItems: 'center',
					}}
				>
					<div>{t('YOU_ONLY_234', {leftDays})}</div>
					<div style={{marginLeft: '24px', flexShrink: 0}}>
						<span style={{marginRight: '16px'}}>
							<Button
								primary
								onClick={() => {
									openBookDemoModal(
										CTA.BOTTOM_BANNER,
										'Trial bottom banner'
									);
								}}
							>
								{
									(
										<span
											style={{
												color: 'white',
											}}
										>
											{t('CONTACT_SALES_1231')}
										</span>
									) as unknown as string
								}
							</Button>{' '}
						</span>
					</div>
				</div>
			</>
		);
		return {content};
	}
	return null;
};

const useWillExpireBanner = () => {
	const {t} = useTranslation();
	const {paymentMethod, status} = usePayment();
	const isAlmostExpired = status === 'expiredSoon';
	if (paymentMethod?.type === PaymentMethodType.card && isAlmostExpired) {
		return {
			content: t('CREDIT_CARD_23424'),
			buttonName: t('UPDATE_2342342'),
			url: '/settings/billing/payment-method',
		};
	}
	return null;
};

const useDidExpireBanner = () => {
	const {t} = useTranslation();
	const {paymentMethod, status} = usePayment();
	if (
		paymentMethod?.type === PaymentMethodType.card &&
		status === 'expired'
	) {
		return {
			content: t('CREDIT_CARD_2098'),
			buttonName: t('UPDATE_2342342'),
			url: '/settings/billing/payment-method',
		};
	}
	return null;
};

const useInvoicesBanner = () => {
	const {t} = useTranslation();
	const {unpaidInvoices, loading} = useUnpaidInvoices();
	const {paymentMethod} = usePayment();
	const {isPayWithShopify} = useIsPayWithShopify();
	const dispatch = useDispatch();
	const subdomain = useShopifySubdomain();
	const {payUnpaidInvoices, startCollectionCharge} = usePayUnpaidInvoices();

	const date = to3rdPartyMoment(unpaidInvoices[0]?.invoiceDate).add(15, 'd');
	const {subscription: suspendedSubscription} = useSuspendedSubscription();
	const total = useInvoicesTotal();

	// sub_fee_type
	const isMainPlan = unpaidInvoices.some(invoice =>
		invoice.items.some((item: any) => item.feeType === FeeType.subscription)
	);
	const isExtraFee = unpaidInvoices.some(invoice =>
		invoice.items.some((item: any) => item.feeType === FeeType.extraQuota)
	);
	const isAddOn = unpaidInvoices.some(invoice =>
		invoice.items.some((item: any) => item.feeType === FeeType.meter)
	);
	const isSuspended = Boolean(suspendedSubscription);
	const amountExceed = total.amountDue > 10000;
	let content = (
		<TextStyle>
			{t('PLEASE_PAY_98080', {date: date.format('MMMM D, YYYY')})}{' '}
			<Link url="/settings/billing/unpaid-invoices">
				<span
					style={{
						color: 'white',
						textDecoration: 'underline white',
					}}
				>
					{t('VIEW_INVOI_230982')}
				</span>
			</Link>
		</TextStyle>
	);

	if (toOrgMoment(date).isBefore(toOrgMoment('2021-07-01T00:00:00Z'))) {
		content = (
			<TextStyle>
				{t('PLEASE_PAY_0721')}
				<Link url="/settings/billing/unpaid-invoices">
					<span
						style={{
							color: 'white',
							textDecoration: 'underline white',
						}}
					>
						{t('VIEW_INVOI_230982')}
					</span>
				</Link>
			</TextStyle>
		);
	} else if (amountExceed) {
		content = (
			<TextStyle>
				{/** t('YOUR_UNPAID_098123') */}
				<Trans
					i18nKey="YOUR_UNPAID_098123"
					components={{
						button: (
							<Button
								plain
								onClick={() => {
									window.$crisp.push(['do', 'chat:open']);
								}}
							/>
						),
						span: (
							<span
								style={{
									color: 'white',
									textDecoration: 'underline white',
								}}
							/>
						),
					}}
				/>
			</TextStyle>
		);
	} else if (isMainPlan || isExtraFee) {
		content = (
			<TextStyle>
				{t('PLEASE_PAY_7711', {date: date.format('MMMM D, YYYY')})}{' '}
				<Link url="/settings/billing/unpaid-invoices">
					<span
						style={{
							color: 'white',
							textDecoration: 'underline white',
						}}
					>
						{t('VIEW_INVOI_230982')}
					</span>
				</Link>
			</TextStyle>
		);
	} else if (isAddOn) {
		if (isSuspended) {
			content = (
				<TextStyle>
					{t('PLEASE_PAY_7782342', {
						date: date.format('MMMM D, YYYY'),
					})}{' '}
					<Link url="/settings/billing/unpaid-invoices">
						<span
							style={{
								color: 'white',
								textDecoration: 'underline white',
							}}
						>
							{t('VIEW_INVOI_230982')}
						</span>
					</Link>
				</TextStyle>
			);
		} else {
			content = (
				<TextStyle>
					{t('PLEASE_PAY_77824')}{' '}
					<Link url="/settings/billing/unpaid-invoices">
						<span
							style={{
								color: 'white',
								textDecoration: 'underline white',
							}}
						>
							{t('VIEW_INVOI_230982')}
						</span>
					</Link>
				</TextStyle>
			);
		}
	}
	// @ts-ignore
	if (unpaidInvoices?.length && !loading.getUnpaidInvoices) {
		return {
			content: content,
			buttonName: t('PAY_NOW_123'),
			// @ts-ignore
			loading: loading.startCollectionCharge || loading.payUnpaidInvoices,
			isPrimary: false,
			hasButton: !amountExceed,
			url: '/settings/billing/payment-method',
			status: 'critical',
			onClick: () => {
				if (isPayWithShopify) {
					startCollectionCharge({
						subdomain,
					});
				} else if (paymentMethod) {
					payUnpaidInvoices();
				} else {
					dispatch(
						addToast({
							message: t('PLEASE_ADD_2342'),
						})
					);
				}
			},
		};
	}
	return null;
};

export const useBanner = () => {
	const isEnterprise = useIsEnterprise();
	const isTrialEnterprise = useIsTrialEnterprise();
	const trialEnterpriseBanner = useTrialEnterpriseBanner();
	const invoicesBanner = useInvoicesBanner();
	const didExpiredBanner = useDidExpireBanner();
	const willExpireBanner = useWillExpireBanner();
	const trialBanner = useTrialBanner();

	if (isTrialEnterprise) return trialEnterpriseBanner;
	return (
		!isEnterprise &&
		(invoicesBanner || didExpiredBanner || willExpireBanner || trialBanner)
	);
};

// FIXME: Useless methods, either remove these or modify
const methods = {
	pushEvent,
	push,
};

// TODO - need to reduce the frequency rerendering
export default () => {
	const bannerInfo = useBanner();

	return <BottomFixedBanner {...methods} {...bannerInfo} />;
};
