.c-bottom-fixed-banner {
    /* Polaris z-index reference: https://github.com/Shopify/polaris-react/blob/master/src/styles/foundation/z-index.scss */
    z-index: 512;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 72px;
    background-color: #000639;
    color: #fff;
    box-shadow: 0 0 0 1px rgba(63, 63, 68, 0.05),
        0 -1px 3px 0 rgba(63, 63, 68, 0.15);
    max-width: '99.8rem';
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all .4s;
}

.c-bottom-fixed-banner.critical {
    background: #d82c0d;
}

/* polaris left nav on desktop */
@media (min-width: 48.0625em) {
    .c-bottom-fixed-banner {
        left: calc(24rem + env(safe-area-inset-left) + var(--asnv-nav-collapse-offset));
    }
}

.c-bottom-fixed-banner .Polaris-Button--outline:hover {
    background-color: #000639;
    color: #fff;
}
