import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Badge, Tooltip} from '@shopify/polaris';
import Pip from 'components/Pip';

export default class BadgeWithTooltip extends PureComponent {
	static propTypes = {
		hasTooltip: PropTypes.bool,
		isException: PropTypes.bool,
		extraInfo: PropTypes.string,
		statusColor: PropTypes.string,
	};

	static defaultProps = {
		hasTooltip: false,
		isException: false,
		extraInfo: '',
		statusColor: '',
	};

	renderBadge = () => {
		const {children, statusColor, ...rest} = this.props;
		return (
			<Badge
				{...rest}
				status={children === 'Exception' ? 'warning' : null}
			>
				{statusColor ? <Pip color={statusColor} /> : null}
				{children}
			</Badge>
		);
	};

	render() {
		const {hasTooltip, extraInfo} = this.props;
		if (hasTooltip) {
			return (
				<Tooltip
					content={<div style={{maxWidth: '20rem'}}>{extraInfo}</div>}
					preferredPosition="below"
				>
					{this.renderBadge()}
				</Tooltip>
			);
		}
		return <div className="BadgeWithTooltip">{this.renderBadge()}</div>;
	}
}
