import React from 'react';
import {Provider} from 'react-redux';
import {mount} from 'enzyme';
import {AppProvider, Tooltip} from '@shopify/polaris';
import {format} from 'prettier';
import {merge as _merge} from 'lodash';
import configureStore from 'store/configureStore';
import {theme} from '../../TopBar';
import Badge from '../index';

window.matchMedia = jest.fn().mockImplementation(query => {
	return {
		matches: false,
		media: query,
		onchange: null,
		addListener: jest.fn(),
		removeListener: jest.fn(),
	};
});

const mountApp = (rootProps = {}, props = {}) => {
	const store = configureStore(
		_merge(configureStore().getState(), {
			...rootProps,
		})
	);

	return mount(
		<AppProvider
			theme={theme}
			i18n={{
				Polaris: {
					ResourceList: {
						allItemsSelected:
							'All {itemsLength}+ {resourceNamePlural} are selected.',
						selectAllItems:
							'Select all {itemsLength}+ {resourceNamePlural}',
						emptySearchResultDescription:
							'Try changing the filters or search terms',
					},
				},
			}}
		>
			<Provider store={store}>
				<Badge {...props} />
			</Provider>
		</AppProvider>
	);
};

describe('[ASADMIN-2488] fill coverage', () => {
	describe('with tooltip', () => {
		it('should render tooltip', () => {
			const app = mountApp({}, {hasTooltip: true});
			expect(app.exists(Tooltip)).toEqual(true);
			expect(format(app.html(), {parser: 'html'})).toMatchSnapshot();
		});
	});

	describe('no tooltip', () => {
		it('should not show tooltip', () => {
			const app = mountApp();
			expect(app.exists(Tooltip)).toEqual(false);
			expect(format(app.html(), {parser: 'html'})).toMatchSnapshot();
		});
	});

	describe('badge with colorful pip', () => {
		it('should display a color pip', () => {
			const app = mountApp({}, {statusColor: '#4baa67'});
			expect(format(app.html(), {parser: 'html'})).toMatchSnapshot();
		});
	});
});
