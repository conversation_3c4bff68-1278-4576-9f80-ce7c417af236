import {Stack, Button, ComplexAction} from '@shopify/polaris';
import React from 'react';

import './fullScreenWorkshop.scss';

export interface OwnProps {
	editor: React.ReactNode;
	preview?: React.ReactNode;
	title?: React.ReactNode;
	primaryAction: ComplexAction;
	secondaryAction: ComplexAction;
	savebarContent?: React.ReactNode;
}

export default function FullScreenWorkshop(props: OwnProps): JSX.Element {
	return (
		<div>
			<header className="workshop-savebar Polaris-Frame-ContextualSaveBar">
				<h1 className="Polaris-Frame-ContextualSaveBar__LogoContainer">
					{props.title}
				</h1>
				<div className="Polaris-Frame-ContextualSaveBar__Contents">
					<div className="workshop-savebar-content">
						{props.savebarContent || null}
					</div>
					<Stack spacing="tight" wrap={false} alignment="center">
						<Button
							onClick={props.secondaryAction.onAction}
							{...props.secondaryAction}
						>
							{props.secondaryAction.content}
						</Button>
						<Button
							primary
							onClick={props.primaryAction.onAction}
							{...props.primaryAction}
						>
							{props.primaryAction.content}
						</Button>
					</Stack>
				</div>
			</header>

			<section className="fullscreen-workshop">
				<div className="workshop-main">
					<div className="workshop-editor">{props.editor}</div>
					<div className="workshop-preview">{props.preview}</div>
				</div>
			</section>
		</div>
	);
}
