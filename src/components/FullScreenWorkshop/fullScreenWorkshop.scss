$backgroundColor: #f4f6f8;

$editorDesktopWidth: 400px;
$topbarHeight: 60px;

$mobileViewBreakpoint: 769px;

$zIndexFullscreenWorkshop: 30;

.workshop-savebar {
	height: $topbarHeight;
	position: fixed;
	z-index: 100;
	top: 0;
	left: 0;
	width: 100%;
	background: #fff;
	display: flex;
	> h1 {
		width: $editorDesktopWidth;
		flex: 0 0 auto;
		display: flex;
	}
	.Polaris-Frame-ContextualSaveBar__Contents {
		margin: 0;
		max-width: 100%;
	}
}

.fullscreen-workshop {
	position: absolute;
	left: 0;
	top: 0;
	z-index: $zIndexFullscreenWorkshop; // above the left side nav bar
	background: $backgroundColor;
	width: 100%;
	height: 100%;
	display: flex;
	.Polaris-Frame & {
		padding-top: $topbarHeight;
	}
}

.workshop-main {
	display: flex;
	width: 100%;
	height: 100%;
}

.workshop-editor {
	width: $editorDesktopWidth;
	z-index: 1; // topper of the preview
	height: 100%;
	overflow: auto;
}
.workshop-preview {
	background-color: #dfe3e8;
	flex: 1;
	overflow: auto;
}

// polaris's mobile view break point
@media (max-width: $mobileViewBreakpoint) {
	.workshop-editor {
		max-width: unset;
		width: 100%;
	}
	.workshop-preview {
		display: none;
	}
	.workshop-savebar {
		> h1 {
			width: auto;
			padding: 0 1.6rem;
			max-width: 46%;
		}
	}

	.workshop-savebar-content {
		display: none;
	}
}
