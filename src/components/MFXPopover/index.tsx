import {Popover, PopoverProps} from '@shopify/polaris';
import React, {useState} from 'react';

interface MFXPopoverProps extends Omit<PopoverProps, 'onClose' | 'active'> {
	className?: string;
}

export const MFXPopover = ({
	className,
	children,
	...popoverProps
}: MFXPopoverProps) => {
	const [show, setShow] = useState(false);
	const closePopover = () => setShow(false);

	return (
		<div
			className={className}
			onMouseMove={() => setShow(true)}
			onMouseLeave={() => setShow(false)}
			role="button"
			tabIndex={0}
		>
			<Popover {...popoverProps} active={show} onClose={closePopover}>
				<div
					style={{padding: '0.5rem 1rem'}}
					onMouseLeave={() => setShow(false)}
				>
					{children}
				</div>
			</Popover>
		</div>
	);
};
