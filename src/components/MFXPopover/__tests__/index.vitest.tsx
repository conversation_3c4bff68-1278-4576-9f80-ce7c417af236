import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import React from 'react';
import {beforeEach, describe, expect, test} from 'vitest';

import {MockAppProvider} from 'tests';

import {MFXPopover} from '..';

describe('MFXPopover', () => {
	beforeEach(() => {
		render(
			<MockAppProvider>
				<MFXPopover
					activator={
						<div data-testid="mfx-popover-desc">
							MFXPopover description
						</div>
					}
					preferredPosition="below"
					preferredAlignment="left"
				>
					<div data-testid="mfx-popover-content">
						MFXPopover content
					</div>
				</MFXPopover>
			</MockAppProvider>
		);
	});

	test('Should be shown or hidden after mouse operation', async () => {
		expect(screen.getByTestId('mfx-popover-desc')).toBeDefined();
		expect(
			screen.queryByText(/MFXPopover content/i)
		).not.toBeInTheDocument();

		const container = screen.getByTestId('mfx-popover-desc');
		fireEvent.mouseMove(container);
		expect(screen.getByTestId('mfx-popover-content')).toBeInTheDocument();

		fireEvent.mouseLeave(container);
		await waitFor(() => {
			expect(
				screen.queryByText(/MFXPopover content/i)
			).not.toBeInTheDocument();
		});
	});
});
