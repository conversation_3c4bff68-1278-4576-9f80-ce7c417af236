import {ResourceList, ResourceListProps} from '@shopify/polaris';
import React, {useRef, useEffect} from 'react';
import {useIntersection} from 'react-use';

const LoadMore: React.FC<{onReach: VoidFunction}> = ({onReach, children}) => {
	const ref = useRef(null);

	const intersection = useIntersection(ref, {
		root: null,
		rootMargin: '0px',
		threshold: 1,
	});

	useEffect(() => {
		if (intersection && intersection.intersectionRatio >= 1) onReach();
	}, [intersection, onReach]);

	return <div ref={ref}>{children}</div>;
};

type Props<T = any> = ResourceListProps<T> & {
	fetchNextPage: VoidFunction;
	hasNextPage?: boolean;
};

const InfiniteResourceList: React.FC<Props> = ({
	hasNextPage,
	fetchNextPage,
	items,
	renderItem,
	...props
}) => (
	<ResourceList
		{...props}
		items={items}
		renderItem={(item, id, index) => {
			if (hasNextPage && index === items.length - 1) {
				return (
					<LoadMore onReach={fetchNextPage}>
						{renderItem(item, id, index)}
					</LoadMore>
				);
			}

			return renderItem(item, id, index);
		}}
	/>
);

export default InfiniteResourceList;
