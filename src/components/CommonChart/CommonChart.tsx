import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>O<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>Option,
	Funnel<PERSON>eriesOption,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	PieSeriesOption,
} from 'echarts/charts';
import {
	DatasetComponent,
	DatasetComponentOption,
	GridComponent,
	GridComponentOption,
	LegendComponent,
	TitleComponentOption,
	TooltipComponent,
	TooltipComponentOption,
	LegendComponentOption,
	ToolboxComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import {UniversalTransition} from 'echarts/features';
import {CanvasRenderer} from 'echarts/renderers';
import React, {CSSProperties, useEffect, useRef} from 'react';

import {
	defaultLegendConfig,
	defaultTextStyleConfig,
	defaultTooltipConfig,
	themeConfig,
} from 'utils/chartConfig';

echarts.use([
	CanvasRenderer,
	DatasetComponent,
	GridComponent,
	LegendComponent,
	TooltipComponent,
	UniversalTransition,
	Bar<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>po<PERSON>,
	<PERSON><PERSON><PERSON>,
]);

export type ChartOption = echarts.ComposeOption<
	| BarSeriesOption
	| LineSeriesOption
	| TitleComponentOption
	| TooltipComponentOption
	| GridComponentOption
	| DatasetComponentOption
	| LegendComponentOption
	| FunnelSeriesOption
	| PieSeriesOption
>;
interface Props {
	chartOptions: ChartOption;
	className?: string;
	styles?: CSSProperties;
	onChange?: (chart: echarts.ECharts) => void;
	onClick?: (params: Record<string, unknown>, chart: echarts.ECharts) => void;
}
const CommonChart: React.FC<Props> = props => {
	const container = useRef<HTMLDivElement>(null);
	const chart = useRef<echarts.ECharts>();

	useEffect(() => {
		if (container.current && !chart.current) {
			chart.current = echarts.init(container.current, themeConfig);
			chart.current.on('click', evt => {
				if (chart.current && props.onClick) {
					props.onClick(evt, chart.current);
				}
			});
		}
		if (chart.current && props.chartOptions) {
			const composeOptions = props.chartOptions;
			composeOptions.tooltip = {
				...defaultTooltipConfig,
				...composeOptions.tooltip,
			};
			composeOptions.legend = {
				...defaultLegendConfig,
				...composeOptions.legend,
			};
			composeOptions.textStyle = {
				...defaultTextStyleConfig,
				...(composeOptions.textStyle as any),
			};

			setTimeout(
				() => chart.current && chart.current.setOption(composeOptions),
				0
			);
		}
		if (props.onChange && chart.current) {
			props.onChange(chart.current);
		}
	}, [props]);

	useEffect(() => {
		const resize = () => {
			chart.current?.resize();
			if (props.onChange && chart.current) {
				props.onChange(chart.current);
			}
		};
		window.addEventListener('resize', resize);
		requestAnimationFrame(function checkIfContainerHasWidth() {
			if (container.current?.getBoundingClientRect().width) {
				resize();
			} else {
				requestAnimationFrame(checkIfContainerHasWidth);
			}
		});
		return () => {
			window.removeEventListener('resize', resize);
		};
	});

	return (
		<div
			style={{width: '100%', height: '100%', ...props.styles}}
			className={props.className}
			ref={container}
		/>
	);
};

export default CommonChart;
