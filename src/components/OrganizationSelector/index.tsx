import {
	<PERSON><PERSON>,
	<PERSON>box,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>ield,
} from '@shopify/polaris';
import {SearchMinor} from '@shopify/polaris-icons';
import React, {useCallback, useMemo, useRef, useState} from 'react';
import {useVirtual} from 'react-virtual';

import {useAllCompanyOrganizationsQuery} from 'services/company';
import isCompany from 'utils/isCompany';

import styles from './index.module.css';

type Props = {
	selected: string[];
	onChange: (ids: string[]) => void;
};

const OrganizationSelector: React.FC<Props> = ({selected, onChange}) => {
	const [keyword, setKeyword] = useState('');

	const {data: allOrganizations = [], isLoading: isLoadingOrganizations} =
		useAllCompanyOrganizationsQuery({
			enabled: isCompany(),
			select: data => {
				return data.sort((a, b) => a.name.localeCompare(b.name));
			},
		});
	const isSelectAll = allOrganizations.length === selected.length;
	const organizations = useMemo(() => {
		const tKeyword = keyword?.toLocaleLowerCase();
		return tKeyword
			? allOrganizations.filter(
					organization =>
						organization?.name
							?.toLocaleLowerCase()
							.includes(tKeyword) ||
						organization?.shortName
							?.toLocaleLowerCase()
							.includes(tKeyword)
			  )
			: allOrganizations;
	}, [keyword, allOrganizations]);

	const parentRef = useRef<HTMLDivElement>(null);

	const virtualizer = useVirtual({
		size: organizations.length,
		estimateSize: useCallback(() => 28, []),
		parentRef,
		overscan: 5,
	});

	const unselect = useCallback(
		(id: string) => {
			onChange(selected.filter(x => x !== id));
		},
		[selected, onChange]
	);

	const select = useCallback(
		(id: string) => {
			onChange([...selected, id]);
		},
		[selected, onChange]
	);

	return (
		<>
			<TextField
				autoComplete="off"
				label="keyword"
				prefix={<Icon source={SearchMinor} color="base" />}
				placeholder="Search by organization"
				value={keyword}
				onChange={setKeyword}
				labelHidden
			/>

			<Stack distribution="equalSpacing" alignment="center">
				<div className={styles.label}>
					All organizations ({organizations.length})
				</div>
				<Button
					plain
					onClick={() => {
						onChange(
							isSelectAll
								? []
								: allOrganizations.map(org => org.id)
						);
					}}
				>
					{isSelectAll ? 'Unselect all' : 'Select all'}
				</Button>
			</Stack>

			<div
				ref={parentRef}
				style={{
					height: Math.min(organizations.length * 28, 200) + 'px',
					overflow: 'auto',
					width: '330px',
				}}
			>
				<div
					style={{
						height: `${virtualizer.totalSize}px`,
						width: '100%',
						position: 'relative',
					}}
				>
					{isLoadingOrganizations && (
						<div style={{textAlign: 'center', margin: '20px'}}>
							<Spinner />
						</div>
					)}

					{virtualizer.virtualItems.map(virtualRow => {
						const organization = organizations[virtualRow.index];
						const checked = selected.includes(organization.id);

						return (
							<div
								key={organization.id}
								className={styles.checkboxWrapper}
								style={{
									height: `${virtualRow.size}px`,
									transform: `translateY(${virtualRow.start}px)`,
								}}
							>
								<Checkbox
									label={organization.name}
									checked={checked}
									onChange={newChecked => {
										if (!checked && newChecked) {
											select(organization.id);
										} else if (checked && !newChecked) {
											unselect(organization.id);
										}
									}}
								/>
							</div>
						);
					})}
				</div>
			</div>
		</>
	);
};

export default OrganizationSelector;
