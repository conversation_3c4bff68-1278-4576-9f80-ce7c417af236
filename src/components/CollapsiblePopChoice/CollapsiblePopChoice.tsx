import {
	Stack,
	Checkbox,
	Popover,
	Icon,
	TextStyle,
	Collapsible,
	Link,
	TextField,
	Button,
} from '@shopify/polaris';
import {CaretDownMinor, SearchMinor} from '@shopify/polaris-icons';
import React, {ReactElement, useState, useMemo} from 'react';

export interface Option {
	value: string;
	label?: string;
	text?: string;
	children?: {
		value: string;
		label?: string;
		text?: string;
	}[];
}

function filterItem(item: Option, filter?: string): boolean {
	if (!filter) {
		return true;
	}
	if (item.value && item.value.toUpperCase().includes(filter.toUpperCase())) {
		return true;
	}
	if (item.label && item.label.toUpperCase().includes(filter.toUpperCase())) {
		return true;
	}
	if (item.children && item.children.length) {
		return item.children.some(child => filterItem(child, filter));
	}
	return false;
}

const CheckItem = (props: {
	item: Option;
	value: string[] | boolean;
	onChange: (values: boolean | string[]) => void;
	filter?: string;
	multiple?: boolean;
	hideSubOptions?: boolean;
}) => {
	const hasChildren = props.item.children?.length;
	const [expanded, setExpanded] = useState(false);
	let checked: boolean | 'indeterminate' = false;
	const multiChecked = (props.value as string[])?.length > 1;
	if (props.value) {
		if (hasChildren) {
			checked =
				(props.value as string[]).length === props.item.children?.length
					? true
					: 'indeterminate';
		} else {
			checked = true;
		}
	}
	const collapseButton = (
		<div
			style={{
				width: '2rem',
				height: '2rem',
				paddingTop: '0.4rem',
			}}
		>
			{!props.hideSubOptions && props.item.children?.length ? (
				<Link
					onClick={() => {
						if (!props.filter) {
							setExpanded(!expanded);
						}
					}}
				>
					<div
						style={{
							transform:
								expanded || props.filter
									? ''
									: 'rotate(-90deg)',
						}}
					>
						<Icon source={CaretDownMinor} />
					</div>
				</Link>
			) : null}
		</div>
	);
	return (
		<Stack spacing="none" wrap={false}>
			<Stack.Item fill>
				<Stack spacing="extraTight" wrap={false}>
					{collapseButton}
					<Stack.Item fill>
						<Stack vertical spacing="none">
							<Stack wrap={false}>
								<Stack.Item fill>
									<Checkbox
										checked={checked}
										label={
											props.item.label || props.item.value
										}
										onChange={checked => {
											if (checked) {
												if (hasChildren) {
													props.onChange(
														props.item.children?.map(
															child => child.value
														) || false
													);
												} else {
													props.onChange(true);
												}
											} else {
												props.onChange(false);
											}
										}}
									/>
								</Stack.Item>
								<div style={{paddingTop: '0.4rem'}}>
									<TextStyle variation="subdued">
										{props.item.text}
									</TextStyle>
								</div>
							</Stack>
							{props.item.children?.length &&
								!props.hideSubOptions && (
									<Collapsible
										id={props.item.value + '-list'}
										open={expanded || Boolean(props.filter)}
									>
										<div style={{paddingLeft: 26}}>
											<Stack vertical spacing="none">
												{props.item.children.map(
													child => {
														const filtered =
															!filterItem(
																child,
																props.filter
															);
														return (
															<div
																key={
																	child.value
																}
																style={{
																	display:
																		filtered
																			? 'none'
																			: 'block',
																}}
															>
																<Stack>
																	<Stack.Item
																		fill
																	>
																		<Checkbox
																			checked={(
																				(props.value as string[]) ||
																				[]
																			).includes(
																				child.value
																			)}
																			label={
																				child.label ||
																				child.value
																			}
																			onChange={checked => {
																				if (
																					checked
																				) {
																					props.onChange(
																						props.multiple
																							? [
																									...((props.value as string[]) ||
																										[]),
																									child.value,
																							  ]
																							: [
																									child.value,
																							  ]
																					);
																				} else {
																					const newValue =
																						multiChecked &&
																						!props.multiple
																							? [
																									child.value,
																							  ]
																							: (
																									props.value as string[]
																							  ).filter(
																									v =>
																										v !==
																										child.value
																							  );
																					props.onChange(
																						newValue.length
																							? newValue
																							: false
																					);
																				}
																			}}
																		/>
																	</Stack.Item>
																	<div
																		style={{
																			paddingTop:
																				'0.4rem',
																		}}
																	>
																		<TextStyle variation="subdued">
																			{
																				child.text
																			}
																		</TextStyle>
																	</div>
																</Stack>
															</div>
														);
													}
												)}
											</Stack>
										</div>
									</Collapsible>
								)}
						</Stack>
					</Stack.Item>
				</Stack>
			</Stack.Item>
		</Stack>
	);
};

const CollapsiblePopChoice = (props: {
	activator: ReactElement;
	active: boolean;
	options: Option[];
	hideSubOptions?: boolean;
	values: {value: string; children?: string[]}[];
	multiple?: boolean;
	searchable?: boolean;
	searchPlaceholder?: string;
	onChange: (values: {value: string; children?: string[]}[]) => void;
	onClose: () => void;
	onClear: () => void;
}) => {
	const [filter, setFilter] = useState('');
	const noChildren = props.options.every(
		o => !o.children || !o.children.length
	);
	const checkList = useMemo(() => {
		return props.options
			.map(option => {
				const [value] = props.values
					.filter(v => v.value === option.value)
					.map(v =>
						v.children && v.children.length ? v.children : true
					);
				return (
					<div
						key={option.value}
						style={{
							marginLeft:
								noChildren || props.hideSubOptions
									? '-2rem'
									: '0',
						}}
					>
						<CheckItem
							item={option}
							value={value}
							filter={filter}
							multiple={props.multiple}
							hideSubOptions={props.hideSubOptions}
							onChange={newValue => {
								if (newValue) {
									if (option.children?.length) {
										props.onChange(
											props.multiple
												? [
														...props.values.filter(
															v =>
																v.value !==
																option.value
														),
														{
															value: option.value,
															children:
																newValue as string[],
														},
												  ]
												: [
														{
															value: option.value,
															children:
																newValue as string[],
														},
												  ]
										);
									} else {
										props.onChange(
											props.multiple
												? [
														...props.values,
														{
															value: option.value,
														},
												  ]
												: [
														{
															value: option.value,
														},
												  ]
										);
									}
								} else {
									props.onChange(
										props.values.filter(
											v => v.value !== option.value
										)
									);
								}
							}}
						/>
					</div>
				);
			})
			.filter((_o, index) => filterItem(props.options[index], filter));
	}, [props, filter]);
	return (
		<Popover
			active={props.active}
			activator={props.activator}
			onClose={props.onClose}
			sectioned
			hideOnPrint
		>
			<div
				style={{
					width: 270,
				}}
			>
				<Stack vertical spacing="baseTight">
					{props.searchable && (
						<TextField
							label=""
							labelHidden
							autoComplete="off"
							placeholder={props.searchPlaceholder || 'Search'}
							prefix={<Icon source={SearchMinor} />}
							value={filter}
							onChange={value => setFilter(value)}
						/>
					)}
					<Stack.Item>
						<Stack vertical spacing="extraTight">
							{checkList}
						</Stack>
						<div style={{textAlign: 'center'}}>
							{props.options.length === 0 && (
								<TextStyle variation="subdued">
									No data
								</TextStyle>
							)}
							{props.options.length > 0 &&
								!props.options.filter(o =>
									filterItem(o, filter)
								).length && (
									<div>
										<TextStyle variation="subdued">
											No results for
										</TextStyle>{' '}
										<TextStyle variation="strong">
											{filter}
										</TextStyle>
									</div>
								)}
						</div>
					</Stack.Item>
					<Button
						plain
						onClick={props.onClear}
						disabled={!props.values.length}
					>
						Clear
					</Button>
				</Stack>
			</div>
		</Popover>
	);
};

export default CollapsiblePopChoice;
