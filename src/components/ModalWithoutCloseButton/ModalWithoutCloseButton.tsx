// @ts-nocheck
import React, {useCallback} from 'react';

import DefaultModal from 'components/Modal';

import styles from './ModalWithoutCloseButton.module.scss';

function ModalWithoutCloseButton<T extends React.FC<any>>({
	ModalComp = DefaultModal,
	extraSmall = false,
	medium = false,
	noPadding = false,
	padding = true,
	customStyles = '',
	...rest
}: {
	ModalComp?: T;
} & React.ComponentProps<T>) {
	const modalRef = useCallback((node: HTMLDivElement | null) => {
		if (node !== null) {
			const rootRef = node.closest('[data-portal-id]');

			if (rootRef) {
				rootRef.classList.add(styles.no_close);
				const modalEl = rootRef.querySelector<HTMLDivElement>(
					'.Polaris-Modal-Dialog__Modal'
				);
				if (!modalEl) return;

				if (extraSmall) {
					modalEl.classList.add('extraSmall');
				}

				if (medium) {
					modalEl.classList.add('medium');
				}

				if (!padding) {
					rootRef.classList.add(styles.no_padding);
				}

				if (noPadding) {
					modalEl.classList.add('noPadding');
				}

				if (customStyles) {
					modalEl.style = customStyles;
				}
			}
		}
	}, []);

	return (
		<ModalComp {...rest}>
			<div ref={modalRef} />
			{rest.children}
		</ModalComp>
	);
}

ModalWithoutCloseButton.defaultProps = {
	onClose: undefined,
	primaryAction: undefined,
	secondaryActions: undefined,
	open: undefined,
};

export default ModalWithoutCloseButton;
