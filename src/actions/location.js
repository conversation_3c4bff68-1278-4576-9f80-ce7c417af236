import {
	PATCH_QUERY,
	QUERY_UPDATE,
	PATCH_PATH,
	PATH_UPDATE,
} from 'constants/ActionTypes';

export function patchQuery(newQuery) {
	return {
		type: PATCH_QUERY,
		payload: newQuery,
	};
}

export function queryUpdate(location) {
	return {
		type: QUERY_UPDATE,
		payload: location,
	};
}

export function pathUpdate(location) {
	return {
		type: PATH_UPDATE,
		payload: location,
	};
}

/**
 * Patch router location pathname.
 * @param {string} name New path need to add
 * @param {number|string} position Which position to add, default is -1, the last position; if is `string`, will do the slice.
 * @param {*} deleteCount How many current path from the position need to delete.
 */
export function patchPath(name, position = -1, deleteCount = 0) {
	return {
		type: PATCH_PATH,
		payload: {
			name,
			position,
			deleteCount,
		},
	};
}

export function pushPath(name) {
	return patchPath(name);
}

export function popPath(count) {
	return patchPath(null, -count, count);
}

// `foo/targetName` -> `foo/replace`
export function splicePath(targetName, replace) {
	return patchPath(replace, targetName);
}
