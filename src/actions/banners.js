import {ADD_BANNER, REMOVE_BANNER} from 'constants/ActionTypes';

import {BANNER_DEFAULT_KEY} from 'constants/Keys';

export function addBanner(payload) {
	return {
		type: ADD_BANNER,
		payload,
	};
}

export function removeBanner(...arg) {
	return {
		type: REMOVE_BANNER,
		payload: arg,
	};
}

// remove the specific id & the default id
export function removeBannerCleanly(...arg) {
	return {
		type: REMOVE_BANNER,
		payload: [BANNER_DEFAULT_KEY, ...arg],
	};
}
