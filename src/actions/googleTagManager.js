import {GTM_PUSH_EVENT} from 'constants/ActionTypes';

/**
 *
 * @param {object} dataLayer the dataLayer needed to push
 * @param {array|string} commonDataNames the common infomation needed to add on
 */
export const pushEvent = (
	dataLayer,
	commonDataNames = [],
	moreCommonDataNames = []
) => {
	let commonDataName;
	if (commonDataNames === 'defaultUserInformation') {
		commonDataName = [
			'userId',
			'loginStatus',
			'brandName',
			'monthlyShipment',
			'currentPlan',
			...moreCommonDataNames,
		];
	} else {
		commonDataName = [...commonDataNames, ...moreCommonDataNames];
	}
	return {
		type: GTM_PUSH_EVENT,
		payload: {
			dataLayer,
			commonDataName,
		},
	};
};

export const pushPureEvent = dataLayer => {
	return {
		type: GTM_PUSH_EVENT,
		payload: {
			dataLayer,
		},
	};
};
