import {TOGGLE_POPOVER, CLOSE_POPOVER} from 'constants/ActionTypes';

export function togglePopover(name) {
	return {
		type: TOGGLE_POPOVER,
		payload: {name: getName(name)},
	};
}

export function closePopover(name) {
	return {
		type: CLOSE_POPOVER,
		payload: {name: getName(name)},
	};
}

function getName(name) {
	if (typeof name === 'string') return name;
	if (name.POPOVER) return name.POPOVER;
	return '';
}
