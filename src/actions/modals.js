import {
	TOGGLE_MODAL,
	OPEN_MODAL,
	CLOSE_MODAL,
	CLOSE_ALL_MODAL,
} from 'constants/ActionTypes';

function getName(name) {
	if (typeof name === 'string') return name;
	if (name.RESOURCE) return name.RESOURCE;
	return '';
}

function getSubName(subName) {
	return subName ? `_${subName}` : '';
}

export function hideUpgradeHint() {
	return {
		type: TOGGLE_MODAL,
		payload: {
			name: 'upgradeHint',
			open: false,
		},
	};
}

export function openModal(name, subName, data) {
	return {
		type: OPEN_MODAL,
		payload: {name: getName(name) + getSubName(subName), ...data},
	};
}

export function closeModal(name, subName) {
	return {
		type: CLOSE_MODAL,
		payload: {name: getName(name) + getSubName(subName)},
	};
}

export function closeAllModal() {
	return {
		type: CLOSE_ALL_MODAL,
	};
}
