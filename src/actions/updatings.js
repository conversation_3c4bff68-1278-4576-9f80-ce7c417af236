import {START_UPDATING, END_UPDATING} from 'constants/ActionTypes';

export function startUpdating(name, keys) {
	const keyArray = getKeys(keys);
	return {
		type: START_UPDATING,
		payload: {name: getName(name), keys: keyArray},
	};
}

export function endUpdating(name, keys) {
	const keyArray = getKeys(keys);
	return {
		type: END_UPDATING,
		payload: {name: getName(name), keys: keyArray},
	};
}

function getKeys(keys) {
	return (typeof keys === 'string' ? [keys] : keys) || [];
}

function getName(name) {
	if (typeof name === 'string') return name;
	if (name.UPDATING) return name.UPDATING;
	return '';
}
