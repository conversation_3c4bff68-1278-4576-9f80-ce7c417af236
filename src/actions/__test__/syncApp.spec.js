import {ADD_SYNC_BANNER, REMOVE_SYNC_BANNER} from 'constants/ActionTypes';
import {addSyncBanner, removeSyncBanner} from 'actions/syncApp';

describe('SyncApp Action', () => {
	const payload = {
		status: 'critical',
		message: 'Network error',
	};

	it('should return add syncBanner', () => {
		const expected = {
			type: ADD_SYNC_BANNER,
			payload: payload,
		};
		expect(addSyncBanner(payload)).toEqual(expected);
	});

	it('should return remove bannner', () => {
		const expected = {
			type: REMOVE_SYNC_BANNER,
			payload: {},
		};
		expect(removeSyncBanner(payload)).toEqual(expected);
	});
});
