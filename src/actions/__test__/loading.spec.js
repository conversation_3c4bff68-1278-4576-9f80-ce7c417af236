import {START_LOADING, END_LOADING} from 'constants/ActionTypes';
import makeResource from 'constants/utils/makeResource';
import {startLoading, endLoading} from '../loadings';

describe('Loading Action', () => {
	const TEST_RESOURCE = makeResource('test-resource', 'CUR');

	it('Should return start loading action', () => {
		const expected = {
			type: START_LOADING,
			payload: {
				name: TEST_RESOURCE.LOADING,
			},
		};
		expect(startLoading(TEST_RESOURCE)).toEqual(expected);
	});

	it('Should return end loading action', () => {
		const expected = {
			type: END_LOADING,
			payload: {
				name: TEST_RESOURCE.LOADING,
			},
		};
		expect(endLoading(TEST_RESOURCE)).toEqual(expected);
	});
});
