import {ADD_BANNER, REMOVE_BANNER} from 'constants/ActionTypes';
import {addBanner, removeBanner} from 'actions/banners';

describe('Banners Action', () => {
	it('should return add banner', () => {
		const payload = {
			status: 'success',
			message: 'Your shipping label is ready to print.',
		};
		const expected = {
			type: ADD_BANNER,
			payload: payload,
		};
		expect(addBanner(payload)).toEqual(expected);
	});

	it('should return remove banner', () => {
		const expected = {
			type: REMOVE_BANNER,
			payload: ['id'],
		};
		expect(removeBanner('id')).toEqual(expected);
	});
});
