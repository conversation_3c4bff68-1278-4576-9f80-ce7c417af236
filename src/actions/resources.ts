import {ResourceType} from 'constants/utils/makeResource';

export const fetchRequest = (resource: ResourceType, defaultPayload = {}) => (
	payload: any
) => ({
	type: resource.FETCH_REQUEST,
	payload: {...defaultPayload, ...payload},
});

export const fetchDone = (resource: ResourceType) => (payload: any) => ({
	type: resource.FETCH_DONE,
	payload,
});

export const updateRequest = (resource: ResourceType) => (payload: any) => ({
	type: resource.UPDATE_REQUEST,
	payload,
});

export const updateDone = (resource: ResourceType) => (payload: any) => ({
	type: resource.UPDATE_DONE,
	payload,
});

export const deleteRequest = (resource: ResourceType) => (payload: any) => ({
	type: resource.DELETE_REQUEST,
	payload,
});

export const deleteDone = (resource: ResourceType) => (payload: any) => ({
	type: resource.DELETE_DONE,
	payload,
});

export const createRequest = (resource: ResourceType) => (payload: any) => ({
	type: resource.CREATE_REQUEST,
	payload,
});

export const createDone = (resource: ResourceType) => (payload: any) => ({
	type: resource.CREATE_DONE,
	payload,
});

export const fetchQueryRequest = (resource: ResourceType) => (
	payload: any
) => ({
	type: resource.FETCH_QUERY_REQUEST,
	payload,
});

export const fetchQueryDone = (resource: ResourceType) => (payload: any) => ({
	type: resource.FETCH_QUERY_DONE,
	payload,
});

export const clear = (resource: ResourceType) => ({
	type: resource.CLEAR,
});
