import {ADD_TOAST, REMOVE_TOAST} from 'constants/ActionTypes';

/**
 * push a toast message to the shown toasts list.
 * @param {object} payload has defined a key: { key: {message: string, error: bool=false} } or no key: {message: string, error: bool=false}
 */
export function addToast(payload) {
	return {
		type: ADD_TOAST,
		payload,
	};
}

/**
 * Remove a toast from the toasts array store.
 * @param {number} payload key of the toasts
 */
export function removeToast(payload) {
	return {
		type: REMOVE_TOAST,
		payload,
	};
}

/**
 * show an error toast
 * @param {string} payload error message
 */
export function showErrorToast(payload) {
	return {
		type: ADD_TOAST,
		payload: {
			error: true,
			message: payload,
		},
	};
}
