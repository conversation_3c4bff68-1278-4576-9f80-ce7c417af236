// @ts-nocheck
import {
	START_LOADING,
	END_LOADING,
	START_FRAME_LOADING,
	END_FRAME_LOADING,
} from 'constants/ActionTypes';

function getName(name) {
	if (typeof name === 'string') return name;
	if (name.LOADING) return name.LOADING;
	return '';
}
function getSubName(subName?) {
	return subName ? `_${subName}` : '';
}

/**
 * start loading action
 * @param {string} name
 * @param {string} subName sub key, if need to specify
 */
export function startLoading(name, subName?) {
	return {
		type: START_LOADING,
		payload: {name: getName(name) + getSubName(subName)},
	};
}

export function endLoading(name, subName?) {
	return {
		type: END_LOADING,
		payload: {name: getName(name) + getSubName(subName)},
	};
}

export function startFrameLoading() {
	return {
		type: START_FRAME_LOADING,
		payload: true,
	};
}

export function endFrameLoading() {
	return {
		type: END_FRAME_LOADING,
		payload: false,
	};
}
