import {
	useBillingGlobalState,
	useBillingDetectUrlActionHook,
} from 'aftershipBillingUi';
import React, {useEffect, useRef} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {usePrevious} from 'react-use';

import {fetchRequest} from 'actions/resources';
import {SUBSCRIPTIONS} from 'constants/Resources';
import {CouponType} from 'constants/billings/coupon';
import {useSetCoupons} from 'hooks/billings/useChoosePlan';
import useExtraPermissions from 'hooks/billings/useExtraPermissions';
import {getShouldUsePartnerPlans} from 'selectors/billings/getPlans';
import isLoading from 'selectors/isLoading';
import isCompany from 'utils/isCompany';
import parseQuery from 'utils/query/parseQuery';

const useInitLoading = (loading: boolean) => {
	const initialized = useRef<boolean>(false);
	const previousInitLoading = usePrevious(loading);
	if (!initialized.current) {
		if (loading === false && previousInitLoading) {
			initialized.current = true;
		}
	}
	return initialized.current;
};

export const useGlobal = () => {
	useBillingDetectUrlActionHook({detect: true});
	const extraPermissionData = useExtraPermissions();

	const {initialized: billingInitialized} = useBillingGlobalState();
	// you can put your one-time loading here
	const initLoading = useSelector((state: any) => {
		return (
			// will delete this two selectors as legacy billing is going down
			isLoading(state, SUBSCRIPTIONS)
		);
	});

	const selectorInit = useInitLoading(
		initLoading || !billingInitialized || extraPermissionData.loading
	);
	return {initLoading: !selectorInit};
};

const useSetPartner = () => {
	const usePartnerPlans = useSelector(getShouldUsePartnerPlans);
	const {partner} = parseQuery(location.search);
	const setCoupons = useSetCoupons(CouponType.partnerCoupon);
	useEffect(() => {
		if (usePartnerPlans && partner) {
			setCoupons();
		}
	}, [partner, usePartnerPlans]);
};

export const WithoutSubscriptionEffect = () => {
	useSetPartner();
	return <></>;
};

export const WithSubscriptionEffect = () => {
	useSetPartner();
	return <></>;
};

export default () => {
	const dispatch = useDispatch();
	useEffect(() => {
		if (!isCompany()) {
			dispatch(fetchRequest(SUBSCRIPTIONS)({}));
		}
	}, []);

	return <div />;
};
