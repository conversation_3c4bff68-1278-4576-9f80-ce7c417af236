import {ConnectedRouter} from 'connected-react-router';
import React, {ReactNode} from 'react';
import {Provider} from 'react-redux';

import {store} from 'store';

import history from '../utils/history';
import setASConfirm from '../utils/setASConfirm';

interface IProps {
	children: ReactNode;
}

setASConfirm(store);

const WrapReduxProvider = ({children}: IProps) => {
	return (
		<Provider store={store}>
			<ConnectedRouter history={history}>{children}</ConnectedRouter>
		</Provider>
	);
};

export default WrapReduxProvider;
