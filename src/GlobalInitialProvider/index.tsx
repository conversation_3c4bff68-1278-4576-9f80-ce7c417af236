import * as Sentry from '@sentry/react';
import {OppAPPSProvider} from 'apps/Widgets';
import React from 'react';

import ErrorState from 'components/ErrorState';

import AutomizelyProvider from '../components/AutomizelyProvider';

import WrapAhaContext from './WrapAhaContext';
import WrapApolloProvider from './WrapApollo';
import WrapAuthProvider from './WrapAuth';
import WrapBillingProvider from './WrapBilling';
import WrapCommentsProvider from './WrapComments';
import {WrapGrowthProvider} from './WrapGrowthProvider';
import {WrapMessageMarketing} from './WrapMessageMarketing';
import WrapNtcProvider from './WrapNotificationCenter';
import {WrapOnboardingProvider} from './WrapOnboardingProvider';
import WrapQueryClientProvider from './WrapQueryClient';
import WrapRBACProvider from './WrapRBAC';
import WrapReduxProvider from './WrapRedux';
import WrapShopifyProvider, {WrapPolarisProvider} from './WrapShopify';

interface IProps {
	children: React.ReactNode;
}

const GlobalInitialProvider = ({children}: IProps) => {
	return (
		<Sentry.ErrorBoundary
			beforeCapture={scope => {
				scope.setLevel('fatal');
			}}
			fallback={<ErrorState />}
		>
			<WrapShopifyProvider>
				<WrapAuthProvider>
					<WrapQueryClientProvider>
						<WrapAhaContext>
							<WrapGrowthProvider>
								<WrapBillingProvider>
									<OppAPPSProvider productCode="tracking">
										<WrapRBACProvider>
											<WrapApolloProvider>
												<WrapNtcProvider>
													<WrapReduxProvider>
														<AutomizelyProvider>
															<WrapMessageMarketing>
																<WrapCommentsProvider>
																	<WrapOnboardingProvider>
																		{
																			children
																		}
																	</WrapOnboardingProvider>
																</WrapCommentsProvider>
															</WrapMessageMarketing>
														</AutomizelyProvider>
													</WrapReduxProvider>
												</WrapNtcProvider>
											</WrapApolloProvider>
										</WrapRBACProvider>
									</OppAPPSProvider>
								</WrapBillingProvider>
							</WrapGrowthProvider>
						</WrapAhaContext>
					</WrapQueryClientProvider>
				</WrapAuthProvider>
			</WrapShopifyProvider>
		</Sentry.ErrorBoundary>
	);
};

export const GlobalInitialProviderForMF = ({children}: IProps) => {
	return (
		<Sentry.ErrorBoundary
			beforeCapture={scope => {
				scope.setLevel('fatal');
			}}
			fallback={<ErrorState />}
		>
			<WrapPolarisProvider>
				<WrapQueryClientProvider>
					<WrapAhaContext>
						<WrapGrowthProvider>
							<WrapBillingProvider>
								<OppAPPSProvider productCode="tracking">
									<WrapRBACProvider>
										<WrapApolloProvider>
											<WrapNtcProvider>
												<WrapReduxProvider>
													<AutomizelyProvider>
														<WrapMessageMarketing>
															<WrapCommentsProvider>
																<WrapOnboardingProvider>
																	{children}
																</WrapOnboardingProvider>
															</WrapCommentsProvider>
														</WrapMessageMarketing>
													</AutomizelyProvider>
												</WrapReduxProvider>
											</WrapNtcProvider>
										</WrapApolloProvider>
									</WrapRBACProvider>
								</OppAPPSProvider>
							</WrapBillingProvider>
						</WrapGrowthProvider>
					</WrapAhaContext>
				</WrapQueryClientProvider>
			</WrapPolarisProvider>
		</Sentry.ErrorBoundary>
	);
};

export default GlobalInitialProvider;
