// eslint-disable-next-line import/no-unresolved
import {BasicDependenciesProvider} from 'aftershipNotification';
import React, {PropsWithChildren, FC} from 'react';

import isCompany from 'utils/isCompany';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface IProps {}

export const WrapMessageMarketing: FC<PropsWithChildren<IProps>> = ({
	children,
}) => {
	return isCompany() ? (
		<>{children}</>
	) : (
		<BasicDependenciesProvider
			productCode="aftership"
			release={process.env.GIT_COMMIT_SHA1 || 'AfterShipTrackingRelease'}
			settingsPath="/settings/ens"
		>
			{children}
		</BasicDependenciesProvider>
	);
};
