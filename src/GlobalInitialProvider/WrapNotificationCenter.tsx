import '@aftership/accounts-widgets/dist/index.css';
import {NtcProvider, Popup, PopupSurvey} from 'AftershipNavigation';
import React from 'react';

interface IProps {
	children: React.ReactNode;
}

const WrapNtcProvider = ({children}: IProps) => {
	return (
		<NtcProvider
			productCode="aftership"
			appEnv={
				process.env.APP_ENV === 'development'
					? 'testing'
					: process.env.APP_ENV || 'production'
			}
		>
			<Popup pageCode="global" />
			<PopupSurvey pageCode="global" />
			{children}
		</NtcProvider>
	);
};

export default WrapNtcProvider;
