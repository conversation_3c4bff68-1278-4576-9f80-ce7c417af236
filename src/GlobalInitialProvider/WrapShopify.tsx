import {AppProvider} from '@shopify/polaris';
import en from '@shopify/polaris/locales/en.json';
import {
	I18nContext,
	I18nManager,
	useI18n,
	TranslationDictionary,
} from '@shopify/react-i18n';
import i18next from 'i18next';
import {merge} from 'lodash';
import React, {ReactNode, useCallback, useRef, useState} from 'react';

import AdaptorLink from '../components/AdaptorLink';
import {theme} from '../components/TopBar';

interface IProps {
	children: ReactNode;
}

const enTranslations = {
	Polaris: {
		ResourceList: {
			// https://github.com/Shopify/polaris-react/blob/master/src/locales/en.json
			// Remove "in your store"
			allItemsSelected:
				'All {itemsLength}+ {resourceNamePlural} are selected.',
			selectAllItems: 'Select all {itemsLength}+ {resourceNamePlural}',
			emptySearchResultDescription:
				'Try changing the filters or search terms',
		},
	},
};

const translations = merge(en, enTranslations);

const locales = import.meta.glob<TranslationDictionary>(
	'../node_modules/@shopify/polaris/locales/(en|de|it|es|fr).json'
);

export const WrapPolarisProvider = ({children}: IProps) => {
	const [i18n] = useI18n({
		id: 'Polaris',
		fallback: translations,
		translations(locale) {
			return locales[
				`../node_modules/@shopify/polaris/locales/${locale}.json`
			]?.().then(trans =>
				locale === 'en' ? merge(trans, enTranslations) : trans
			);
		},
	});

	return (
		<AppProvider
			linkComponent={AdaptorLink}
			// Notitce: 通过火焰图查看到该版本的 Polaris 内置的 ThemeProvider 中一个 useMemo 执行时间过长，影响初始化性能
			theme={theme}
			i18n={i18n.translations}
			features={{newDesignLanguage: true}}
		>
			{children}
		</AppProvider>
	);
};

export const WrapI18nProvider = ({children}: IProps) => {
	const i18nManagerRef = useRef<I18nManager | null>(null);
	const [lng, setLng] = useState('en');

	const getI18nManagerInstance = useCallback(() => {
		if (i18nManagerRef.current !== null) {
			return i18nManagerRef.current;
		}
		const i18nManager = new I18nManager({
			locale: 'en',
			currency: 'USD',
		});
		i18nManagerRef.current = i18nManager;
		return i18nManager;
	}, []);

	i18next.on('languageChanged', lng => {
		getI18nManagerInstance().update({locale: lng, currency: 'USD'});
		setLng(lng);
	});

	if (getI18nManagerInstance().details.locale !== lng) {
		getI18nManagerInstance().update({locale: lng, currency: 'USD'});
	}

	return (
		<I18nContext.Provider value={getI18nManagerInstance()}>
			{children}
		</I18nContext.Provider>
	);
};

const WrapShopifyProvider = ({children}: IProps) => {
	return (
		<WrapI18nProvider>
			<WrapPolarisProvider>{children}</WrapPolarisProvider>
		</WrapI18nProvider>
	);
};

export default WrapShopifyProvider;
