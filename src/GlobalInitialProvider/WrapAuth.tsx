import {AuthProvider, IOptions} from '@aftership/automizely-product-auth';
import React, {ReactNode} from 'react';

import isCompany from 'utils/isCompany';

interface IProps {
	children: ReactNode;
}

const WrapAuthProvider = ({children}: IProps) => {
	const config: IOptions = isCompany()
		? {
				clientId: 'company',
				realm: 'business',
				forceLogin: true,
				flow: 'standard',
				withoutCreateOrganization: true,
				refreshPageAtOrganizationSwitched: false,
		  }
		: {
				clientId: 'aftership',
				realm: 'business',
				forceLogin: true,
				flow: 'standard',
				withoutCreateOrganization: false,
				refreshPageAtOrganizationSwitched: true,
				appConfig: {
					name: 'aftership',
				},
		  };

	return <AuthProvider config={config}>{children}</AuthProvider>;
};

export default WrapAuthProvider;
