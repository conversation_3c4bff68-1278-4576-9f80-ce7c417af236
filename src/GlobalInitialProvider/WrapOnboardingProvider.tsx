import {OnboardingProvider} from '@aftership/sdk-journey-onboarding';
import React, {useCallback} from 'react';

import {useTourHasShow} from 'components/Tour/utils';
import {
	useOrganizationConfig,
	useOrganizationConfigUpdate,
	useOrganizationOnboarding,
} from 'hooks';
import {
	useIsEnterprise,
	useIsTrialEnterprise,
} from 'hooks/billings/useChoosePlan';
import {useIsFreeTrialPlan} from 'hooks/billings/useIsFreeUser';
import {useOnboardingOnOff} from 'hooks/onboarding/useOnboardingOnOff';
import {useOnboardingV4} from 'hooks/onboarding/useOnboardingV4';
import useIsWSC from 'hooks/useIsWSC';
import useIsWineShipping from 'hooks/useIsWineShipping';
import isCompany from 'utils/isCompany';

interface IProps {
	children: React.ReactElement;
}

const Provider: React.FC<IProps> = ({children}) => {
	useOnboardingV4();

	const {isFetched: isFetchedOnboardingV3, isHideGuide: isHideGuideV3} =
		useOrganizationOnboarding();

	const {
		data: organization,
		refetch: refetchConfig,
		isFetched: isFetchedOrg,
	} = useOrganizationConfig();

	const isEnterprise = useIsEnterprise();
	const isTrialEnterprise = useIsTrialEnterprise();
	const {isWineShipping} = useIsWineShipping();
	const {isWSC} = useIsWSC();

	const hasShowTour = useTourHasShow();

	const disabled =
		Boolean(isEnterprise) ||
		Boolean(isTrialEnterprise) ||
		isWineShipping ||
		isWSC;

	const isFreeTrial = useIsFreeTrialPlan();

	const {mutate} = useOrganizationConfigUpdate({
		onSuccess: () => {
			refetchConfig();
		},
	});

	// @TODO: 临时解决方案，后续需要优化
	// 防止死循环
	const onAllDone = useCallback(() => {
		if (
			isFetchedOrg &&
			organization?.trialPlanGuides &&
			(!organization?.trialPlanGuides?.monitorYourShipments?.done ||
				!organization?.trialPlanGuides?.customerSelfTracking?.done ||
				!organization?.trialPlanGuides?.automationNotification?.done)
		) {
			const newTrialPlanGuides = JSON.parse(
				JSON.stringify(organization.trialPlanGuides)
			);

			newTrialPlanGuides.monitorYourShipments.done = true;
			newTrialPlanGuides.customerSelfTracking.done = true;
			newTrialPlanGuides.automationNotification.done = true;

			mutate({
				input: {
					// 注意 trialPlanGuides 需要全量更新
					trialPlanGuides: newTrialPlanGuides,
				},
			});
		}
	}, [isFetchedOrg, organization?.trialPlanGuides, mutate]);

	return (
		<OnboardingProvider
			productCode="tracking"
			disabledOnboarding={disabled}
			disabledHints={!isFreeTrial}
			disabledTours={hasShowTour}
			hideOnboarding={isFetchedOnboardingV3 && isHideGuideV3}
			// onAllDone={onAllDone}
		>
			{children}
		</OnboardingProvider>
	);
};

export const WrapOnboardingProvider: React.FC<IProps> = ({children}) => {
	const canShow = useOnboardingOnOff();

	return isCompany() || !canShow ? children : <Provider>{children}</Provider>;
};
