import {useAuth} from '@aftership/automizely-product-auth';
import {RBACProvider} from '@aftership/automizely-rbac-react';
import React, {ReactNode} from 'react';

interface IProps {
	children: ReactNode;
}

const WrapRBACProvider = ({children}: IProps) => {
	const [{organization}] = useAuth();

	return (
		<RBACProvider orgId={organization?.id || ''}>{children}</RBACProvider>
	);
};

export default WrapRBACProvider;
