// import 'aftershipBillingUi/build/index.css';
// import 'aftershipBillingUi/dist/index.css';
import {capture} from '@aftership/datacat';
import {Skeleton} from 'AftershipNavigation';
import {BillingProviderV2} from 'aftershipBillingUi';
import React, {ReactNode, useEffect, useRef} from 'react';

import history from 'utils/history';

interface IProps {
	children: ReactNode;
}

const GlobalSpinLoading = () => {
	const renderRef = useRef({mountStartTime: 0});
	if (!renderRef.current.mountStartTime) {
		renderRef.current.mountStartTime = performance.now();
	}
	useEffect(() => {
		const {mountStartTime} = renderRef.current;
		const mountEndTime = performance.now();
		return () => {
			// unmounted
			const unmountedTime = performance.now();
			capture('performance', {
				type: 'global_loading',
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				entered_time: (window as any).AS_ENTER_TIMESTAMP / 1000,
				// eslint-disable-next-line react-hooks/exhaustive-deps
				mount_start_time: mountStartTime / 1000,
				mount_end_time: mountEndTime / 1000,
				unmounted_time: unmountedTime / 1000,
				true_fcp:
					// eslint-disable-next-line @typescript-eslint/no-explicit-any
					(unmountedTime - (window as any).AS_ENTER_TIMESTAMP) / 1000,
			});
		};
	}, []);
	return <Skeleton />;
};

const WrapBillingProvider = ({children}: IProps) => {
	return (
		<React.Suspense fallback={<GlobalSpinLoading />}>
			<BillingProviderV2
				contactCMSMrrThreshold={200}
				productCode="aftership"
				appCode="aftership"
				theme={{
					colors: {
						primary: '#ff6b2b',
					},
				}}
				rootHistory={history}
			>
				{children}
			</BillingProviderV2>
		</React.Suspense>
	);
};

export default WrapBillingProvider;
