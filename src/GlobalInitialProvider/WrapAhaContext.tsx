import {ConfigProvider} from '@aftership/aha';
import type {ConfigProviderProps} from '@aftership/aha';
import React from 'react';

import {useUserLanguage} from 'hooks';

interface IProps {
	children: React.ReactNode;
}

const WrapAhaContext = ({children}: IProps) => {
	const {language} = useUserLanguage();

	return (
		<ConfigProvider locale={language as ConfigProviderProps['locale']}>
			{children}
		</ConfigProvider>
	);
};

export default WrapAhaContext;
