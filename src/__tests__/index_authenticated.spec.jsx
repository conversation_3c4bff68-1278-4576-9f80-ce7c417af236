import React from 'react';
import ReactDOM from 'react-dom';
import SpinLoading from 'components/SpinLoading';
import App from '../App';

ReactDOM.render = jest.fn();

window.gtag = jest.fn();

jest.mock('@aftership/analytiker-editor', () => ({
	useAnalytiker: () => ({
		routeEntries: [],
		routeLoaders: [],
	}),
}));
jest.mock('@aftership/comments', () => ({
	__esModule: true,
	default: jest.fn().mockReturnValue(() => true),
}));

xdescribe('application initialization authenticated', () => {
	it('should call ReactDOM.render() with SpinLoading and App', async () => {
		await import('../index');

		expect(ReactDOM.render.mock.calls.length).toBe(2);
		expect(ReactDOM.render.mock.calls[0][0]).toStrictEqual(
			<SpinLoading />,
			null
		);
		expect(ReactDOM.render.mock.calls[1][0]).toStrictEqual(<App />, null);
	});
});
