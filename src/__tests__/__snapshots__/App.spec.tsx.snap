// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`[ASADMIN-2487] fill coverage should render the App correctly 1`] = `
"<div>
  <div
    class=\\"Polaris-Frame Polaris-Frame--hasNav Polaris-Frame--hasTopBar\\"
    data-polaris-layer=\\"true\\"
    data-has-navigation=\\"true\\"
  >
    <div class=\\"Polaris-Frame__Skip\\">
      <a href=\\"#AppFrameMainContent\\">Skip to content</a>
    </div>
    <div
      class=\\"Polaris-Frame__TopBar\\"
      data-polaris-layer=\\"true\\"
      data-polaris-top-bar=\\"true\\"
      id=\\"AppFrameTopBar\\"
    >
      <div class=\\"Polaris-TopBar\\">
        <button
          type=\\"button\\"
          class=\\"Polaris-TopBar__NavigationIcon\\"
          aria-label=\\"Toggle menu\\"
        >
          <span class=\\"Polaris-Icon\\"
            ><svg
              viewBox=\\"0 0 20 20\\"
              class=\\"Polaris-Icon__Svg\\"
              focusable=\\"false\\"
              aria-hidden=\\"true\\"
            >
              <path
                d=\\"M19 11H1a1 1 0 0 1 0-2h18a1 1 0 1 1 0 2zm0-7H1a1 1 0 0 1 0-2h18a1 1 0 1 1 0 2zm0 14H1a1 1 0 0 1 0-2h18a1 1 0 0 1 0 2z\\"
              ></path></svg
          ></span>
        </button>
        <div
          class=\\"Polaris-TopBar__LogoContainer Polaris-TopBar__LogoDisplayControl\\"
        >
          <a
            data-polaris-unstyled=\\"true\\"
            class=\\"Polaris-TopBar__LogoLink\\"
            style=\\"width: 100px;\\"
            href=\\"/\\"
            ><img
              src=\\"aftership.svg\\"
              alt=\\"aftership\\"
              class=\\"Polaris-TopBar__Logo\\"
              style=\\"width: 100px;\\"
          /></a>
        </div>
        <div class=\\"Polaris-TopBar__Contents\\">
          <div class=\\"Polaris-TopBar__SearchField\\"></div>
          <div class=\\"Polaris-TopBar__SecondaryMenu\\">
            <div
              class=\\"Polaris-Stack Polaris-Stack--alignmentCenter Polaris-Stack--noWrap\\"
            >
              <div class=\\"Polaris-Stack__Item\\"></div>
              <div class=\\"Polaris-Stack__Item\\">
                <div role=\\"org-switcher\\"></div>
              </div>
            </div>
          </div>
          <div>
            <div class=\\"Polaris-TopBar-Menu__ActivatorWrapper\\">
              <button
                type=\\"button\\"
                class=\\"Polaris-TopBar-Menu__Activator\\"
                tabindex=\\"0\\"
                aria-controls=\\"Polarispopover1\\"
                aria-owns=\\"Polarispopover1\\"
                aria-expanded=\\"false\\"
              >
                <div class=\\"Polaris-MessageIndicator__MessageIndicatorWrapper\\">
                  <span
                    aria-label=\\"Avatar with initials A\\"
                    role=\\"img\\"
                    class=\\"Polaris-Avatar Polaris-Avatar--sizeSmall Polaris-Avatar--styleSix\\"
                    ><span class=\\"Polaris-Avatar__Initials\\"
                      ><svg class=\\"Polaris-Avatar__Svg\\" viewBox=\\"0 0 40 40\\">
                        <text
                          x=\\"50%\\"
                          y=\\"50%\\"
                          dy=\\"0.35em\\"
                          fill=\\"currentColor\\"
                          font-size=\\"20\\"
                          text-anchor=\\"middle\\"
                        >
                          A
                        </text>
                      </svg></span
                    ></span
                  >
                </div>
                <span class=\\"Polaris-TopBar-UserMenu__Details\\"
                  ><p class=\\"Polaris-TopBar-UserMenu__Name\\">SSO Username</p>
                  <p class=\\"Polaris-TopBar-UserMenu__Detail\\">
                    <EMAIL>
                  </p></span
                >
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class=\\"Polaris-Frame__Navigation\\" id=\\"AppFrameNav\\">
        <nav class=\\"Polaris-Navigation\\">
          <div class=\\"Polaris-Navigation__LogoContainer\\">
            <a
              data-polaris-unstyled=\\"true\\"
              class=\\"Polaris-Navigation__LogoLink\\"
              style=\\"width: 100px;\\"
              href=\\"/\\"
              ><img
                src=\\"aftership.svg\\"
                alt=\\"aftership\\"
                class=\\"Polaris-Navigation__Logo\\"
                style=\\"width: 100px;\\"
            /></a>
          </div>
          <div
            class=\\"Polaris-Navigation__PrimaryNavigation Polaris-Scrollable Polaris-Scrollable--vertical\\"
            data-polaris-scrollable=\\"true\\"
          >
            <ul class=\\"Polaris-Navigation__Section\\">
              <li class=\\"Polaris-Navigation__ListItem\\">
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item Polaris-Navigation__Item--selected Polaris-Navigation--subNavigationActive\\"
                    tabindex=\\"0\\"
                    href=\\"/\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><svg
                          viewBox=\\"0 0 20 20\\"
                          class=\\"Polaris-Icon__Svg\\"
                          focusable=\\"false\\"
                          aria-hidden=\\"true\\"
                        >
                          <path
                            d=\\"M18 7.261V17.5c0 .841-.672 1.5-1.5 1.5h-2c-.828 0-1.5-.659-1.5-1.5V13H7v4.477C7 18.318 6.328 19 5.5 19h-2c-.828 0-1.5-.682-1.5-1.523V7.261a1.5 1.5 0 0 1 .615-1.21l6.59-4.82a1.481 1.481 0 0 1 1.59 0l6.59 4.82A1.5 1.5 0 0 1 18 7.26z\\"
                          ></path></svg
                      ></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\">Home</span></a
                  >
                </div>
              </li>
              <li class=\\"Polaris-Navigation__ListItem\\">
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item\\"
                    tabindex=\\"0\\"
                    href=\\"/shipments?page=1&amp;start-date=20200808&amp;end-date=20200808\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><svg
                          viewBox=\\"0 0 20 20\\"
                          class=\\"Polaris-Icon__Svg\\"
                          focusable=\\"false\\"
                          aria-hidden=\\"true\\"
                        >
                          <path
                            fill-rule=\\"evenodd\\"
                            d=\\"M1.5 2A1.5 1.5 0 0 0 0 3.5v11A1.5 1.5 0 0 0 1.5 16H2a3 3 0 1 0 6 0h4a3 3 0 1 0 6 0h.5a1.5 1.5 0 0 0 1.5-1.5v-3.361a1.5 1.5 0 0 0-.214-.772l-2.783-4.639A1.5 1.5 0 0 0 15.717 5H13V3.5A1.5 1.5 0 0 0 11.5 2h-10zM15 17a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM4 16a1 1 0 1 0 2 0 1 1 0 0 0-2 0zm13.234-6H13V7h2.434l1.8 3z\\"
                          ></path></svg
                      ></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\">Shipments</span></a
                  >
                </div>
              </li>
              <li class=\\"Polaris-Navigation__ListItem\\">
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item\\"
                    tabindex=\\"0\\"
                    href=\\"/notifications/setting\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><svg
                          viewBox=\\"0 0 20 20\\"
                          class=\\"Polaris-Icon__Svg\\"
                          focusable=\\"false\\"
                          aria-hidden=\\"true\\"
                        >
                          <path
                            d=\\"M10 0a1 1 0 0 1 1 1v2.032l-.001.021-.002.03A6.002 6.002 0 0 1 16 9c0 3.093.625 4.312 1.599 6.21l.034.068c.17.33-.07.722-.442.722H2.809a.496.496 0 0 1-.442-.722l.034-.068C3.375 13.312 4 12.093 4 9a6.002 6.002 0 0 1 5.003-5.918l-.002-.04A.835.835 0 0 1 9 3V1a1 1 0 0 1 1-1zm2 18a2 2 0 0 1-4 0h4z\\"
                          ></path></svg
                      ></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\"
                      >Notifications<span class=\\"Polaris-Navigation__Indicator\\"
                        ><span
                          class=\\"Polaris-Indicator Polaris-Indicator--pulseIndicator\\"
                        ></span></span></span
                  ></a>
                </div>
              </li>
              <li
                class=\\"Polaris-Navigation__ListItem Polaris-Navigation__ListItem--hasAction\\"
              >
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item\\"
                    tabindex=\\"0\\"
                    href=\\"/tracking-pages\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><svg
                          viewBox=\\"0 0 20 20\\"
                          class=\\"Polaris-Icon__Svg\\"
                          focusable=\\"false\\"
                          aria-hidden=\\"true\\"
                        >
                          <path
                            d=\\"M1.791 2.253l-.597 3.583A1 1 0 0 0 2.18 7h.893a1.5 1.5 0 0 0 1.342-.83L5 5l.585 1.17A1.5 1.5 0 0 0 6.927 7h1.146a1.5 1.5 0 0 0 1.342-.83L10 5l.585 1.17a1.5 1.5 0 0 0 1.342.83h1.146a1.5 1.5 0 0 0 1.342-.83L15 5l.585 1.17a1.5 1.5 0 0 0 1.342.83h.893a1 1 0 0 0 .986-1.164l-.597-3.583A1.5 1.5 0 0 0 16.729 1H3.271a1.5 1.5 0 0 0-1.48 1.253zM4 18.5A1.5 1.5 0 0 1 5.5 17H8v-3h4v3h2.5a1.5 1.5 0 0 1 1.5 1.5v.5H4v-.5z\\"
                          ></path>
                          <path
                            d=\\"M2 9h2v4h12V9h2v4.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 13.5V9z\\"
                          ></path></svg
                      ></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\"
                      >Tracking pages</span
                    ></a
                  ><a
                    href=\\"https://undefined.aftership.io\\"
                    target=\\"_blank\\"
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__SecondaryAction\\"
                    tabindex=\\"0\\"
                    aria-label=\\"View tracking page\\"
                    ><span class=\\"Polaris-Icon\\"
                      ><svg
                        viewBox=\\"0 0 20 20\\"
                        class=\\"Polaris-Icon__Svg\\"
                        focusable=\\"false\\"
                        aria-hidden=\\"true\\"
                      >
                        <path
                          d=\\"M17.928 9.628C17.837 9.399 15.611 4 10 4S2.162 9.399 2.07 9.628a1.017 1.017 0 0 0 0 .744C2.163 10.601 4.389 16 10 16s7.837-5.399 7.928-5.628a1.017 1.017 0 0 0 0-.744zM10 14a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6a2 2 0 1 0 .002 4.001A2 2 0 0 0 9.999 8z\\"
                        ></path></svg></span
                  ></a>
                </div>
              </li>
              <li class=\\"Polaris-Navigation__ListItem\\">
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item\\"
                    tabindex=\\"0\\"
                    href=\\"/tracking-app\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><svg
                          viewBox=\\"0 0 20 20\\"
                          class=\\"Polaris-Icon__Svg\\"
                          focusable=\\"false\\"
                          aria-hidden=\\"true\\"
                        >
                          <path
                            fill-rule=\\"evenodd\\"
                            d=\\"M3 1.5C3 .7 3.7 0 4.5 0h11c.8 0 1.5.7 1.5 1.5v17c0 .8-.7 1.5-1.5 1.5h-11c-.8 0-1.5-.7-1.5-1.5v-17zM5 2h10v14H5V2zm4 15a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2H9z\\"
                          ></path></svg
                      ></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\">Tracking app</span>
                    <div class=\\"Polaris-Navigation__Badge\\">
                      <span
                        class=\\"Polaris-Badge Polaris-Badge--statusNew Polaris-Badge--sizeSmall\\"
                        ><span class=\\"Polaris-VisuallyHidden\\">New</span
                        >New</span
                      >
                    </div></a
                  >
                </div>
              </li>
              <li class=\\"Polaris-Navigation__ListItem\\">
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item\\"
                    tabindex=\\"0\\"
                    href=\\"/dashboard/shipment\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><img
                          class=\\"Polaris-Icon__Img\\"
                          src=\\"data:image/svg+xml;utf8,Polaris_Analytics.svg\\"
                          alt=\\"\\"
                          aria-hidden=\\"true\\"
                      /></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\">Analytics</span></a
                  >
                </div>
              </li>
              <li class=\\"Polaris-Navigation__ListItem\\">
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item\\"
                    tabindex=\\"0\\"
                    href=\\"/apps/track-button\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><svg
                          viewBox=\\"0 0 20 20\\"
                          class=\\"Polaris-Icon__Svg\\"
                          focusable=\\"false\\"
                          aria-hidden=\\"true\\"
                        >
                          <path
                            d=\\"M0 1.5A1.5 1.5 0 0 1 1.5 0h17A1.5 1.5 0 0 1 20 1.5v6A1.5 1.5 0 0 1 18.5 9h-5.889a1.5 1.5 0 0 1-1.5-1.5V5.111a1.111 1.111 0 1 0-2.222 0V7.5a1.5 1.5 0 0 1-1.5 1.5H1.5A1.5 1.5 0 0 1 0 7.5v-6z\\"
                          ></path>
                          <path
                            d=\\"M7 5a3 3 0 0 1 6 0v4.384a.5.5 0 0 0 .356.479l2.695.808a2.5 2.5 0 0 1 1.756 2.748l-.633 4.435A2.5 2.5 0 0 1 14.699 20H6.96a2.5 2.5 0 0 1-2.27-1.452l-2.06-4.464a2.417 2.417 0 0 1-.106-1.777c.21-.607.719-1.16 1.516-1.273 1.035-.148 2.016.191 2.961.82V5zm3-1a1 1 0 0 0-1 1v7.793c0 1.39-1.609 1.921-2.527 1.16-.947-.784-1.59-.987-2.069-.948a.486.486 0 0 0 .042.241l2.06 4.463A.5.5 0 0 0 6.96 18h7.74a.5.5 0 0 0 .494-.43l.633-4.434a.5.5 0 0 0-.35-.55l-2.695-.808A2.5 2.5 0 0 1 11 9.384V5a1 1 0 0 0-1-1z\\"
                          ></path></svg
                      ></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\"
                      >Order lookup widget</span
                    ></a
                  >
                </div>
              </li>
            </ul>
            <div>
              <ul
                class=\\"Polaris-Navigation__Section Polaris-Navigation__Section--fill\\"
              >
                <li class=\\"Polaris-Navigation__SectionHeading\\">
                  <span class=\\"Polaris-Navigation__Text\\">INTEGRATIONS</span>
                </li>
                <li class=\\"Polaris-Navigation__ListItem\\">
                  <div class=\\"Polaris-Navigation__ItemWrapper\\">
                    <a
                      data-polaris-unstyled=\\"true\\"
                      class=\\"Polaris-Navigation__Item\\"
                      tabindex=\\"0\\"
                      href=\\"/store-connection\\"
                      ><div class=\\"Polaris-Navigation__Icon\\">
                        <span class=\\"Polaris-Icon\\"
                          ><svg
                            viewBox=\\"0 0 20 20\\"
                            class=\\"Polaris-Icon__Svg\\"
                            focusable=\\"false\\"
                            aria-hidden=\\"true\\"
                          >
                            <path
                              d=\\"M17.986 12.166a3 3 0 1 1-1.973 0A1.003 1.003 0 0 1 16 12V9.999A3.999 3.999 0 0 0 12.001 6h-.587l1.293 1.293a1 1 0 1 1-1.414 1.414l-3-3a1 1 0 0 1 0-1.414l3-3a1 1 0 0 1 1.414 1.414L11.414 4h.587A5.999 5.999 0 0 1 18 9.999V12c0 .057-.005.112-.014.166zm-14-4.332c.01.054.014.11.014.166v2.001A4 4 0 0 0 7.999 14h.587l-1.293-1.293a1 1 0 0 1 1.414-1.414l3 3a1 1 0 0 1 0 1.414l-3 3a1 1 0 1 1-1.414-1.414L8.586 16h-.587A6 6 0 0 1 2 10.001V8c0-.057.005-.112.014-.166a3 3 0 1 1 1.972 0zM4 5a1 1 0 1 0-2 0 1 1 0 0 0 2 0zm14 10a1 1 0 1 0-2.001.001A1 1 0 0 0 18 15z\\"
                            ></path></svg
                        ></span>
                      </div>
                      <span class=\\"Polaris-Navigation__Text\\"
                        >Connections</span
                      ></a
                    >
                  </div>
                </li>
                <li
                  class=\\"Polaris-Navigation__ListItem Polaris-Navigation__ListItem--hasAction\\"
                >
                  <div class=\\"Polaris-Navigation__ItemWrapper\\">
                    <a
                      data-polaris-unstyled=\\"true\\"
                      class=\\"Polaris-Navigation__Item\\"
                      tabindex=\\"0\\"
                      href=\\"/\\"
                      ><div class=\\"Polaris-Navigation__Icon\\">
                        <span class=\\"Polaris-Icon\\"
                          ><svg
                            viewBox=\\"0 0 20 20\\"
                            class=\\"Polaris-Icon__Svg\\"
                            focusable=\\"false\\"
                            aria-hidden=\\"true\\"
                          >
                            <path
                              d=\\"M9 9H1V2.5A1.5 1.5 0 0 1 2.5 1H9v8zm0 2v8H2.5A1.5 1.5 0 0 1 1 17.5V11h8zm2 0v8h6.5a1.5 1.5 0 0 0 1.5-1.5V11h-8zm4-10a1 1 0 0 1 1 1v2h2a1 1 0 1 1 0 2h-2v2a1 1 0 1 1-2 0V6h-2a1 1 0 1 1 0-2h2V2a1 1 0 0 1 1-1z\\"
                            ></path></svg
                        ></span>
                      </div>
                      <span class=\\"Polaris-Navigation__Text\\">Apps</span></a
                    ><a
                      href=\\"https://organization.automizely.io/apps?organization_id=mockOrgId\\"
                      target=\\"_blank\\"
                      data-polaris-unstyled=\\"true\\"
                      class=\\"Polaris-Navigation__SecondaryAction\\"
                      tabindex=\\"0\\"
                      aria-label=\\"View all apps\\"
                      ><span class=\\"Polaris-Icon\\"
                        ><svg
                          viewBox=\\"0 0 20 20\\"
                          class=\\"Polaris-Icon__Svg\\"
                          focusable=\\"false\\"
                          aria-hidden=\\"true\\"
                        >
                          <path
                            d=\\"M11 4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 1 1-2 0V6.414l-5.293 5.293a1 1 0 0 1-1.414-1.414L13.586 5H12a1 1 0 0 1-1-1zM3 6.5A1.5 1.5 0 0 1 4.5 5H8a1 1 0 0 1 0 2H5v8h8v-3a1 1 0 1 1 2 0v3.5a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 3 15.5v-9z\\"
                          ></path></svg></span
                    ></a>
                  </div>
                </li>
              </ul>
            </div>
            <div
              class=\\"nav-more-products Polaris-Navigation__Section Polaris-Navigation__Section--fill\\"
            >
              <ul
                class=\\"Polaris-Navigation__Section Polaris-Navigation__Section--fill\\"
              >
                <li class=\\"Polaris-Navigation__SectionHeading\\">
                  <span class=\\"Polaris-Navigation__Text\\"
                    ><div
                      class=\\"Polaris-Stack Polaris-Stack--distributionEqualSpacing\\"
                    >
                      <div class=\\"Polaris-Stack__Item\\">
                        <span>MORE PRODUCTS</span>
                      </div>
                      <div class=\\"Polaris-Stack__Item\\">
                        <div style=\\"padding: 0px 1.6rem; cursor: pointer;\\">
                          <svg viewBox=\\"0 0 20 20\\" style=\\"width: 20px;\\">
                            <path d=\\"M15 12l-5-5-5 5h10z\\"></path>
                          </svg>
                        </div>
                      </div></div
                  ></span>
                </li>
              </ul>
            </div>
            <ul class=\\"Polaris-Navigation__Section\\">
              <li class=\\"Polaris-Navigation__ListItem\\">
                <div class=\\"Polaris-Navigation__ItemWrapper\\">
                  <a
                    data-polaris-unstyled=\\"true\\"
                    class=\\"Polaris-Navigation__Item\\"
                    tabindex=\\"0\\"
                    href=\\"/settings\\"
                    ><div class=\\"Polaris-Navigation__Icon\\">
                      <span class=\\"Polaris-Icon\\"
                        ><img
                          class=\\"Polaris-Icon__Img\\"
                          src=\\"data:image/svg+xml;utf8,Polaris_Settings.svg\\"
                          alt=\\"\\"
                          aria-hidden=\\"true\\"
                      /></span>
                    </div>
                    <span class=\\"Polaris-Navigation__Text\\">Settings</span></a
                  >
                </div>
              </li>
            </ul>
          </div>
        </nav>
        <button
          type=\\"button\\"
          class=\\"Polaris-Frame__NavigationDismiss\\"
          aria-hidden=\\"true\\"
          aria-label=\\"Close navigation\\"
          tabindex=\\"-1\\"
        >
          <span class=\\"Polaris-Icon\\"
            ><svg
              viewBox=\\"0 0 20 20\\"
              class=\\"Polaris-Icon__Svg\\"
              focusable=\\"false\\"
              aria-hidden=\\"true\\"
            >
              <path
                d=\\"M11.414 10l6.293-6.293a1 1 0 1 0-1.414-1.414L10 8.586 3.707 2.293a1 1 0 0 0-1.414 1.414L8.586 10l-6.293 6.293a1 1 0 1 0 1.414 1.414L10 11.414l6.293 6.293A.998.998 0 0 0 18 17a.999.999 0 0 0-.293-.707L11.414 10z\\"
              ></path></svg
          ></span>
        </button>
      </div>
    </div>
    <div
      class=\\"Polaris-Frame__ContextualSaveBar Polaris-Frame-CSSAnimation--startFade\\"
    ></div>
    <main
      class=\\"Polaris-Frame__Main\\"
      id=\\"AppFrameMain\\"
      data-has-global-ribbon=\\"false\\"
    >
      <a id=\\"AppFrameMainContent\\" tabindex=\\"-1\\"></a>
      <div class=\\"Polaris-Frame__Content\\">
        <div class=\\"Polaris-Layout\\">
          <div class=\\"Polaris-Layout__Section\\">
            <div class=\\"Polaris-Card\\">
              <div class=\\"Polaris-Card__Section\\">
                <div
                  class=\\"Polaris-SkeletonBodyText__SkeletonBodyTextContainer\\"
                >
                  <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  <div class=\\"Polaris-SkeletonBodyText\\"></div>
                </div>
              </div>
            </div>
            <div class=\\"Polaris-Card\\">
              <div class=\\"Polaris-Card__Section\\">
                <div class=\\"Polaris-TextContainer\\">
                  <div
                    class=\\"Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall\\"
                  ></div>
                  <div
                    class=\\"Polaris-SkeletonBodyText__SkeletonBodyTextContainer\\"
                  >
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class=\\"Polaris-Card\\">
              <div class=\\"Polaris-Card__Section\\">
                <div class=\\"Polaris-TextContainer\\">
                  <div
                    class=\\"Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall\\"
                  ></div>
                  <div
                    class=\\"Polaris-SkeletonBodyText__SkeletonBodyTextContainer\\"
                  >
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class=\\"Polaris-Layout__Section Polaris-Layout__Section--secondary\\"
          >
            <div class=\\"Polaris-Card\\">
              <div class=\\"Polaris-Card__Section\\">
                <div class=\\"Polaris-TextContainer\\">
                  <div
                    class=\\"Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall\\"
                  ></div>
                  <div
                    class=\\"Polaris-SkeletonBodyText__SkeletonBodyTextContainer\\"
                  >
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  </div>
                </div>
              </div>
              <div class=\\"Polaris-Card__Section\\">
                <div
                  class=\\"Polaris-SkeletonBodyText__SkeletonBodyTextContainer\\"
                >
                  <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  <div class=\\"Polaris-SkeletonBodyText\\"></div>
                </div>
              </div>
            </div>
            <div class=\\"Polaris-Card Polaris-Card--subdued\\">
              <div class=\\"Polaris-Card__Section\\">
                <div class=\\"Polaris-TextContainer\\">
                  <div
                    class=\\"Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall\\"
                  ></div>
                  <div
                    class=\\"Polaris-SkeletonBodyText__SkeletonBodyTextContainer\\"
                  >
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  </div>
                </div>
              </div>
            </div>
            <div class=\\"Polaris-Card Polaris-Card--subdued\\">
              <div class=\\"Polaris-Card__Section\\">
                <div class=\\"Polaris-TextContainer\\">
                  <div
                    class=\\"Polaris-SkeletonDisplayText__DisplayText Polaris-SkeletonDisplayText--sizeSmall\\"
                  ></div>
                  <div
                    class=\\"Polaris-SkeletonBodyText__SkeletonBodyTextContainer\\"
                  >
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                    <div class=\\"Polaris-SkeletonBodyText\\"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  <div id=\\"PolarisPortalsContainer\\">
    <div data-portal-id=\\"popover-Polarisportal-16\\"></div>
    <div data-portal-id=\\"modal-Polarisportal-17\\"><div></div></div>
    <div data-portal-id=\\"modal-Polarisportal-10\\"><div></div></div>
    <div data-portal-id=\\"Polarisportal-11\\">
      <div class=\\"Polaris-Frame-ToastManager\\" aria-live=\\"polite\\"></div>
    </div>
    <div data-portal-id=\\"modal-Polarisportal-50\\"><div></div></div>
    <div data-portal-id=\\"modal-Polarisportal-51\\"><div></div></div>
    <div data-portal-id=\\"modal-Polarisportal-52\\"><div></div></div>
    <div data-portal-id=\\"modal-Polarisportal-53\\"><div></div></div>
  </div>
</div>
"
`;

exports[`[ASADMIN-2487] fill coverage should render the App correctly 2`] = `""`;
