import {render, waitFor} from '@testing-library/react';
import {format} from 'prettier';
import React from 'react';

import App from '../App';

jest.mock('../utils/day', () => ({
	...jest.requireActual('../utils/day'),
	dateToString: () => '20200808',
}));

jest.mock('react-stripe-elements', () => ({
	...jest.requireActual('react-stripe-elements'),
	StripeProvider: ({children}: {children: any}) => children,
}));

jest.mock('@shopify/polaris', () => ({
	...jest.requireActual('@shopify/polaris'),
	SkeletonPage: ({children}: {children: any}) => children,
}));

jest.mock('aftershipBillingUi', () => ({
	...jest.requireActual('aftershipBillingUi'),
	useBillingPlans: () => ({
		closeModal: () => {},
		openModal: () => {},
	}),
}));
jest.mock('@aftership/analytiker-editor', () => ({
	useAnalytiker: () => ({
		routeEntries: [],
		routeLoaders: [],
	}),
}));
jest.mock('@aftership/comments', () => ({
	__esModule: true,
	default: jest.fn().mockReturnValue(() => true),
}));

window.matchMedia = jest.fn().mockImplementation(query => {
	return {
		matches: false,
		media: query,
		onchange: null,
		addListener: jest.fn(),
		removeListener: jest.fn(),
	};
});

describe('[ASADMIN-2487] fill coverage', () => {
	it.skip('should render the App correctly', async () => {
		const {container, getByRole} = render(<App />);
		const orgSwitcher = await waitFor(() => getByRole('org-switcher'));

		expect(format(container.innerHTML, {parser: 'html'})).toMatchSnapshot();

		expect(
			format(orgSwitcher.innerHTML, {parser: 'html'})
		).toMatchSnapshot();
	});
});
