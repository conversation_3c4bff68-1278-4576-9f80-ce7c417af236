import React from 'react';
import ReactDOM from 'react-dom';
import SpinLoading from 'components/SpinLoading';

ReactDOM.render = jest.fn();

window.gtag = jest.fn();

xdescribe('application initialization unauthenticated', () => {
	it('should call  ReactDOM.render() with SpinLoading', async () => {
		await import('../index');
		expect(ReactDOM.render.mock.calls.length).toBe(1);
		expect(ReactDOM.render.mock.calls[0][0]).toStrictEqual(
			<SpinLoading />,
			null
		);
	});
});
