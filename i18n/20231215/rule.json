{"ADD_PRODUC_ea6fc": "Add products", "CUSTOM_PRO_0e12a": "Custom processing time", "PROCESSING_50778": "Processing time", "THIS_WILL_df924": "This will override your warehouses default processing time for these products.", "AUTOMATIC_7816f": "Automatic selection", "MANUALLY_S_c85cd": "Manual selection", "PRODUCTS_068f8": "Products", "CREATE_RUL_ccee2": "Create rule", "SHOW_ALL_eb920": "Show all", "SHOW_LESS_c74ea": "Show less", "DEFAULT_PR_d9efe": "Default processing time: {{count}} business {{day}}", "ORDER_PROC_2e372": "Order processing rules", "NO_SHIP_FR_26137": "No ship-from locations added yet", "YOUR_SHIP_5bbef": "Your ship-from locations are missing a default order processing time.", "DEFAULT_7a192123": "<PERSON><PERSON><PERSON>", "RULE_NAME_a4257hk231": "Rule name", "ANY_PRODUC_eb3b1": "Any products currently following this rule will follow your warehouses default processing time instead.", "SELECT_AN_9ee9a": "Select an order processing time", "SELECT_A_C_dffd2": "Select a carrier pickup date", "THIS_RULE_fa30e": "This rule will only apply to products you select individually.", "THIS_RULE_3c300": "All current and future products labeled with a particular product type will automatically follow this rule.", "CARRIER_PI_06074": "Carrier pickup date", "DATE_HAS_E_b7dfc": "Date has expired", "SOME_RULES_cbd65": "Some rules’ actions can not be applied due to actions expired", "SOME_RULES_34970": "Some rules’ actions can not be applied due to invalid location", "ANY_PRODUC_b9394": "Any products following this rule will follow your warehouses default processing time instead.", "BUSINESS_D_ef388": "business day(s)", "INPUT_A_NU_d80ae": "Input a number of days", "SELECT_PRO_11ac8": "Select product types", "IF_A_PRODU_08638": "If a product appears in both a manual and automated selection, the rule with the automated selection will take effect.", "COUNT_18396": "{{count}} business {{day}}", "DISABLE_bcfac": "Disable", "UNSAVED_CH_c227a": "Unsaved changes", "DISABLE_RU_17e3f": "Disable rule", "THE_PRODUC_93ebf": "The products in this rule will no longer follow this rule.", "ALL_PRODUC_30ebd": "All products not in product-specific rules.", "DEFAULT_SH_3b9ab": "Default ship-from location", "OTHER_LOCA_bd57c": "Other locations", "CUSTOM_SHI_d4f05": "Custom shipping rules", "ALL_PRODUC_40dec343": "All products", "SHIP_FROM_7e0ae": "Ship-from location", "SET_DEFAUL_4f468": "Set default shipping service", "EDIT_DEFAU_6863f": "Edit default shipping service", "CHANGE_LOC_5722b": "Change location", "CREATE_ZON_52db7": "Create zone", "EDIT_ZONE_7374e": "Edit zone", "CREATE_686e6": "Create", "DELETE_ORD_4e4b7": "Delete order processing rule?", "CREATE_CUS_38334": "Create custom shipping rule", "EDIT_CUSTO_9ec9e": "Edit custom shipping rule", "DELETE_CUS_06f05": "Delete custom shipping rule?", "ALL_PRODUC_e89ce": "All products from this rule will follow your general shipping rules or any other custom shipping rule that applies to them.", "ADD_A_SHIP_66fb0": "Add a ship-from location for this rule", "SHIPPING_Z_aff2a": "Shipping zones", "NO_ZONES_C_6fda1": "No zones created yet", "EDIT_SHIPP_d059a": "Edit shipping service", "ADD_SHIPPI_fd45d": "Add shipping service", "CREATE_A_Z_0ce39": "Create a zone for this ship-from location", "ADD_A_SHIP_0b711": "Add a shipping service for this zone", "CHOOSE_WHI_17d2b": "Choose which products will follow this rule", "ZONE_NAME_a9f4e": "Zone name is required", "THIS_NAME_ca716": "This name already exists", "ALL_ZONES_a4aa2": "All zones not covered by other ship-from locations", "YOUR_PRODU_7c2b2": "Your product pages can only show one EDD. This setting tells our AI which method to use when calculating an EDD for product pages", "THIS_SHIP_ce075": "This ship-location was deleted", "THIS_SHIP_86907": "This ship-from location has been deactivated", "YOU_MUST_S_a0d45": "You must set up the order processing settings and full address for this ship-from location at <lnk>Organization > Locations</lnk>.", "YOU_MUST_S_7fef4": "You must set up the order processing settings for this ship-from location at <lnk>Organization > Locations</lnk>.", "YOU_MUST_P_7eae0": "You must provide the full address for this ship-from location at <lnk>Organization > Locations</lnk>.", "UNSUPPORTE_efdb1": "Unsupported carrier service", "ABOVE_CARR_85a33": "Above carrier and service type will used for product page EDD calculation", "PRODUCT_PA_19b73": "Product page shipping method", "THIS_RULE_09114": "This rule is disabled as the ship-from location has been deactivated. You can reactive it at <lnk>Organization > Locations</lnk>.", "ALL_PRODUC_5e8d0": "All products not in custom shipping rules", "EDIT_GENER_e9706": "Edit general shipping rules", "GENERAL_SH_a59d8": "General shipping rules", "YOU_MUST_S_82f6d": "You must set a default ship-from location first.", "SET_A_DEFA_b465e": "Set a default ship-from location", "SET_UP_YOU_08835": "Set up your default shipping service", "YOU_MUST_A_74ec5": "You must add a default shipping service for your default ship-from location.", "SOME_RULES_49579": "Some rules wont apply because their ship-from location has been deactivated.", "ALL_ZONES_04f64": "All zones not covered by other locations", "SHIPPING_M_83d89": "Shipping method", "SERVICE_TY_641b9": "Service type", "CARRIER_91441": "Carrier", "YOU_MUST_S_4b3a4": "You must set up the default processing time at Organization > Locations first", "PRE_PURCHA_e28ae": "Pre-purchase EDDs are not being calculated", "IMPROVE_TH_7b8e7": "Improve the shopping experience and boost conversions by showing customers an estimated delivery dates while they shop.", "PRE_PURCHA_7ec51": "Pre-purchase", "CHECKOUT_E_c806b": "Checkout EDD (Shopify Plus)", "REDUCE_PUR_2c71c": "Reduce purchase anxiety and abandoned carts by showing an estimated delivery date for all your shipment methods at checkout.", "PROVEN_TO_b515a": "Proven to influence a customer’s purchase decision, show customers an estimated delivery date and give them the confidence to buy.", "REDUCE_QUE_1acf5": "Reduce queries from customers about their delivery by displaying an estimated delivery date in emails, SMS, and on your tracking page.", "ORDER_TRAC_c65ff": "Order tracking EDD", "SHOW_CUSTO_ab31e": "Show customers an EDD in tracking notifications and on your tracking page, even when the carrier fails to provide one.", "YOUR_DEFAU_a6f18": "Your default ship-from location has been deactivated. <lnk1>Reactivate it</lnk2> or <lnk2>set a new default location</lnk2>.", "YOU_MUST_S_2ca6a": "You must set up the order processing settings and full address for your default ship-from location at <lnk>Organization > Locations</lnk>.", "YOUR_DEFAU_13b12": "Your default ship-location was deleted. You must <lnk>set a new default location</lnk>.", "A_RULE_WIT_e9e06": "A rule with this name already exists", "PRODUCT_TY_0e0f3": "Product types", "RULE_UPDAT_3ca2e": "Rule updated", "RULE_CREAT_1dabc": "Rule created", "SET_UP_YOU_568a4": "Set up your ship-from location and rules first", "SET_A_DEFA_27644": "Set a default shipping service", "CHANGE_ZON_871ec": "Change zone", "WORLDWIDE_5982a": "Worldwide", "YOU_NEED_T_7893e": "You need to set up your default ship-from location and default shipping rule to use pre-purchase EDDs.", "SET_UP_NOW_af88f": "Set up now", "DEFAULT_PR_9d2b6": "Default processing time: ", "YOUR_DEFAU_7fa3c": "Your default ship-location was deleted. You must set a new default location.", "YOUR_DEFAU_82k424": "You must set up the order processing settings for your default ship-from location at <lnk>Organization > Locations</lnk>.", "SOME_RULES_b4a4a": "Some rules wont apply because their ship-from location or carrier service is not supported by pre-purchase EDDs.", "SEARCH_BY_432ec": "Search by product or variant name", "PRODUCT_TY_3d19a": "Product type", "CONNECT_SH_6ba07": "Connect Shopify store", "BOOST_SALE_63b7c": "Boost sales, build trust, and outshine your competition by showing customers an estimated delivery date throughout the customer journey.", "DELIVERY_D_68a41": "Delivery dates powered by AI", "SELECT_A_S_45280": "Select a shipping method", "NO_RESULTS_e576c": "No results found", "IN_OTHER_R_eced3": "in other rule", "THE_PROCES_3a06f": "The processing time for different products and locations allows our AI to calculate more accurate pre-purchase EDDs.", "PRE_PURCHA_f7e13": "Pre-purchase EDDs cant be calculated as your default ship-from location or carrier service is not supported.", "RULE_PRIOR_60327": "Rule priority:  Manual selection > Automatic selection > All products", "RULE_PRIOR_a2c6f": "Rule priority: Manual selection > Automatic selection", "TO_CALCULA_a16cc": "To calculate pre-purchase EDDs, our AI needs to know where and how you ship your products.", "SHIPPING_R_568ba": "Shipping rules", "SOME_RULES_71518": "Some rules won't fully apply because of a problem with their ship-from location.", "COVERED_BY_1432c": "Covered by another location", "IN_OTHER_R_37641": "In other rule", "SEARCH_BY_3cd80": "Search by product type", "YOU_MUST_C_6f413": "You must connect a Shopify store to use pre-purchase EDDs.", "YOUR_LNK_3394b": "Your <lnk>default shipping rule</lnk> has an unsupported carrier service.", "YOUR_LNK_75718": "Your <lnk>default shipping rule</lnk> has an unsupported ship-from location", "YOUR_STORE_c7844": "Your store has no products", "SHOW_THE_E_8470c": "Show the earliest EDD", "SELECT_DAT_75319": "Select date", "A_RULE_THA_f0869": "A rule that applies to all products already exists", "CREATE_ORD_bc98a": "Create order processing rule", "EDIT_ORDER_a967a": "Edit order processing rule", "PRODUCT_DE_6253e": "Product deleted", "THE_SHIPPI_8b3b5": "The shipping method was deleted", "ADD_LOCATI_93640": "Add location", "NOT_SUPPOR_039ba": "Not supported by pre-purchase EDDs. <lnk>Download list</lnk> of supported carriers.", "AEDD_CUSTO_c1a97": "Custom-added carrier", "SHOW_AN_ED_de534": "Show an EDD on your store to boost conversions, and in shipment tracking notifications to reduce support queries."}