{"OUR_AI_COV_00e84": "Our AI covers 101 worldwide carriers. <lnk1>View carriers</lnk1> or <lnk2>view regions</lnk2>. If you have any questions, you can <lnk3>contact us</lnk3>.", "SHOWING_cf905": "Showing {{length}} regions", "POST_PURCH_fe206": "Post-purchase estimated delivery dates", "REDUCE_CUS_0e469": "Reduce customer queries, boost trust in your brand, and track the performance of your carriers with AfterShip's EDDs.", "LEARN_HOW_589c2": "Learn how our EDDs help you and your customers, and how our our AI can improve the accuracy and coverage.", "HOW_CAN_AF_96c99": "How can AfterShip EDDs actually help?", "DISMISS_c8a59": "<PERSON><PERSON><PERSON>", "SOURCE_PRI_db23a": "Source priority", "CARRIERS_S_bfbe3": "Carriers supported by AI Predictive EDD", "REGIONS_SU_f77af": "Regions supported by AI Predictive EDD", "WHEN_SHOWI_8eb0c": "When showing an EDD, we pick the first available source from this list. You can change a sources priority by dragging it up and down the list.", "A_CUSTOM_E_b44c5": "A custom EDD can always be calculated. This means any sources with a lower priority in the list will never be used.", "SHOW_CUSTO_b6da7": "Show customers an EDD, even when the carrier fails to provide one.", "RECOMMENDE_1f24d": "Recommended. A range has increased accuracy which means happier customers.", "OUR_AI_UTI_e01a2": "Our AI utilizes years of tracking date to calculate the most likely day for delivery.", "SINGLE_DAT_beabf": "Single date", "DATE_RANGE_08ee1": "Date range", "DATE_FORMA_4ba8b": "DATE FORMAT", "DISABLE_AI_dffef": "Disable AI Predictive EDD", "OUR_AI_WIL_79f35": "Our AI will no longer calculate EDDs for shipments.", "AI_PREDICT_6195f": "AI Predictive EDD disabled", "AI_PREDICT_90c0b": "AI Predictive EDD enabled", "AI_PREDICT_59edb": "AI Predictive EDD", "PROVIDE_CU_26ec9": "Provide customers with more accurate EDDs thanks to our AI-powered engine.", "MANAGE_34e34hw2": "Manage", "SET_AI_PRE_842b5": "Set AI Predictive EDD format", "MANUALLY_S_25fc5": "Manually set the EDD based on the transit times for different regions and carriers.", "DISABLE_CU_b249c": "Disable Custom EDD", "WE_WILL_NO_6076d": "We will no longer calculate a custom EDD for shipments.", "EDD_SOURCE_8491c": "EDD sources", "AFTER_SHIP_69836": "AfterShip EDD sources", "GET_THE_MO_1d693": "Get the most out of your EDDs", "WE_RECOMME_963c1": "We recommend checking out our short guide on where AfterShip EDDs appear for you and your customers.", "CONTROL_TH_436f0": "Control the source priority", "YOU_CAN_NO_f24c6": "You can now choose which EDD source you would prefer to show customers, and which to use as fallbacks.", "EASILY_CON_47632": "Easily control missed EDDs", "TO_AVOID_C_7fd12": "To avoid customer anxiety when an order is late, weve added custom settings that let you manually override the EDD.", "MISSED_EDD_021c3": "Missed EDDs", "REMEMBER_T_6bd35": "Remember to set which days count as <strong>carrier business days</strong>.", "WHEN_A_SHI_ab984": "When a shipment hasn’t been delivered and the EDD hasn’t updated, you can decide what info to show customers.", "SHOW_THE_M_917a0": "Show the missed EDD", "ADD_EXTRA_338b0": "Add extra business days to the missed EDD", "ENTER_A_WH_e3369": "Enter a whole number of 1 or higher", "WHEN_A_SHI_e9ebe": "When a shipment has not been delivered and the EDD hasn't updated, we will add your selected number of business days to that missed EDD", "SETTINGS_U_c88wk84": "Settings updated", "CARRIER_ED_33b49": "Carrier EDD", "PROMISED_D_dbee7": "Promised delivery date", "PROVIDE_BY_68cf8": "Provide by manual", "PROVIDED_B_bfdc7": "Provided by customer", "PROVIDE_BY_6b647": "Provide by carrier", "PROVIDE_BY_52929": "Provide by AI", "WITH_OVER_08bb3": "With over a decade of data to learn from, our AI can calculate EDDs with outstanding accuracy.", "PRE_PURCHA_4428a": "Pre-purchase estimated delivery dates", "POST_PURCH_341ea": "Post-purchase EDD", "PRE_PURCHA_5c8e9": "Pre-purchase EDD", "THIS_IS_TH_41fb9": "This is the priority of sources used for this shipments current EDD. If you change the priority, it will only take effect when the EDD next updates.", "EXTRA_BUSI_6cb65": "Extra business days were added to the missed EDD based on your settings", "EXTRA_BUSI_e40b1": "extra business days", "WITH_AN_ED_b5f18": "With an EDD on your tracking page, anxious customers never need to ask you where their order is ever again.", "AFTER_SHIPS_3d80e": "AfterShip's automated shipment notifications keep customers in the loop by highlighting the EDD.", "WITH_THE_S_e6426": "With the shipments dashboard you can quickly and easily see when a shipment is due to be delivered.", "WEVE_MADE_b3b15": "We've made managing carrier accountability easy with data showing who actually delivered on time according to their original EDD.", "IF_YOU_WAN_97818": "If you want to improve the accuracy of EDDs, try our AI Predictive EDD. With over a decade of tracking data to learn from, it can generate an EDD with outstanding accuracy."}