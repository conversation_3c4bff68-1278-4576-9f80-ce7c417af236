{"DATA_SHOWN_ebd19": "Data shown is based on the original promised EDD from carriers or your <lnk>Estimated delivery dates</lnk>.", "SHIPMENTS_f6604": "Shipments with an unknown carrier service or location info are not displayed separately, but are included in the totals.", "ON_TIME_SH_67fec": "On-time shipments", "EXPORT_56424": "Export", "BY_STATE_a41ee": "By state", "BY_COUNTRY_a41ee": "By country/region", "BY_ORIGIN_ed94f": "By origin carrier", "BY_DEST_a41ee": "By destination carrier", "SHIPMENTS_15ede": "Shipments", "EDD_COVER_c5103": "EDD coverage", "ON_TIME_31774": "On-time %", "ON_TIME_ff2b3": "On-time", "AS_EXPECT_a6906": "As expected %", "AS_EXPECT_dd6ac": "As expected", "EARLY_8e023": "Early %", "EARLY_f2f2f": "Early", "LATE_47c8c": "Late %", "LATE_d6312": "Late", "LANE_56966": "Lane", "CARRIER_3dfe7": "Carrier", "TOTAL_NUMB_fa1ce": "Total number of shipment (not limited to shipment with EED)", "ON_TIME_RA_5eb28": "On time rate ≥ 85% is considered good performance. Lower than 85% is considered bad performance.", "EARLY_80528": "Early", "LATE_ed838": "Late %", "LATE_32b59": "Late", "LANE_0743d": "Lane", "CARRIER_53e92": "Carrier", "SHIPMENTS_b67da": "Shipments", "EDD_COVER_fb857": "EDD coverage", "ON_TIME_99ed4": "On-time %", "ON_TIME_f0c22": "On-time", "AS_EXPECT_168a9": "As expected %", "AS_EXPECT_937a9": "As expected", "EARLY_a4e01": "Early %", "VIEW_DETAI_121ff": "View details", "EXPORT_CSV_75edd": "Export CSV", "BY_LANE_a41ee": "On-time shipment by lane", "BY_CARRIER_a41ee": "On-time shipments by carrier service", "NO_DATA_df991": "No data", "NO_DATA_f1f11": "No data", "EXPORT_TO_9f785": "Export to CSV", "EXPORT_a5bc3": "Export", "CANCEL_ddc44": "Cancel", "DATA_WILL_3d5fb": "Data will be exported to CSV based on the date, location, and carrier filters you've applied.", "CSV_DOWNLO_8307c": "CSV downloaded", "COLUMNS_36c93": "Columns", "SHOW_99076": "Show ({{length}})", "LEAST_1_a3307": "5 least on-time carriers", "LEAST_2_a3307": "5 least on-time lanes", "CARRIER_a3307": "Carrier", "LANES_a3307": "Lanes", "ON_TIME_a3307": "On-time %", "SET_UP_EST_297a8": "Set up estimated delivery dates", "ONLY_04543": "Only <strong>{{eddCoverageRate}}</strong> of your shipments have an estimated delivery date. Configure estimated delivery dates for your shipments to improve data efficiency.", "HEADER_1_b7fcf": "Carriers / Service", "HEADER_2_b7fcf": "Total number of shipment (not limited to shipment with EED)", "HEADER_3_b7fcf": "Shipments", "HEADER_4_b7fcf": "EDD coverage", "HEADER_5_b7fcf": "On time rate ≥ 85% is considered good performance. Lower than 85% is considered bad performance.", "HEADER_6_b7fcf": "On-time %", "HEADER_7_b7fcf": "On-time", "HEADER_8_b7fcf": "As expected %", "HEADER_9_b7fcf": "As expected", "HEADER_10_b7fcf": "Early %", "HEADER_11_b7fcf": "Early", "HEADER_12_b7fcf": "Late %", "HEADER_13_b7fcf": "Late", "ON_TIME_SH_aa8f8": "On-time shipments by carrier service", "VIEW_DETAI_c087c": "View details", "EXPORT_TO_10c72": "Export to CSV", "EXPORT_9566f": "Export", "CANCEL_5bc15": "Cancel", "DATA_WILL_ac1d1": "Data will be exported to CSV based on the date, location, and carrier filters you've applied.", "CSV_DOWNLO_98cfb": "CSV downloaded", "DELIVERY_b1796": "Delivery performance", "SAME_5bcc5": "Same as expected", "DAYS_b584f": "{{gap}} day(s) late", "DAYS_5bcc5": "{{gap}} day(s) early", "DELIVERY_P_b29f3": "Delivery performance", "IT_SHOWS_Y_9fe41": "It shows your early, late, or on-time shipments based on the first recorded delivery attempt.", "SAME_AS_EX_a4935": "Same as expected", "EARLY_b53f9": "Early", "LATE_02ddd": "Late", "DIRECT_33fbf": "Direct", "SEARCH_EN_6ccf9": "Search Engine", "UNION_ADS_a8a72": "Union Ads", "VIDEO_ADS_8565e": "Video Ads", "EMAIL_92ad8": "Email", "ON_TIME_b7551": "On-time: ", "TRENDING_O_71937": "Trending on-time: ", "LATE_9926e": "Late: ", "TRENDING_L_f6c00": "Trending late: ", "OVERDUE_72057": "Overdue: ", "ON_TIME_SH_2b529": "On-time shipments over time", "ON_TIME_aa6c1": "On-time", "TRENDING_O_6e4bc": "Trending on-time", "OVERDUE_e5e4f": "Overdue", "LATE_0fa76": "Late", "TRENDING_L_e6587": "Trending late", "ON_TIME_S_1a6e0": "On-time status distribution", "ON_TIME_S_78297": "On-time status distribution", "ON_TIME_ST_b2455": "On-time status distribution", "PERCENTAG_8a228": "Percentage of shipments delivered or having a delivery attempt before the original promised EDD", "ON_TIME_DA_ab6ca": "On-time data", "ON_TIME_DA_0919b": "On-time data", "ON_TIME_RA_d8390": "On-time rate", "SHIPMENTS_18c31": "Shipments with EDD", "LATE_SHIPM_76b64": "Late shipments", "OVERDUE_SH_92d29": "Overdue shipments", "EDD_MAP_1_a4955": "All EDD", "EDD_MAP_2_a4955": "Carrier EDD", "EDD_MAP_3_a4955": "AI Predictive EDD", "EDD_MAP_4_a4955": "Custom EDD", "LOCATION_2_7a17b": "Location ({{totalCount}})", "LOCATION_1_7a17b": "Location", "CLEAR_2a8f3": "clear", "CLEAR_be716": "clear", "CARRIER_2_a4955": "Carrier / Service ({{totalCount}})", "CARRIER_a4955": "Carrier / Service", "CLEAR_09356": "clear", "CLEAR_7e9ba": "clear", "APPLY_908a9": "Apply", "ON_TIME_S_u81a": "On-time shipments by lane", "ON_TIME_S_uva1": "On-time shipments by lane", "BY_STATE_skq81": "By state", "BY_COUNTRY_aju81": "By country/region", "CARRIER_aa81": "Carrier / Service", "ON_TIME_sjzuq": "On-time shipments by carrier service", "ON_TIME_sj71": "On-time shipments by carrier service", "SHIPMENTS_WITH_1u81": "Shipments with an unknown carrier service or location info are not displayed separately, but are included in the totals.", "EXPORT_ai81": "Export CSV", "BY_ORIGIN_aji1": "By origin carrier", "BY_DESTINATION_81a1f": "By destination carrier", "ORIGIN_ajg81": "Origin", "SHIP_whkxuj8ba": "Shipments", "EDD__gmr4uwnym": "EDD coverage", "ON_T_ms1trfzmf5": "On-time %", "ON_T_gg1cqbhx": "On-time", "AS_E_bzjuf2u7x": "As expected %", "AS_E_witmse9ndw": "As expected", "EARL_6klw8bhlgq": "Early %", "EARL_svot8wjhjz": "Early", "LATE_n3dqrof56": "Late %", "LATE_v1fmjjlvd0": "Late"}