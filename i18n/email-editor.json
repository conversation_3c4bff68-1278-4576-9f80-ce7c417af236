{"INSERT_MER_19e25": "Insert merge tags", "AUTOMIZELY_3d212": "Automizely", "OWNER_8a03b": "Owner", "OTHER_ORGA_13d7d": "Other organization members ({{length}})", "YOU_NEED_T_e17a2": "You need to select a member first", "MANAGE_MEM_a1f6d": "Manage member", "MANAGE_MEM_5ad1d": "Manage member", "SAVE_7a1a6": "Save", "MEMBER_d8c74": "member", "MEMBERS_d8c74": "members", "VIEW_DETAI_262f2": "View details for {{id}}", "PLEASE_TR_dd964": "Please try again.", "FAVICON_028af": "favicon", "FAILED_TO_fb0c9": "Failed to add image:", "REMOVE_187d6": "Remove", "UNLOCK_PRO_2e3a3": "Unlock Pro features", "UPGRADE_TO_ab7fe": "Upgrade to the Pro plan or higher to unlock this feature.", "UNLOCK_PRE_bc484": "Unlock Pre features", "UPGRADE_TO_dab81": "Upgrade to the Premium plan or higher to unlock this feature.", "SUBJECT_908123": "Subject: {{subject}}", "POWERED_BY_29a74": "Powered by AfterShip", "CLICK_TO_R_e946e": "Click to remove", "SWITCH_1080123": "Switch to HTML editor", "SWITCH_10801239911": "Switch to drag-and-drop email editor", "SAVE_27879": "Save", "CANCEL_3aff7": "Cancel", "SEND_TEST_641af": "Send test email", "ADD_b749b": "Add", "SAVE_TEMP_02880": "Save template changes before continue", "THE_EMAIL_cb03a": "The email templates are not saved yet. Do you wish to save it before continue?", "SAVE_CO_d49d4": "Save & Continue", "CANCEL_07781": "Cancel", "SWITCH_123123": "SWITCH LANGUAGE", "SAVE_0908123": "Save template changes before continue", "THE_EMAIL_1231": "The email templates are not saved yet. Do you wish to save it before continue?", "SAVE_0091231": "Save & Continue", "ADD_REMOVE_94c35": "Add/remove", "SET_DEFAUL_69c4e": "Set default language", "SAVE_eaf17": "Save", "CANCEL_112bf": "Cancel", "DEFAULT_LA_756c9": "Default language", "PLEASE_ADD_dd0ee": "Please add languages first and then set the default language of email editor.", "UPGRADE_AN_8e6c9": "Upgrade and unlock", "CHANGES_09821323": "Changes that you made may not be saved.", "REMOVE_8b984": "Remove {{name}}", "ARE_YOU_SU_c7d8d": "Are you sure you want to remove {{name}}?", "ADD_REMOVE_aea58": "Add/remove languages", "DEFAULT_LA_75355": "Default language", "AFTER_SHIP_a768b": "AfterShip will auto-send multilingual emails to your customers based on the order language. If the order language doesn’t match any languages in the list, we will use your default language.", "SET_DEFAUL_2317d": "Set default language", "LANGUAGES_bd21f": "Languages", "PLEASE_EDI_6ee0b": "Please edit the template content using the language you add.", "DEFAULT_7ee8e": "<PERSON><PERSON><PERSON>", "SETTINGS_00822": "Settings", "CONTENT_091231": "Content", "CONTENT_C09812": "Content config", "STYLES_09123": "Styles", "STYLES_C12081": "Styles config", "DISPLAY_61a0c": "Display", "STANDARD_W_fb58e": "Standard web fonts", "CUSTOM_WEB_8e17d": "Custom web fonts", "FONT_14372": "Font", "FONT_NAME_b84b7": "Font name", "ONLY_LIMIT_f70dc": "Only limited email clients support custom font.", "E_G_NOTO_d3459": "E.g. 'Noto Sans', sans-serif", "FONT_STYLE_a3d4b": "Font stylesheet url", "E_G_HTTPS_3409f": "E.g. https://fonts.googleapis.com/css2?family=.....", "COLORS_66515": "Colors", "EMAIL_07cd0": "Email", "BACKGROUND_dd093": "Background", "CONTENT_BA_fce80": "Content Background", "TEXT_a1e9a": "Text", "TITLE_aedca": "Title", "DESCRIPTIO_f4d07": "Description", "BUTTON_ebd65": "<PERSON><PERSON>", "TEXT_6bf4a": "Text", "BACKGROUND_3065b": "Background", "HEADER_eb1ee": "Header", "TEXT_0e896": "Text", "BACKGROUND_92cb5": "Background", "FOOTER_70164": "Footer", "TEXT_15f22": "Text", "BACKGROUND_43944": "Background", "HTML_5c470": "HTML", "CONTENT_1890a": "content", "EMAIL_CONT_388b6": "Email content", "THIS_IS_A_4856a": "This is a template for transactional emails", "TRANSACTIO_77bfb": "Transactional emails do not require an unsubscribe link. Only check this box if this template are not marketing related.", "EMAIL_CONT_bb675": "Email content", "NOTE_ONL_5a099": "Note: Only HTML <body> is supported for now.  Other elements like <header>, <img> and <footer> will not be displayed accurately.", "FOOTER_40a8a": "Footer", "DESCRIPTIO_c1cfe": "Description", "FOOTER_PAD_d1b1c": "Footer padding", "TOP_7f82a": "Top", "BUTTOM_10396": "<PERSON><PERSON>", "LEFT_93c6d": "Left", "RIGHT_16502": "Right", "LOGO_5a410": "Logo", "LESS_THAN_fe518": "Less than 2MB. Accepts .jpg, .png, jpeg and .gif.", "STORE_NAME_eac5a": "Store name", "HTTPS_WW_c33ff": "https://www.yourstore.com", "STORE_NAME_63b2f": "Store name will only show when you don’t have a logo for your store.", "DESTINATIO_36931": "Destination URL", "HTTPS_WW_e488a": "https://www.yourstore.com", "STYLE_d79ef": "Style", "ALIGNMENT_2c57e": "Alignment", "SIZE_74e4d": "Size", "TEXT_722cb": "Text", "DESCRIPTIO_61ba4": "Description", "IMAGE_01555": "Image", "SUGGEST_1231": "Suggested dimensions: 600 × 300 (min height);", "LESS_TH_1o23": "Less than 2MB; Accepts .jpg, .jpeg, .gif, .png.", "IMAGE_WIDT_e4e74": "Image width", "DESTINATIO_faea5": "Destination URL", "HTTPS_WW_8c706": "https://www.yourstore.com", "TEXT_7904c": "Text", "HEADER_3ec0d": "Header", "DESCRIPTIO_eec2c": "Description", "BUTTON_eb28d": "<PERSON><PERSON>", "TEXT_25c97": "Text", "DESTINATIO_36629": "Destination URL", "HTTPS_WW_63286": "https://www.yourstore.com", "IMAGE_94ce2": "Image", "IMAGE_WIDT_3cc68": "Image width", "DESTINATIO_4e5d9": "Destination URL", "HTTPS_WW_95241": "https://www.yourstore.com", "STYLE_169b0": "Style", "SEQUENCE_f70e0": "Sequence", "DELETE_89953": "Delete", "NAME_9aead": "Name", "LINK_73839": "Link", "HTTPS_WW_7a314": "https://www.yourstore.com", "ADD_LINK_3d91e": "Add Link", "TEXT_5da1b": "Text", "DESTINATIO_12807": "Destination URL", "HTTPS_WW_901df": "https://www.yourstore.com", "TIPS_THIS_f9fbe": "Tips: This block supports Shopify/ Magento-2/ Magento-1/ BigCommerce stores by now, we’ll show items info of the tracking, if the tracking has no item, will not show this block.", "HEADER_4b18a": "Header", "ITEMS_79563": "Items", "STYLE_f1a51": "Style", "IMAGE_SHAP_f6906": "Image shape", "IMAGE_SIZE_61f8b": "Image size", "STYLE_f144c": "Style", "PRICE_TYPE_ce754": "Price type", "STORE_a2072": "Store", "TO_USE_AFT_0cb91": "To use AfterShip product recommendations, you need to <lnk>connect your store</lnk>.", "STORE_38dca": "Store", "NOTE_DUE_ad452": "Note: Due to compatibility display issue, Outlook users may not be able to view this content in their emails.", "TEXT_93e3b": "Text", "TITLE_b50b1": "Title", "DESCRIPTIO_72c76": "Description", "ENTER_TEXT_0e589": "Enter text", "MAX_NUMBE_8c688": "Max. number of products", "THE_NUMBER_a5b9f": "The number of products displayed may be less due to inventory or customer behavior, etc.", "AI_RECOMM_e45e6": "AI recommendations", "PRODUCT_C_8fc56": "Product collection", "MANUAL_SE_73bd7": "Manual selection", "AI_RECOMM_46924": "AI recommendations", "MANUAL_SE_f15b4": "Manual selection", "PRODUCT_SE_28755": "Product settings", "RECOMMENDA_b2e9a": "Recommendation type", "COLUMNS_cd6a5": "Columns", "IMAGE_SHAP_a7227": "Image shape", "RECTANGLE_12313": "Rectangle", "SQUARE_12313": "Square", "CONTENT_1e892": "Content", "SHOW_PRODU_96fc3": "Show product name", "SHOW_PRODU_b7193": "Show product price", "SHOW_COMPA_a7676": "Show compare at price", "ACTION_BUT_6cf71": "Action button", "PRODUCT_57118": "Product", "ADD_PRODUC_a36e0": "Add products", "NOTE_OUT_61ec7": "Note: Out of stock products will not be displayed.", "BEST_SELLE_1eadd": "Best sellers", "INSPIRE_CU_c4366": "Inspire customers by showcasing what’s hot in your store right now.", "FREQUENTLY_44574": "Frequently bought together", "RECOMMEND_89e13": "Recommend products related to the customer's last purchase or the products currently viewed.", "API_814ba": "API", "LOGO_9ea2f": "{{platform}} logo", "STORE_06055": "Store", "CONNECT_ST_4d22a": "Connect store", "ALL_PRODU_6e3ef": "All products", "COLLECTION_dc99c": "Collection", "HOME_PAG_1237e": "'Home page' is a default collection, if it doesn’t exist in your store, we’ll choose ‘All products’ instead.", "ADD_PRODUC_abffc": "Add products", "THE_PRODUC_c0de4": "The product is out of stock now thus cannot be selected.", "CANNOT_091809231": "Cannot exceed the max quantity (18)", "SELECTED_98908123": "{{length}} selected products", "ADD_9012": "Add", "IMAGE_132": "Image with text", "IMAGE_123123": "Image", "TEXT_123123": "Text", "BUTTON_098912": "<PERSON><PERSON>", "TEXT_LI1231": "Text links", "SHIPMENT_09123": "Shipment items", "PRODUCT_091809283": "Product recommendation", "SECTIONS_d7788": "Sections", "ADD_8d38b": "Add", "HEADER_4d316": "Header", "IMAGE_WIT_d8d55": "Image with text", "IMAGE_5bf45": "Image", "TEXT_c4b60": "Text", "FOOTER_8ed80": "Footer", "BUTTON_df210": "<PERSON><PERSON>", "TEXT_LINK_ec98d": "Text links", "PRODUCT_R_aa5e5": "Product recommendations", "ADD_BLOCK_ed72c": "Add blocks", "ORDER_ITEMS_7123": "Order items", "SHIPMENT_ITEMS_1671h1": "Shipment items", "NEW_75740": "New", "GET_ACCESS_c23f2": "Get access to premium features", "UPGRADE_YO_4d35d": "Upgrade your plan to customize the delivery notification template and enjoy other premium features.", "UPGRADE_AN_557bd": "Upgrade and unlock", "EMAIL_SUBJ_cd9c4": "Email subject", "EMAIL_SUBJ_388ca": "Email subject line", "SEND_TO_f0357": "Send to", "SUBMITTED_d331f": "Submitted successfully", "TALK_TO_OU_0f72a": "Talk to our product expert and arrange a demo.", "CONTACT_SA_80f08": "Contact sales", "SHOPIFY_a074b": "Shopify ({{shopifyUrl}})", "PLACE_HOLDE_b43be": "PlaceHolder", "PAYMENT_CA_82a9b": "Payment card", "SECURE_FOR_0501e": "Secure form", "ADD_PAYMEN_729dc": "Add payment method", "NAME_OF_CA_d26ca": "Name of card", "ADD_PAYMEN_3dc70": "Add payment method", "LEFT_DAYS_2342": "{{leftDays}} days left on your free trial. Ready to choose a plan?", "COMPARE_PLANS_23424": "Compare plans", "YOU_ONLY_234": "You only have {{leftDays}} days left on your Enterprise trial. Contact sales to set up your plan.", "CONTACT_SALES_1231": "Contact sales", "CREDIT_CARD_23424": "Credit card is expiring soon.", "UPDATE_2342342": "Update payment method", "CREDIT_CARD_2098": "Credit card has expired. Your account may be suspended soon.", "PLEASE_PAY_98080": "Please pay overdue invoice before {{date}} to avoid losing access to premium features.", "VIEW_INVOI_230982": "View invoices", "PLEASE_PAY_0721": "Please pay overdue invoice to avoid losing access to premium features.", "YOUR_UNPAID_098123": "Your unpaid amount is over $10,000, please <button><span>contact support/span></button> service for assistance.", "PLEASE_PAY_7711": "Please pay overdue invoice before {{date}} to avoid losing access to premium features.", "PLEASE_PAY_7782342": "Please pay overdue invoice before {{date}} to avoid losing access to AfterShip's custom domain.", "PLEASE_PAY_77824": "Please pay your overdue invoice now to continue using AfterShip’s custom domain.", "PAY_NOW_123": "Pay now", "PLEASE_ADD_2342": "Please add payment method first", "EXPERTS_6a352": "experts", "SCHEDULE_A_8498d": "Schedule a live demo", "OUR_PRODUC_f56ad": "Our product experts would like to offer you 1 on 1 service to create the perfect plan and save you money. Let us know when you're available.", "CHOOSE_DAT_e1ab2": "Choose date & time", "HOURS_2ed6d": "HOURS", "MINS_c213a": "MINS", "SECS_b90dc": "SECS", "EMPTY_7fd76": "empty", "SOMETHING_aeca9": "Something went wrong", "REFRESH_TH_7c72a": "Refresh the page and try again.", "REFRESH_PA_5efa5": "Refresh page", "CONTACT_SU_345d2": "Contact support", "SELECT_A_D_15095": "Select a date range to import shipments. New shipments will auto-sync after the first import.", "FULFILLED_5ae2c": "Fulfilled within", "WELL_INCL_4b986": "We’ll include orders created within", "UPGRADE_77a0c": "Upgrade", "QUOTA_EXCE_74941": "Quota exceeded by {{quota}}", "UPDATE_PER_e82d8": "Update permissions", "NOT_NOW_1871a": "Not now", "UPDATE_SHO_65b49": "Update Shopify store permission", "SHOPIFY_36aae": "shopify", "UPDA_b9ec3": "🙆‍♀️ Update Shopify permission to enjoy the new feature product recommendation for free! You can now enable product recommendation on your branded tracking page. Generate additional revenue and drive more traffic back to your store.", "IF_YOU_HAV_b5d0f": "If you have connected multiple stores, you need to update the permissions for each store individually. Please <lnk>contact us</lnk> if you have any questions.", "SWITCHED_T_a6bbc": "Switched to {{name}} template", "SEND_A_TES_4597f": "Send a test email", "SEND_cb25d": "Send", "CANCEL_912c7": "Cancel", "EMAIL_1268a": "Email", "TRY_IT_97627": "Try it", "NOT_NOW_7c803": "Not now", "DRAG_AND_D_5dcdb": "Drag and drop email editor", "TRY_EDITIN_f1ac8": "Try editing the template using the new drag and drop email editor to boost engagement rates.", "DRAG_AND_D_8f028": "Drag and drop email editor", "SWITCHING_79520": "Switching to {{modeTitle}} email editor", "SWITCH_24f1b": "Switch", "CANCEL_3f048": "Cancel", "ARE_YOU_SU_93cfe": "Are you sure you want to discard all unsaved changes?", "DEFAULT_9123": "Default language set to {{name}}.", "SWITCHED_77621": "Switched to {{name}} template", "NICE_TO_7123": "Nice to meet you!", "YOUR_ORDER_1231": "Your order has been delivered", "EMAIL_T_89123": "Email template saved", "LANGUAGE_7123123": "Language removed.", "SEND_A_912": "Send a test email", "SEND_1209": "Send", "CANCEL_98123": "Cancel", "EMAIL_7721": "Email", "EASY_EMAIL_a91jh": "Easy email editor", "DRAG_AND_DROP_ai9p1": "Drag-and-drop email editor", "HTML_E_16a7v": "HTML email editor", "CONTACT_is91jf": "Contact sales", "STORE_9yqnmcnfr": "Store", "STORE_8othuirlty": "Store name", "STORE_84lyxchwb0": "Store URL", "COURI_thlurltyt": "Courier", "COURI_heq2hbmtig": "Courier", "DESTI_pbktesdl4w": "Destination Carrier", "COURI_ascfb8khxo": "Courier contact", "COURI_s71ssaimj0": "Courier redirect URL", "SHIPM_wh09g51fzt": "Shipment", "TITLE_got6hgnrbw": "Title", "TRACK_rjmrw3fqot": "Tracking number", "TRACK_noop3vuwb": "Tracking URL", "TRACK_v2umnydwtk": "Tracking link", "DIREC_p4vjsadzzl": "Direct tracking URL", "DIREC_us5tafc5o": "Direct tracking link", "STATU_srexcsso": "Status", "DETAI_gezrljmpsgx": "Detailed status", "LOCAT_keoh3qge2u2": "Location", "LAST__ftixleczu": "Last update time", "EXPEC_r7mxfo5hfns": "Expected delivery", "PICKU_ndyeck3rg0": "Pickup location", "SHIPP_dhquez1xlbf": "Shipping address", "SIGNE_qqpuvnopom0": "Signed by", "SHIPM_eeekyts31": "Shipment type", "DELIVERYPR_af1fc": "Delivery proof URL", "ORDER_6r5muohmpb": "Order", "ORDER_q15aajpjyr": "Order number", "ORDER_mmuzwdakmxr": "Order ID", "ORDER_f2dzaqqzsh": "Order path", "ORDER_lc4yoigk4qy": "Order URL", "_ORDE_ncck0z1rlfa": " Order status URL", "_ORDE_xsuuozyyzcg": " Order date", "CUSTO_9xoycfspcgh": "Customer name", "FIRST_2vy62xwd": "First name", "NOTE_nlzfuvwd3ow": "Note", "EMAIL_opflwfdqccs": "Email", "EMAIL_5zrehbelbk": "Emails", "TO_EM_nq1g46xlpew": "To email", "SENT__pj0d1dy8506": "Sent from email", "CUSTO_o4pwlybsjo": "Custom field", "ITEM__e3sz9j5zq3": "Item names", "SALES_c0hemy7s4t": "Sales number", "STORE_rk3lgqof2": "Store", "STORE_zwxnghhid": "Store name", "STORE_mse4zivlm": "Store URL", "ORDER_hnku1ikloo": "Order", "ORDER_zbubjjztjg": "Order number", "_ORDE_fp4awpxn8": " Order date", "FIRST_yoaea0xmi": "First name", "FULFILLEDO_bfa1a": "Fulfilled order items", "UNFULFILLE_5afde": "Unfulfilled order items", "CONTENT_asd123da": "content", "SHOW_PRICE_Fhk2313": "show product price", "SHOW_COMPARE_90klw42": "show compare at price", "ORDERITEMS_916b8": "Order items"}