{"ALLOW_CUST_55c4b": "Allow customers to track shipments easily at your site. You can also pre-input tracking numbers to allow tracking in a click.", "SEE_EXAMPL_ead22": "See example", "TRACK_SHIP_e5eb7": "Track shipments of any couriers using public account. Alternatively, you can limit tracking shipments that only exist in your AfterShip account.", "YOU_CAN_FU_7c1b7": "You can further customise the track button using different parameters. Simply modify the parameters in the second set of the codes.", "LOOKUP_MET_25951": "Lookup method of the widget. Can be <code>tracking-number</code> (default), <code>order-number-and-email</code> , or <code>both</code>.", "WEBSITE_DO_6b6d4": "Website domain of the tracking page. Can be <code>your_shortcode.aftership.com</code> (default), <code>track.aftership.com</code>, or custom domain. <lnk>Learn about setting up your custom domain</lnk>.", "TEXT_DISPL_b4530": "Text displayed on the Track button, which must be less than 20 characters. Default is <code>Track</code>.", "BACKGROUND_52095": "Background color of the Track button, which must be a 6-digit HEX number. Default is <code>#f9a31a</code>.", "TEXT_COLOR_d4250": "Text color of the Track button, which must be a 6-digit HEX number. Default is <code>#ffffff</code>.", "SPECIFIES_e413d": "Specifies whether to hide the AfterShip's icon, <code>true</code> or <code>false</code> (default).", "SPECIFIES_da8ce": "Specifies whether to remove ”Powered by AfterShip” wordings from the track button, <code>true</code> or <code>false</code> (default). This feature is available only on AfterShip paid subscription plans.", "SPECIFIES_ee312": "Specifies a carrier code in the AfterShip system. If this parameter does not exist (default), AfterShip will detect the carrier automatically. <lnk>Learn more about carrier detection</lnk>.", "THE_TRACK_ad207": "The Track button is available in 3 sizes: <code>small</code>, <code>medium</code>, and <code>large</code> (default).", "SPECIFIES_87c78": "Specifies whether to make the order lookup widget mobile responsible, <code>true</code> (default) or <code>false</code>.", "THE_WIDTH_fb898": "The width of the track button in px (applicable only if <code>data-responsive</code> is <code>false</code>), e.g. <code>300px</code>.", "PRE_INPUT_a104a": "Pre-input tracking number so customers can track order in one click. Applicable only if the lookup option is <code>tracking-number</code>. <lnk>Learn more about tracking at the order history page</lnk>.", "SPECIFIES_22f5b": "Specifies whether to hide the tracking number. Applicable only if you have a pre-input tracking number.", "SPECIFIES_69326": "Specifies whether to center, <code>true</code> or <code>false</code> (default).", "COPIED_7e4c1": "<PERSON>pied", "CODE_1_32c89": "CODE #1", "COPY_THE_A_d5b53": "Copy the above code and paste it into your page after the <body> tag.", "COPY_9da6a": "Copy", "BUILD_A_TR_f9ece": "Build a tracking page with Automizely Page Builder", "CODE_2_f5d8a": "CODE #2", "COPY_THE_A_98227": "Copy the above code and paste it into the required page between the <body> and </body> tags. The button will appear wherever you place it.", "COPY_4f596": "Copy", "BUILD_A_TR_6acd1": "Build a tracking page with Automizely Page Builder", "SELECT_TRA_0cbc0": "Select tracking page", "PRE_INPUT_87cc8": "Pre-input tracking number", "APPLICABLE_ae22b": "Applicable only if the lookup option is tracking-number", "HIDE_TRACK_926aa": "Hide tracking number", "ADD_ORDER_47fe5": "Add order lookup widget in minutes", "BOOST_CUST_2081a": "Boost customer satisfaction with 1-click order tracking.", "LEARN_MORE_4c3b3": "Learn more", "AFTER_SHIP_6e9be": "AfterShip branding", "UPGRADE_Y_123123": "Upgrade your plan to Premium plan or higher to unlock this feature", "UPGRADE_A_12992": "Upgrade and unlock", "REMOVE_PO_e6c3a": "Remove \"Powered by AfterShip\"", "CUSTOMIZE_c9ae0": "Customize order lookup widget", "LOOKUP_OPT_78367": "Lookup options", "ORDER_NUMB_a0b1b": "Order number and email", "TRACKING_NUMBER_b5621": "Tracking number", "BOTH_b5621": "Both", "LANDING_PA_3b22b": "Landing page URL", "TRACKING_N_6fd88": "Tracking Number", "BUTTON_STY_2d2bd": "Button style", "LABEL_50822": "Label", "BACKGROUND_39e52": "Background color", "TEXT_COLOR_49ae9": "Text color", "ICON_a1f0c": "Icon", "HIDE_ICON_ebfd2": "Hide icon", "SIZE_400dd": "Size", "ALIGN_8dc7e": "Align", "CENTER_5db39": "Center", "PREVIEW_8d99a": "Preview", "INSERT_AFT_78b53": "Insert AfterShip's order lookup widget to allow visitors to get delivery updates faster. Get more happy customers! Learn how to <lnk>add order lookup widget to your store.</lnk>", "GET_CODE_4ee79": "Get code", "CODE_MAY_C_eb4ac": "The codes will update based on the settings you choose.", "ORDER_LOOK_1e1f0": "Order lookup widget", "PARAMETER_fi1a3": "Parameter", "TYPE_a81n1": "Type", "DESCRIPTION_au91j0": "Description", "LARGE_ai91gv": "Large", "MEDIUM_tg61hf": "Medium", "SMALL_d8kg1": "Small", "TRACK_8jf1v": "Track"}