{"UNABLE_TO_02785": "Unable to obtain tracking information for {{totalCount}} Seur shipments", "SEUR_REQUI_72bd1": "Seur requires the postal code and customer phone number to track shipments. You can <lnk1>export the affected shipments</lnk1> to a CSV file, which will be sent to your email. Then, update the shipments by API, or enter the additional info manually here on your shipment dashboard.", "FOR_FUTURE_79b09": "For future Seur shipments, we advise you to include these 2 fields so we can automatically get shipment updates for you.", "UNABLE_TO_ebb1c": "Unable to obtain tracking information for {{totalCount}} Seur shipments", "SEUR_REQUI_0494a": "Seur requires the postal code and customer phone number to track shipments. You can <lnk1>export the affected shipments</lnk1> as a CSV file, which will be sent to your email. Then, <lnk2>update the shipments</lnk2> using the same CSV file, or enter the additional info manually here.", "FOR_FUTURE_2449a": "For future Seur shipments, we advise you to include these 2 fields so we can automatically get shipment updates for you.", "UPDATE_SHI_19390": "Update shipments by CSV", "UPDATE_SHI_5a916": "Update shipment details by CSV", "YOU_CAN_UP_eef33": "You can update the carrier, postal code, customer phone number, and shipment status by CSV.", "EITHER_EXP_cf795": "Either export the shipments you want to update as a CSV first and edit them, or download our <lnk1>CSV template</lnk1>. For more help, check our guide on <lnk2>updating shipments by CSV</lnk2>.", "FILES_MUST_7fbda": "Files must not exceed 5 MB or 5,000 rows.", "SUCCESS_C_297b0": "{{successCount}} shipments were updated.", "SOME_SHIPM_0bd14": "Some shipments could not be updated", "SUCCESS_C_deaf3": "{{successCount}} shipments were updated.", "FAILURE_C_c12a5": "{{failureCount}} shipments could not be updated. Check email for more info.", "UNABLE_TO_4696c": "Unable to update any shipments. Check email for more info.", "SEUR_SHIPM_5b3ad": "Seur shipments require you to <lnk1>add the postal code</lnk1> and <lnk2>add the customer phone number</lnk2> to track them.", "POSTAL_COD_bf8f6": "Postal code", "EDIT_POSTA_22995": "Edit postal code", "POSTAL_COD_fa0b6": "Postal code", "ENTER_POST_0012a": "Enter postal code", "E_G_12345_61895": "E.g. 12345678910", "E_G_12345_f1410": "E.g. 12345678910", "E_G_12345_08c3c": "E.g. 12345678910"}