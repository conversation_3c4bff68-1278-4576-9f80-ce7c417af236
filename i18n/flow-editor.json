{"EDIT_WORKF_0b225": "Edit workflow name", "SAVE_8a609": "Save", "CANCEL_427c7": "Cancel", "NAME_5ebc0": "Name", "ENABLED_faf44": "Enabled", "DISABLED_b13a2": "Disabled", "STATUS_84447": "Status", "ENABLED_28ea1": "Enabled", "DISABLE_28ea1": "Disabled", "THIS_FIEL_a256e": "This field is required", "PLEASE_EN_ca062": "Please enter a valid number", "ENTER_AN_ab6e9": "Enter an integer between 1 and 45", "ADD_FILTER_78188": "Add filter", "TRIGGER_3f605": "Trigger / {{defaultFilterValue}}", "NOTE_SETT_7084b": "Note: Setting changes only apply to the {{readableEventType}}s created after you modify the workflow.", "THIS_WORKFLOW_7084b": "This workflow will be triggered when the {{readableEventType}} status remains {{defaultFilterValue}} for", "DAY_S_006fb": "day(s)", "TRIGGER_08023234": "Trigger fields({{length}})", "TRIGGER_23234": "Trigger fields", "IF_YOU_W_234234": "If you would like only certain {{defaultFilterValue}} statuses to trigger this workflow, add trigger filter(s) here.", "AND_b26de": "And", "SEND_EMAIL_3255b": "Send email", "THE_EMAIL_a96d6": "The email will be sent automatically to your customers when trigger condition is met.", "EMAIL_TEMP_b4d2a": "Email template", "SUBJECT_9f2b8": "Subject: {{templateSubject}}", "EDIT_TEMPL_e8883": "Edit template", "ADD_FILTER_f839e": "Add filter", "TRIGGER_c3510": "Trigger / {{defaultFilterValue}}", "THIS_WORKFLOW_234234": "This workflow will start when {{readableEventType}} status updates to {{defaultFilterValue}}.", "TRIGGER_234324": "Trigger / {{eddStatus}}", "THIS_WORKF_12260": "This workflow will start when the estimated delivery date is {{eddStatus}}.", "TRIGGER_F2_234234": "Trigger fields({{totalCount}})", "TRIGGER_F_234234": "Trigger fields", "IF_YOU_W_234902": "If you would like only certain {{value}} statuses to trigger this workflow, add trigger filter(s) here.", "AND_2c3b5": "And", "PLEASE_CHOOSE_234234": "Please choose at least one {{name}}", "MAXIMUM_23234": "Maximum 50 {{name}} can be selected", "PLEASE_ENTER_23423": "Please enter a valid number", "THIS_FIELD_IS_2342": "This field is required", "ENTER_A_io234": "Enter a valid {{name}}", "THIS_FIEL_8b05c": "This field is required", "DELETE_a2e39": "Delete", "NO_RESULT_282d8": "no result", "NO_RESULT_23423": "No result", "PLEASE_CHO_73cca": "Please choose at least one {{filterName}}", "MAXIMUM_5_1331e": "Maximum 50 choices can be selected", "WHEN_THE_e7fea": "<div1>When the {{readableEventType}} status <text>remains for {{stuckField}} day(s)</text> in</div1><div2>{{value}}</div2>", "TRIGGER_FI_e061b": "Trigger filter ({{length}})", "THEN_059cb": "Then", "SEND_AN_EM_6c22c": "Send an email to customer", "EDIT_EMAIL_bb9de": "Edit email template", "WHEN_THE_ac342": "When the {{readableEventType}} status updates to", "WHEN_THE_E_ea274": "When the estimated delivery date is", "REVISED_259e0": "Revised", "MISSED_47d0e": "Missed", "TRIGGER_FI_25b8a": "Trigger filter ({{length}})", "SAVE_5ccb1": "Save", "CANCEL_0af4b": "Cancel"}