{"REDUCE_PUR_8ef1c": "Reduce purchase anxiety", "75_OF_CUS_e1198": "75% of customers say having an EDD positively influences their purchase decision.", "NO_CODING_0ee45": "No coding needed", "WORKS_WITH_3fe0c": "Works with all Shopify themes, including OS 2.0. Add it to your store in minutes.", "BOOST_CONV_2cb10": "Boost conversions", "BUILD_TRUS_0b4ac": "Build trust and fill more carts by showing shoppers an EDD while the browse.", "BOOST_SALE_c3ef7": "Boost sales, build trust, and outshine your competition by showing an estimated delivery date on product pages and at checkout.", "GET_SHOPIF_437d5": "Get Shopify add-on", "NOT_USING_c7858": "Not using Shopify? Enterprise plan users can add pre-purchase EDDs using <lnk>our API</lnk>.", "ENTERPRISE_b749a": "Enterprise users can also add an EDD to their store using <Lnk1>our API</Lnk1>. If you have an questions, please <lnk2>contact us</lnk2>.", "ADD_ED_DS_T_745b1": "Add EDDs to your Shopify store in 3 simple steps", "IT_TAKES_L_5b5fa": "It takes less than 15 minutes", "YOU_NEED_T_4e29b": "You need to connect your store first to use pre-purchase EDDs.", "STEP_1_SE_8b74b": "Step 1: Set up your ship-from location", "TELL_OUR_A_b5bc6": "Tell our AI where you ship orders from and how long it takes to process an order.", "SET_UP_SHI_7a80d": "Set up ship-from location", "EDIT_SHIP_c2289": "Edit ship-from location", "TELL_OUR_A_a5eda": "Tell our AI which carrier you use by default.", "STEP_2_SE_27259": "Step 2: Set your default shipping rule", "SET_SHIPPI_bb74f": "Set shipping rule", "EDIT_SHIPP_ccd76": "Edit shipping rule", "STEP_3_IN_d1f25": "Step 3: Install widgets on your store", "ADD_AN_EDD_143db": "Add an EDD to your product pages and checkout.", "INSTALL_WI_51b72": "Install widgets", "VIEW_WIDGE_2b963": "View widgets", "SHOWING_AN_1cfdc": "Showing an EDD while customers browse is proven to positively influence their purchase decision. See <lnk>how to install</lnk>.", "BOOST_CONV_e5bc0": "Boost conversions and remove anxiety by showing customers when their order is likely to arrive. See <lnk>how to install</lnk>.", "CHECKOUT_E_c806b": "Checkout EDD (Shopify Plus)", "INSTALL_WI_28a82": "Install widget", "ADD_AS_APP_8750e": "Add as app block", "COPY_WIDGE_537eb": "Copy widget code", "TELL_OUR_A_eb035": "Tell our AI where you ship from, and how long it takes each location to process an order.", "SHIP_FROM_a845a": "Ship-from locations", "TELL_OUR_A_d6dcf": "Tell our AI which carriers and locations you use to handle different orders. You can set individual rules for different regions and products.", "ORDER_PROC_1b0c8": "Order processing rules (optional)", "FOR_PRODUC_4bf28": "For products that have a different processing time, you can set rules that override a locations default processing time.", "HIDE_THE_E_f3b5d": "Hide the EDD for:", "OUT_OF_STO_74191": "Out of stock products", "SPECIFIC_P_01349": "Specific products", "SPECIFIC_P_08d60": "Specific product types", "HIDE_CHECK_9b9ee": "Hide checkout EDDs", "IF_AN_ORDE_febeb": "If an order includes an item with a hidden product page EDD, you can choose whether or not to still show an EDD for the overall order.", "HIDE_THE_C_119cb": "Hide the checkout EDD for the order", "SHOW_A_CHE_59b61": "Show a checkout EDD based on the remaining items EDDs", "HOW_TO_ADD_cc5c1": "How to add pre-purchase EDDs to your store", "CHECK_OUT_c9034": "Check out step-by-step guide on how to set up and add pre-purchase EDD widgets to your Shopify store.", "YOU_MUST_S_fee6a": "You must set up a <lnk1>default ship-from location</lnk1> and <lnk2>default shipping rule</lnk2>.", "YOUR_DEFAU_facd2": "Your default ship-from location has been deactivated. <lnk1>Reactivate it</lnk1> or <lnk2>set a new default location</lnk2>.", "FOR_PRODUC_a244a": "For product page EDDs, our AI needs access to your customers IP address to determine their location. <lnk>Manage access</lnk>", "HOW_TO_INS_318eb": "How to install the EDD widget at checkout", "HOW_TO_INS_9408e": "How to install the EDD widget on product pages", "YOU_CAN_IN_b031c": "You can install as an app block or by copying the pasting the code.", "APP_BLOCK_0b8cc": "App block", "WIDGET_COD_0ac5d": "Widget code", "STEP_1_OP_12f85": "Step: 1 Open the Shopify theme editor", "SELECT_THE_4826f": "Select the Products page, then in the left panel click Add block.", "STEP_2_SE_a30e0": "Step 2: Select the AfterShip Pre-purchase EDD app", "YOULL_FIN_7ba4d": "You'll find it under the \"Apps\" tab. You can drag and drop to change it's position. Click \"Save\" when you're done.", "YOULL_FIND_23ef1": "You'll find it under the Apps tab. You can drag and drop to change its position. Click Save when you're done.", "STEP_1_CO_0ae7d": "Step 1: Copy the widget code", "FROM_THE_W_33e38": "From the Widgets page, click Install widget and choose Copy widget code from the dropdown.", "ON_SHOPIFY_36317": "On Shopify, go to Online store > Themes and click Edit code.", "STEP_2_ED_b8408": "Step 2: Edit your Shopify code", "STEP_3_SE_35b9e": "Step 3: Search for the main-product.liquid file in left column", "STEP_4_SE_06b1b": "Step 4: Search for product-form-installment-", "WE_RECOMME_5af6a": "We recommend pasting the widget code under this line. But you can put it anywhere you like. Click Save when you're done.", "SELECT_THE_8b078": "Select the Shipping page, then in the left panel click Add app block.", "STEP_2_SE_14a72": "Step 2: Select the AfterShip Pre-purchase EDD app block", "AFTER_ADDI_2e9cd": "After adding the block, click Save when you're done.", "STEP_3_SE_0086e": "Step 3: Search the code for checkout.liquid", "STEP_4_PA_ea2cd": "Step 4: Paste the widget code", "PASTE_THE_74645": "Paste the code after </body> and click save when you're done.", "STEP_1_GO_fb012": "Step 1: Go to the <lnk>Shopify theme editor</lnk>", "CHECKOUT_E_865f1": "Checkout EDD", "PRODUCT_PA_d2460": "Product page EDD", "HIDE_EDD_F_e70f2": "Hide EDD for special items", "NEXT_TO_ED_1be22": "Next to EDD", "NEXT_TO_CU_143b4": "Next to customer location", "NO_ICON_2372b": "No icon", "FONT_SIZE_c4943": "Font size", "LINE_HEIGH_153ad": "Line height", "DESTINATIO_89e2c": "Destination text", "COUNTDOWN_70dce": "Countdown timer text", "EDD_TEXT_b7167": "edd text", "BODY_TEXT_bb7d8": "body text", "THE_WIDGET_78970": "The widget will use your store‘s font.", "MAX_FILE_d0c35": "Max. file size: 1 MB. Accepted formats: JPG/JPEG and PNG.", "ICON_IMAGE_23035": "Icon image", "BORDER_2de42": "border", "TOP_BOTTOM_4befb": "Top/bottom", "LEFT_RIGHT_7520d": "Left/right", "BACKGROUND_d229b": "background", "CONTAINER_0e727": "Container", "BOTTOM_2ad9d": "Bottom", "TOP_a4ffd": "Top", "MARGINS_bd326": "<PERSON><PERSON>", "YOU_MUST_S_3b346": "You must set a <lnk>default ship-from location</lnk>.", "YOU_MUST_S_1edb8": "You must set up <lnk>default shipping rule</lnk>.", "ALL_PREVIE_4ead3": "All preview data is an example", "EDD_SETTIN_fe435": "EDD settings", "DATE_FORMA_534fd": "Date format", "EDD_FORMAT_99e6a": "EDD Format", "ICON_LOCAT_5795a": "Icon location", "MESSAGE_4c2a8": "Message", "YOU_MUST_I_b08c6": "You must include the EDD merge tag", "SHOW_CUSTO_6cf70": "Show customers an estimated delivery date to build trust, reduce cart abandonment, and increase conversions. Our powerful Al algorithm factors in different shipping methods, past delivery performance, holidays, traffic trends, and carrier service levels to give customers a precise prediction.", "AUTOMATICA_b23a3": "Automatically calculates an EDD based on your customers IP address", "OVER_90_A_d9cf5": "Over 90% accurate city-to-city EDD predictions", "SUPPORTS_C_e066c": "Supports custom shipping rules based on location and product", "COPY_AND_P_f88a5": "Copy and paste this code to your Shopify product page.", "COPY_AND_P_8da73": "Copy and paste this code to your Shopify checkout page.", "CODE_COPIE_6374f": "Code copied", "COPY_CODE_e2ab9": "Copy code to install", "FOR_PRODUC_418ae": "For product page EDDs, our AI needs access to your customers IP addresses to determine their location. <lnk>Manage access</lnk>", "SOME_RULES_50bd3": "Some rules wont apply because of a problem with their settings.", "FONT_WEIGH_a47c4": "Font weight", "MANAGE_WHI_38512": "Manage which of your locations you fulfill and ship orders from.set up default ship-from location first.", "TO_USE_AI_7a261": "To use AI EDD, You need to set up default shipping rule first.", "SHOPIFY_WI_0fc15": "Shopify will sunset the code embedding at August 13, 2024. This feature will be unavailable from then on.", "PADDING_a1044": "Padding", "UNDERLINE_85272": "Underline", "CONTENT_45685": "CONTENT", "CLOSE_d3d2e": "Close", "CONFIGURAT_254f6": "Configuration", "MANAGE_PRO_8bd7e": "manage products", "PRODUCTS_86024": "products", "PRODUCT_TY_afc50": "product types", "PRE_PURCHA_c6b04": "Pre-purchase EDDs", "ADD_APP_BL_daefa": "Add app block on Shopify checkout page", "GO_TO_SHOP_34fc2": "Go to Shopify", "MANAGE_ACC_323a2": "Manage access", "PRE_PURCHA_e3abe": "Pre-purchase EDDs require access to additional data", "DISABLE_CA_38d1c": "Disable carrier EDD?", "WE_WILL_NO_93547": "We will no longer retrieve any EDD info from your carriers.", "CARRIER_ED_33b49": "Carrier EDD", "THE_ESTIMA_95f73": "The estimated delivery date provided by the carrier."}