{"PAY_PAL_PAC_84d68": "PayPal Package tracking", "DELIVER_A_fa7c2": "Deliver a seamless CX by enabling shoppers to track their orders in the PayPal app. Significantly minimize disputes, simplify resolution, and reduce operational costs.", "INSTALLING_b9940": "Installing...", "GO_TO_SETT_a8808": "Go to settings", "INSTALL_PA_2ebbe": "Install PayPal", "INCLUSIVE_4a50b": "Inclusive proactive tracking experience", "EMPOWER_SH_c9c95": "Empower shoppers to track orders within the PayPal app across multiple touchpoints. Deliver a proactive, device-friendly tracking experience on the go.", "INCREASED_33c09": "Increased delivery success rates", "WITH_TIMEL_f0a15": "With timely notification of shipment statuses, shoppers can better plan for delivery acceptance, thereby improving the delivery success rate.", "FEWER_DISP_63f25": "Fewer disputes and minimized chargebacks", "RELIABLE_O_a143b": "Reliable order tracking sync enhances your account standing and reduces PayPal payment hold times. Centralize tracking data to resolve disputes quickly and minimize chargebacks.", "PAY_PAL_PAC_ee0ba": "PayPal Package tracking benefits both you and your shoppers. Shoppers enjoy seamless order tracking and simplified management in the PayPal app. Meanwhile, you can improve the post-purchase experience, minimize disputes, streamline dispute resolution, and reduce operational costs.", "HOW_DOES_P_64fce": "How does PayPal Package tracking work?", "CONNECT_YO_9ee0c": "Connect your PayPal merchant account to AfterShip.", "SYNCING_BE_38e0e": "Syncing begins automatically, sending relevant order tracking information to your PayPal app.", "ACTIVATE_T_ac9c9": "Activate the “Track with PayPal” button on the order thank you page or in tracking emails for your shoppers.", "WHAT_ARE_T_8503e": "What are the benefits of syncing order tracking information to PayPal?", "WHEN_PAY_PA_25d1f": "When PayPal sees that your store syncs order tracking information quickly and reliably, it improves your merchant account record. This helps reduce the 'account reserve' PayPal holds. Simply put, PayPal holds less of your money as a 'guarantee fund' and releases your funds up to 10 times faster.", "MOST_IMPOR_b08aa": "Most importantly, as your shoppers receive up-to-date tracking information in their PayPal accounts, you can experience up to 90% fewer disputes and chargebacks.", "HOW_DO_I_E_5559b": "How do I enable PayPal Package tracking for my store?", "SIMPLY_INS_d0e3f": "Simply install the PayPal Package tracking add-on above and follow the provided instructions.", "ARE_THERE_5977e": "Are there additional fees to enable PayPal Package tracking?", "THERE_ARE_7dc2f": "There are no extra fees from AfterShip or PayPal for using the PayPal Package tracking feature. However, you will need an active AfterShip Tracking subscription to enable this functionality.", "HOW_LONG_D_e4520": "How long does it take to sync tracking information to PayPal?", "AFTER_AN_O_76d2e": "After an order is fulfilled, AfterShip instantly syncs the tracking information to the connected PayPal account.", "IS_PAY_PAL_d6049": "Is PayPal Package tracking secure?", "AFTER_SHIP_2ac86": "AfterShip Tracking uses PayPal’s secure API, ensuring your order data remains protected. Only shoppers can access their transaction history and order information.", "LAST_30_DA_c2e5c": "Last 30 days (recommended)", "LAST_60_DA_a0364": "Last 60 days", "LAST_90_DA_a369a": "Last 90 days", "SHOW_A_TRA_15b82": "Show a Track with PayPal button below the thank you message.", "SHOW_A_TR_1db77": "Show a \"Track with PayPal\" button below the thank you message.", "SHOW_A_TR_c3764": "Show a \"Track with PayPal\" button above the tracking info.", "SHOW_A_TR_2bdac": "Show a \"Track with PayPal\" button below the confirmation message.", "SHOW_A_TR_e1431": "Show a \"Track with PayPal\" button below the thank you message. Requires the \"PayPal Payments for WooCommerce\" plugin.", "INSTALL_BU_c8ba0": "Install button", "PAY_PAL_CON_ca468": "PayPal connections", "MANAGE_CON_8d2fc": "Manage connections", "CONNECTED_2ec0d": "Connected", "DISCONNECT_ef70e": "Disconnected", "SYNCED_ORD_cad83": "Synced orders", "NEW_ORDERS_86591": "New orders will be automatically synced to PayPal, but you can also", "SYNC_EXIST_81432": "sync existing orders now", "SHOW_LESS_9a917": "Show less connections", "SHOW_ALL_b1fca": "Show all {{count}} connections", "TRACK_WITH_e10e1": "Track with PayPal buttons", "ADDING_TR_f4e13": "Adding \"Track with PayPal\" buttons at key touchpoints allows your customers to review their orders and track their shipments all in one place. These buttons are only shown to customers who pay using PayPal US.", "ALL_YOUR_P_885d5": "All your PayPal accounts were disconnected.", "SYNC_EXIST_8cd49": "Sync existing orders to PayPal", "SYNC_d8e87": "Sync", "SYNC_ALL_O_d7fef": "Sync all orders placed in the:", "PASTE_THE_1dd2a": "Paste the widget code in BigCommerce", "GO_TO_CHAN_e705e": "Go to channel manager", "CLOSE_d3d2e": "Close", "STEP_1_GO_e84c2": "Step 1: Go to your BigCommerce \"Channel Manager\" page", "FROM_YOUR_b1ce5": "From your list of storefronts, click the name of the storefront you want to add this widget to.", "STEP_2_PA_d186d": "Step 2. Paste the code in the script manager", "CLICK_SCR_c4d54": "Click \"Script manager\" in the left panel, then click the “Create a script” button. Fill in the settings as follows:", "ENTER_A_NA_813a6": "Enter a name for the script. We suggest \"PayPal Package tracking\".", "LEAVE_THE_32e5d": "Leave the placement on the default setting. The button will appear below the confirmation message regardless.", "FOR_THE_LO_091f2": "For the location select \"Order confirmation\".", "LEAVE_THE_89a58": "Leave the script category on the default setting.", "FOR_SCRIPT_b461e": "For script type select \"<PERSON><PERSON><PERSON>\" and paste the code you just copied into the text field.", "CLICK_SAV_8deda": "Click \"Save\" at the bottom when done. Remember to place a test order to check that the widget appears properly on your store.", "COPY_THE_W_f9811": "Copy the widget code", "COPY_CODE_1b8b0": "Copy code and continue", "HOW_TO_INS_83b1d": "How to install on your Shopify order status page", "GO_TO_CHEC_e0f74": "Go to checkout editor", "STEP_1_GO_727d5": "Step 1: Go to your Shopify checkout editor", "FROM_THE_D_b7629": "From the dropdown menu at the top of the checkout editor, select your \"Order status\" page.", "STEP_2_AD_3ecd8": "Step 2: Add the \"PayPal Package tracking\" app block", "CLICK_SEC_d5cb6": "Click \"Sections\" in the left panel, then click \"Add app block\" at the bottom of the panel. Select \"PayPal Package tracking\" to add the block to your page.", "RECOMMENDE_3ee48": "Recommended setting: Hide Shopify's \"Track order with Shop\" button", "SHOPIFY_SH_f71a0": "Shopify shows a download link for their Shop tracking app by default. We strongly recommend turning this off so your customers don't get confused.", "GO_TO_YOUR_007f0": "Go to your", "SHOPIFY_SE_eeef9": "Shopify settings", "AND_UNDE_2c930": ", and under the \"Checkout\" section untick the option \"Show a link for customers to track their order with Shop\".", "HOW_TO_INS_01d4b": "How to install on your Shopify thank you page", "FROM_THE_D_71620": "From the dropdown menu at the top of the checkout editor, select your \"Thank you\" page.", "PASTE_THE_adf73": "Paste the widget code in WooCommerce", "GO_TO_PAGE_81ff9": "Go to pages editor", "STEP_1_GO_c0a61": "Step 1: Go to your WooCommerce \"Pages\" editor", "IN_THE_LIS_c843b": "In the list of pages, find your \"Checkout\" page and click \"Edit\".", "STEP_2_PA_314e1": "Step 2. Paste the code in the text field", "PASTE_THE_8a306": "Paste the code you just copied into the text field as shown below. Click \"Update\" in the right side panel when done.", "YOUR_CUSTO_460ec": "Your customers can now track orders directly in the PayPal app. Add a \"Track with PayPal\" button to your tracking pages that appears when customers pay using PayPal US.", "TRACK_WI_0ba87": "*\"Track with PayPal\" is currently only available for US shoppers.", "SHOW_A_TR_5372b": "Show a \"Track with PayPal\" button above the footer of your tracking emails.", "SHOW_A_TR_59403": "Show a \"Track with PayPal\" button on your tracking pages.", "TO_SHOW_A_e751e": "To show a \"Track with PayPal\" button on BigCommerce, you must update permissions for the AfterShip Tracking app on these stores:"}