{"IMPORT_BY_bde68": "Import by CSV", "START_TRAC_34918": "Start tracking your orders", "HERE_YOUR_e1fbc": "Here your shipments are grouped by order, allowing you to easily monitor the order-to-delivery progress.", "THE_INPUT_6e50c": "The input range is between 1-180", "DELETE_ORD_afe3c": "Delete orders", "THIS_MAY_T_eec16": "This may take a while depending on the number of orders.", "SHOW_ALL_1bb74": "Show all ({{count}})", "LOADING_ER_0a73a": "Loading error.", "ORDER_TAGS_e9e4d": "Order tags", "ORDERS_117_726f1": "Orders", "SEARCH_SOU_32203": "Search sources", "SEARCH_STO_bfaca": "Search stores", "DONE_117_6ea11": "Done", "FILTERS_11_97022": "Filters", "EXPORT_ORD_da8cb": "Export orders", "ORDER_NUMB_ada23": "Order number copied to clipboard", "YOU_HAVE_N_f9a84": "You have not provided a tracking number for this shipment.", "ORDER_ID_C_bf141": "Order id copied to clipboard", "THE_ORDERS_b7b03": "The orders have been exported. You will receive an email with a download link in the next 40 minutes.", "ORDER_NOTE_7bfff": "Order notes", "ORDER_NUMB_df35f": "Order number", "ORDER_ID_3b3a5": "Order ID", "FULFILLMEN_5ca01": "Fulfillment status", "PAYMENT_ST_40a72": "Payment status", "NO_OF_ITE_bde62": "No. of items", "NO_OF_SHI_02c84": "No. of shipments", "TOTAL_COL_cdea3": "Total", "STORE_COL_e4cca": "Store", "CUSTOMER_N_ec921": "Customer name", "ORDER_DATE_36588": "Order date", "ORDER_IMPO_0ac96": "Order import date", "LATEST_UPD_d8cfd": "Latest update date", "SHIPPING_M_d4fa3": "Shipping method", "SOURCE_CO_84c6b": "Source", "ORDER_NOTE_889f8": "Order notes", "ORDER_TAGS_7e045": "Order tags", "DESTINATIO_48679": "Destination", "WE_IMPORT_d3f59": "We import new orders and shipments automatically. If you can’t find what you’re looking for, you can re-sync your connections or edit your import settings here.", "DATA_WILL_4afaf": "Data will be exported based on the filters you've applied and sent to your email ({{email}}). To learn what each field in the file refers to, check our help article on <lnk>exporting CSV files</lnk>.", "IMPORT_DAT_fcf88": "Import date", "LATEST_UPD_fe259": "Latest update date", "ORDER_DATE_4f237": "Order date", "ORDER_NUMB_99d1f": "Order number", "STORE_FIL_e35d4": "Store", "ORDER_DATE_78aaa": "Order date", "ORDER_IMPO_85b9b": "Order import date", "LATEST_UPD_94a13": "Latest update date", "FULFILLMEN_85e87": "Fulfillment status", "PAYMENT_ST_15caf": "Payment status", "SHIPPING_M_ebdf4": "Shipping method", "ORDER_TAGS_34c05": "Order tags", "DESTINATIO_a957f": "Destination", "SOURCE_FI_df511": "Source", "DELETE_SEL_cbb57": "Delete selected orders?", "ALL_SELECT_00b94": "All selected orders and their shipments will be deleted from AfterShip. You will not be able to re-import these orders or shipments in future.", "ALL_SELECT_7f06c": "All selected orders will be deleted from AfterShip. You will not be able to re-import them or their shipments in future.", "SEARCH_BY_d6891": "Search by order number, order ID, customer name, tracking number, or order name", "SHIPPING_D_169d7": "Shipping details", "STORE_ORD_98c1e": "Store", "SHIPPING_A_0c458": "Shipping address", "CUSTOMER_5b5c6": "Customer", "ORDERED_8312a": "Ordered: ", "IMPORTED_17f91": "Imported ({{platform}}): ", "VIEW_TRACK_afda8": "View tracking page", "SUBTOTAL_0f994": "Subtotal", "SHIPPING_095ac": "Shipping", "TAX_ORDER_9b683": "Tax", "DISCOUNTS_6a240": "Discounts", "TOTAL_ORD_70eae": "Total", "SHOW_LESS_2b12f": "Show less", "CONNECT_ST_390a6": "Connect store"}