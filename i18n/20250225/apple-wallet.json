{"SHOPIFY_TH_06ecb": "Shopify thank you page", "SHOW_A_TR_9c5e7": "Show a \"Track with Apple Wallet\" button on your Shopify thank you page when the customer is using an Apple device.", "SHOPIFY_OR_5f7dc": "Shopify order status page", "SHOW_A_TRA_8aad1": "Show a \"Track with Apple Wallet\" button on your Shopify order status page when the customer is using an Apple device.", "STEP_2_AD_de233": "Step 2: Add the \"Apple Wallet Order Tracking\" app block", "CLICK_SEC_afcf8": "Click \"Sections\" in the left panel, then click \"Add app block\" at the bottom of the panel. Select \"Apple Wallet Order Tracking\" to add the block to your page.", "STEP_2_AD_c3bd7": "Step 2: Add the \"Apple Wallet Order Tracking\" app block", "CLICK_SEC_504e8": "Click \"Sections\" in the left panel, then click \"Add app block\" at the bottom of the panel. Select \"Apple Wallet Order Tracking\" to add the block to your page.", "RECOMMENDE_3ee48": "Recommended setting: Hide Shopify's \"Track order with Shop\" button", "SHOPIFY_SH_2ef3a": "Shopify shows a download link for their Shop tracking app by default. We strongly recommend turning this off so you're customers don't get confused.", "GO_TO_YOUR_ba1c9": "Go to your <lnk>Shopify settings</lnk>, and under the \"Checkout\" section untick the option \"Show a link for customers to track their order with Shop\".", "INSTALL_BU_c8ba0": "Install button", "CONFIRM_YO_c9bb8": "Confirm your store info", "BEFORE_YOU_68ac4": "Before you install the button, you need to provide the following store information so that orders can appear in Apple Wallet."}