{"SPECIFICEV_c63cc": "Specific events", "CLEARALLEV_9709f": "Clear all events", "ADDFILTERS_517bd": "Add filters", "ADVANCED_12743": "Advanced", "CLEAR_72975": "Clear", "DELIVERYDA_30fa6": "Delivery date", "DATE_d757f": "Date", "LOCATION_dc3c6": "Location", "REASON_b998c": "Reason", "WHERE_f5e52": "where", "SELECTREAS_660a2": "Select reason", "SELECTLOCA_b40ea": "Select location", "SHIPMENTCA_9da5a": "Shipment canceled", "REDELIVERY_64a89": "Redelivery scheduled", "DROPPEDOFF_3776a": "Dropped off at collection point", "MISROUTED_e8978": "Misrouted", "REROUTED_97bca": "Rerouted", "REJECTEDBY_eeed2": "Rejected by recipient", "DELIVEREDT_d4e0b": "Delivered to neighbor", "SIGNEDBYCU_28cf8": "Signed by customer", "DELIVEREDT_3c102": "Delivered to a location", "RETURNEDTO_726bf": "Returned to sender", "HELDBYCUST_19b70": "Held by customs", "DAMAGED_34cbe": "Damaged", "LOST_c42f3": "Lost", "ADDEVENT_6fdb4": "Add event", "NOEVENTSAD_ff90a": "No events added yet", "SHIPMENTUP_979a2": "Shipment update", "CHOOSEFROM_f819e": "Choose from one of our recommended pre-made templates:", "SEARCHCOUN_de10e": "Search countries/regions", "SEARCHSERV_b62b5": "Search service types", "SEARCHCARR_3f067": "Search carriers", "SEARCHTAGS_912b4": "Search tags", "SEARCHEVEN_f0e64": "Search events", "SEARCHSTAT_67463": "Search states", "SEARCHCITI_3c9e5": "Search cities", "SEARCH_SHOP_e4d5c": "Search Shopify tags", "SEARCHSTAT_0c948": "Search statuses", "SEARCHSUBS_4cfe3": "Search sub-statuses", "SEARCHSOUR_ae8e9": "Search sources", "SEARCHSTAT_cf4f7": "Search statuses", "SEARCHREAS_2195e": "Search reasons", "THEINPUTRA_041fd": "The input range is between 0-120", "THEINPUTRA_70b7d": "The input range is between 1-120", "THEINPUTRA_a407c": "The input range is between 0-120", "OTHEREXCEP_bdc26": "Other exception events", "DESTINATIO_5515e": "Destination carrier", "ORIGINCARR_3788a": "Origin carrier", "WEONLYPROV_ecbda": "We only provide 3 out of 13 exception types for shipment filtering currently."}