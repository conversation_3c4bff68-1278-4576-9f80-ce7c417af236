{"APPLE_WALLE_23362": "Apple Wallet Tracking", "ADDTO_APPLE_3900e": "Add to Apple Wallet button", "APPLE_WALLE_ebc4f": "Apple Wallet Order page", "SHOPIFYORD_e7f19": "Shopify order status page", "SHOWAN_ADDT_14556": "Show an \"Add to Apple Wallet\" button on your Shopify order status page when the customer is using an Apple device.", "BRANDEDTRA_8cb75": "Tracking pages", "SHOWAN_ADDT_7906a": "Show an 'Add to Apple Wallet' button on all your AfterShip tracking pages.", "POSITION_7b8fd": "Position", "VIEWINTRAC_6b581": "View in tracking page editor", "VIEWINTRAC_6b582": "View in tracking pages", "BENEATHTHE_24be2": "Beneath the header", "ABOVETHEFO_874f3": "Above the footer", "EMAILNOTIF_d8995": "Email notifications", "SHOWAN_ADDT_6af69": "Show a \"Track with Apple Wallet\" button in the footer of your tracking emails (in-store pickup emails currently not supported).", "ABOVEEMAIL_a4067": "Above email footer", "VIEWINEMAI_794e6": "View in email editor", "VIEWINSMS_ab32c": "View in SMS editor", "VIEW_IN_NOTIFICATION_FLOWS_8b8a9": "View in notification flows", "BETWEENSMS_cf3a6": "Between SMS custom text and opt out text", "SM_SNOTIFIC_715f7": "SMS notifications", "SHOWAN_ADDY_12380": "Show a \"Track with Apple Wallet\" link in your tracking SMS (in-store pickup SMS currently not supported).", "STOREINFOR_61d99": "Store information", "FORORDERST_09cd3": "For orders to appear correctly in Apple Wallet, you need to provide the following info for your stores.", "ENTERYOURS_fdc82": "Enter your store name", "ENTERYOURS_ff9fc": "Enter your store URL", "ENTERANORD_2b616": "Enter an Order management page URL", "STORENAME_c2c12": "Store name", "STORELOGOO_39641": "Store logo (optional)", "STORE_URL_29161": "Store URL", "ORDERMANAG_9de52": "Order management page URL", "CONTENT_c50d5": "Content", "EDITYOURST_a65b3": "Edit your store's appearance and links within Apple Wallet.", "DIMENSIONS_6b271": "Suggested dimensions: 500 x 500 pixels. Max. file size: 1 MB. Accepted formats: JPG/JPEG and PNG.", "ORDERSOURC_13540": "Order source:", "CONNECTSTO_8ca21": "Connect store", "APPLE_WALLE_5b898": "Apple Wallet Order Preview", "WITHINTHES_43905": "Within the subscription section", "WITH_ORDER_T_a8c88": "With Order Tracking, your customers can now track orders directly from Wallet. Add an 'Add to Apple Wallet' button to your tracking pages that appears when customers use an Apple device.", "CUSTOMERSU_69848": "Customer support email address", "AUR_LWHERET_cc5bb": "A URL where the customer can manage the order.", "CUSTOMERSU_ea718": "Customer support phone number", "CONTACTPAG_a9c57": "Contact page URL", "PROVIDEALI_92125": "Provide a link to the page where customers can talk directly to a support agent, or where they can find contact info for customer support", "PROVIDEALI_d8743": "Provide a link to the page on your store where customers can manage their order", "NOSTORECON_08341": "No store connection", "YOURSTOREH_e0e95": "Your store has been disconnected from AfterShip. Please reconnect your store to sync orders.", "INCOMPLETE_06b0d": "Incomplete store information", "YOUMUSTPRO_ce6a1": "You must provide all required information for the following stores to allow customers to add orders to Apple Wallet:", "CONNECTSTO_7fa39": "Connect store", "TOSAVETHES_02139": "To save these settings, the following information is required:", "INTHE_SUBSC_80405": "In the 'Subscription' section", "APPLE_ORDER_84469": "Apple Order Tracking", "ALLOWCUSTO_6aa13": "Allow customers with iPhone and iPod Touch to add orders directly to Apple Wallet App. Provides them a seamless order tracking experience and improve your brand awareness.", "ENABLE_bab29": "Enable", "ADDORDERTO_8484f": "Add order to Apple Wallet", "HERESSOMED_27032": "Here’s some description of this value.", "NATIVEORDE_b06bf": "Native order tracking experience", "HERESSOMED_2fc46": "Here’s some description of this value.", "HYPERBRAND_39416": "Hyper brand awareness", "HERESSOMED_fe738": "Here’s some description of this value.", "ABOUT_THIS_074f9": "About this plugin", "TOALLOWORD_cb55b": "To allow orders from this store to be added to Apple Wallet, you must provide the following information:", "YOUVEENABL_67ae0": "You've enabled the \"Track with Apple Wallet\" link in SMS notifications.", "EDITSETTIN_51d5a": "Edit settings", "APPLE_WALLE_a6c37": "Apple Wallet Order Tracking button", "EDITSETTIN_691ef": "Edit settings", "PROVIDEALI_7af64": "Provide a link to the page on your store where customers can manage their order", "APPLE_WALLE_6ac11": "Apple Wallet Order Tracking benefits you and your customers. Shoppers can easily add orders to Apple Wallet and enjoy timely order tracking and simplified order management. With timely shipment statuses, shoppers can better plan for delivery acceptance, resulting in a higher delivery success rate.", "DESCRIPTIO_ddf06": "Description", "POWEREDBY_A_32e31": "Powered by Apple inc.", "HOWDO_IENAB_19938": "How do I enable Apple Wallet Order Tracking for my store?", "ALLMERCHAN_a7e43": "All merchants can enable Apple Wallet Order Tracking through AfterShip Tracking's admin portal.", "IFYOUALREA_adf26": "If you already have an AfterShip account, log in to your admin portal and install the Apple Wallet Order Tracking add-on.", "IFYOUARENE_dac01": "If you are new to AfterShip Tracking, subscribe to one of AfterShip Tracking's plans, connect it to your eCommerce store, and then install the Apple Wallet Order Tracking add-on.", "AFTERINSTA_17a0f": "After installing Apple Wallet Order Tracking, you can configure it through AfterShip Tracking for your Shopify order status page, branded tracking page, email, and SMS.", "ARETHEREAD_03f72": "Are there additional fees to enable Apple Wallet Order Tracking?", "NEITHER_AFT_79ee6": "Neither AfterShip nor Apple charges additional fees for the Apple Wallet Order Tracking feature. However, an AfterShip Tracking subscription is required to enable Apple Wallet Order Tracking.", "HOWDOSHOPP_f0a98": "How do shoppers add orders to Apple Wallet?", "THEREARETW_bb458": "There are two ways for shoppers to add orders to Apple Wallet after purchase:", "CLICKTHE_AD_160c2": "Click the 'Add Order to Wallet' button to add an order to Apple Wallet on the order confirmation page, order tracking page, post-transactional customer emails, or SMS order updates.", "ORDERSMADE_ff155": "Orders made using Apple Pay at eligible merchants are automatically added to Apple Wallet.", "IS_APPLE_WAL_0fdec": "Is Apple Wallet Order Tracking private?", "YES_ONLYTHE_7d502": "Yes. Only the shopper has access to the transaction history and the order information provided by the merchant/AfterShip. The information is processed and stored on the shopper's iPhone, ensuring privacy and security."}