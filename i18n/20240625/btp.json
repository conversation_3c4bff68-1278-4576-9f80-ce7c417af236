{"LOOKUP_PAG_0d47f": "Lookup page", "IN_STORE_P_59608": "In-store pickups", "SUPPORTED_0feaf": "Supported order types", "YOU_CAN_CU_012b3": "You can customize the page design for shipments and in-store pickup orders separately.", "UNABLE_TO_4f644": "Unable to unselect", "YOUR_TRACK_d9f66": "Your tracking page must support at least one order type.", "REMOVE_N_7cbcd": "Remove {{name}}?", "ANY_UNSAVE_1213d": "Any unsaved changes to the page design for this order type will not be saved.", "LOOKUP_ed9d8": "LOOKUP", "SHIPMENT_S_53e28": "SHIPMENT STATUS", "PRE_SHIPME_7cada": "PRE-SHIPMENT STATUS", "ERROR_STAT_fbe92": "ERROR STATUS", "PROCESSING_64356": "Processing", "CANCELED_0e22f": "Canceled", "PICKED_UP_86924": "Picked up", "READY_FOR_5336b": "Ready for pickup", "STATUS_5f241": "STATUS", "ORDER_DELI_15646": "Order delivery methods", "SELECT_WHI_be43f": "Select which types of orders you support. You can customize the page design for each type separately.", "DEVICES_c03ca": "Devices", "STATUSES_61fe9": "Statuses", "PRE_SHIPME_fba65": "PRE-SHIPMENT", "SHIPMENT_a8d8b": "SHIPMENT", "TRACKING_U_42682": "Tracking updates", "STATUS_NAM_5cd05": "Status names and descriptions", "EDIT_7dce1": "Edit", "ERROR_STAT_460a7": "Error status names and descriptions", "FAILED_ATT_cccd7": "Failed attempt", "IN_STORE_P_879cf": "In-store pickups have no tracking number, so you must allow customers to find their order using the order number.", "TABS_TO_SW_ee9cc": "Tabs for other items in order", "SECTION_TY_df3f3": "Section type", "ESTIMATED_e1c79": "Estimated pickup date", "PROGRESS_B_5c62d": "Progress bar", "MAP_46f3e": "Map", "ENABLED_00d23": "Enabled", "DISABLED_b9f5c": "Disabled", "STAGES_8a59e": "Stages", "LINE_4803e": "Line", "TYPE_a1fa2": "Type", "MAP_MARKER_1c84a": "Map marker icon", "TITLE_b78a3": "Title", "PROGRESS_B_8e4a5": "Progress Bar", "TEXT_1cb25": "text", "STATUS_NAM_69a31": "Status name", "RIGHT_92b09": "Right", "LEFT_945d5": "Left", "PICKUP_INS_7a994": "Pickup instructions", "HOW_DO_L_P_0bdd8": "How do I pick up my order in store?", "IN_STORE_P_71c24": "In-store pickups", "COMPACT_OR_cb7cc": "Compact order info", "ORDER_UPDA_4b870": "Order updates", "WIDGETS_fa076": "Widgets", "HEADER_AND_89c60": "Header and footer", "OTHER_ITEM_fb305": "Other items in order", "CONTACT_SA_0d7b1": "Contact sales to unlock this feature.", "UNAVAILABL_453e6": "Unavailable", "GIFT_TRACK_55a65": "Gift tracking", "RESULTS_PA_dbefc": "Results page", "SHIPMENT_I_738ff": "Shipment items", "PAGE_NAME_c0a1f": "Page name", "PAGE_NAME_5b617": "Page name can only include letters, numbers, hyphens, and underscores.", "TO_DISPLAY_aa6f0": "To display pre-shipment statuses, you must <lnk1>connect your store</lnk1> or use the <lnk2>AfterShip Commerce API</lnk2>"}