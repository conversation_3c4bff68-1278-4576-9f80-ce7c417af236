{"NOT_SUPPOR_bebee": "Not supported by pre-purchase EDDs.", "BUSINESS_D_b4338": "Business days is required", "TIMEZONE_I_92ff6": "Timezone is required", "ORDER_CUTO_0ff00": "Order cutoff time is required", "ORDER_PROC_b7715": "Order processing time is required", "ANY_CHANGE_d4675": "Any changes you make here will update this locations settings for all AfterShip products and features. You can also manage location settings at <lnk>Organization -> Locations</lnk>", "IN_ANOTHER_44387": "In another zone", "ALREADY_SE_760c6": "Already set for this location", "ONLY_SHOW_f059f": "Only show carriers supported by pre-purchase EDDs", "SEARCH_SER_e3835": "Search service types", "RECENTLY_U_042be": "Recently used", "NEED_A_CAR_22847": "Need a carrier we don't support yet?", "CONTACT_US_02d44": "Contact us", "PROCESSING_e27a2": "Processing time:", "LOCATION_be946": "Location:", "ORDER_CUTO_35cab": "Order cutoff time:", "BUSINESS_D_c0faa": "Business days:", "YOU_MUST_A_e858d": "You must add a carrier service.", "ADD_CARRIE_e991e": "Add carrier service", "CREATE_SHI_2c532": "Create shipping zone", "YOU_MUST_A_72ef3": "You must add a shipping zone.", "ADD_SHIP_F_b0796": "Add ship-from location", "YOU_MUST_A_3bc84": "You must add a ship-from location.", "CHECK_AFFE_806d1": "Check affected rules", "ADD_CARR_1023b": "+ Add carrier service", "LNK_THIS_81fd0": "<lnk>This ship-from location</lnk> has been deactivated", "EDIT_LOCAT_429f8": "Edit location settings", "APPLIES_TO_22de7": "Applies to all orders not covered by custom shipping rules.", "LET_OUR_AI_5aa79": "Let our AI create additional shipping rules", "OUR_AI_IS_1eb90": "Our AI is analyzing your orders...", "THIS_MIGHT_902c4": "This might take a while.", "OUR_AI_CAN_c029b": "Our AI can analyze orders from the past 30 days to learn how you ship your products. It then automatically creates shipping rules for any products not covered by custom rules", "NO_AI_SHIP_aa419": "No AI shipping rules created", "MAKE_SURE_40d8f": "Make sure that all your orders from the past 30 days have been imported to Tracking.", "PRODUCT_VA_99941": "Product variants covered by AI shipping rules: ", "DEFAULT_CA_71885": "Default carrier service", "NO_CARRIER_78c08": "No carrier service added", "SET_DEFAUL_34178": "Set default shipping rule", "SET_UP_b1915": "Set up", "EDIT_DEFAU_f145a": "Edit default shipping rule", "EDIT_SHIPP_30fd8": "Edit shipping zone", "THESE_RULE_e2a36": "These rules apply to specific ship-from locations or products. Rule priority:  Manual selection > Automatic selection > All products", "RULE_ENABL_9acb7": "Rule enabled", "RULE_DISAB_45cf3": "Rule disabled", "YOU_MUST_C_203f3": "You must correct all errors first", "YOU_HAVE_U_99c34": "You have unsaved changes", "TRY_UNCHEC_e15e7": "Try unchecking the supported by pre-purchase EDD filter or changing the search term.", "ALTHOUGH_Y_7cb79": "Although you might offer multiple shipping methods, Shopify product pages can only show the EDD from one of them.", "EDD_PREFER_2ac4a": "EDD Preference", "SHOW_THE_E_77e39": "Show the EDD for my fastest shipping method", "SHOW_THE_E_802b6": "Show the EDD for my slowest shipping method"}