{"TEXT_9dffb": "Text", "IMAGE_WITH_3cdc0": "Image with text", "IMAGES_WIT_48a71": "Images with text", "HEADER_MES_0376b": "Header message", "FOOTER_MES_e69f8": "Footer message", "ADD_BLOCK_2ff80": "Add block", "TYPE_OR_PA_47042": "Type or paste URL", "CONTENT_f15c1": "Content", "SECTION_TY_df3f3": "Section type", "TEXT_OVER_d523d": "Text over image", "SEPARATE_I_6aa80": "Separate image and text", "ADD_BUTTON_6741e": "Add button", "DESCRIPTIO_b5a7a": "Description", "SWITCH_bbc15": "Switch", "CANCEL_ea478": "Cancel", "IMAGE_RATI_0cdc1": "Image ratio", "IMAGE_POSI_9b93b": "Image position", "TEXT_POSIT_91202": "Text position", "BACKGROUND_a9ded": "Background", "COMBINE_ME_dabe3": "Combine media with messaging", "IMAGES_WIT_b2c65": "Images with text is a flexible section that allows you to add text next to, or over, animage. You can also add a CTA button.", "BACK_0557f": "Back", "SAY_ANYTHI_b6dcf": "Say anything, with style", "THE_NEW_T_a40d6": "The new \"Text\" section can be used to addheadings, quotes, FAQs, and more. It supportsboth rich text and markdown for bettercustomization.", "CHANGE_TO_89f26": "Change to rich text", "CHANGE_TO_c2def": "Change to html", "CHANGE_TO_e8ee9": "Change to rich text?", "SOME_FORMA_8aeeb": "Some formatting settings may be lost so remember to review the content again after changing.", "CHANGE_TO_66936": "Change to html?", "SOME_FORMA_753bd": "Some formatting settings may cause the content to exceed the character limit. If this happens, the text will be truncated, so remember to review the content again after changing.", "CHANGE_f4ec5": "Change", "ORDER_TRAC_11035": "Order tracking", "OTHERS_52ef9": "Others", "ACCEPTED_F_24c5c": "Accepted formats: JPG/JPEG, GIF, and PNG."}