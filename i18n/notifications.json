{"DATE_RANGE_bf6dc": "Date range", "STARTING_affdc": "Starting", "ENDING_cb4f9": "Ending", "CANCEL_4db5e": "Cancel", "APPLY_ff7ba": "Apply", "ALL_2e725": "All", "EMAIL_2e725": "Email", "SMS_2e725": "SMS", "WEBHOOK_2e725": "Webhook", "WHATSAPP_2e725": "WhatsApp", "SOMETHING_8d7a5": "Something went wrong", "NOTIFICATI_4212b": "Notification history", "NO_NOTIFIC_dc253": "No notification history", "SETUP_NOTI_6cd55": "Setup notifications", "LOOKS_LIKE_f6ffa": "Looks like you haven't sent out any notifications yet", "SHOWING_1f730": "Showing {{limit}} of {{count}} results", "PAGE_87282": "page: {{page}} / {{pageCount}}", "RECIPIENT_39c70": "Recipient", "NOTIFICATI_2981b": "Notification status", "TRACKING_N_4077f": "Tracking no. / Order no.", "ORDER_SHIP_c1fb3": "Order/shipment status", "SENT_DATE_47f52": "Sent date", "SMS_SEGMENTS_b7a6d": "SMS segments", "FOLLOWS_THE_TIME_i3opd": "Follows the time zone of your organization", "SMS_OVER_68y71": "SMS over 160 characters are automatically split into segments and then re-assembled to appear as one message for the recipient. SMS are charged per segment.", "DATE_RANG_L_1a80b": "Date is from {{start}} to {{end}}", "STATUS_L_1c98e": "Status is {{value}}", "STATUS_L_M_1c98e": "Status are {{value}}", "NOTIFICAT_L_efce0": "Notification status is {{value}}", "NOTIFICAT_L_M_efce0": "Notification status are {{value}}", "ORDER_STATUS_1231": "Order status", "SHIPMENT_STATUS_1231": "Shipment status", "NOTIFICAT_26c5d": "Notification status", "SEARCH_BY_652bb": "Search by notification status", "WEBHOOKS_C_a694d": "Webhooks cannot be filtered by notification status.", "ORDER_SHI_8a869": "Order/shipment status", "SEARCH_BY_2cbc2": "Search by order/shipment status", "SEARCH_BY_6e847": "Search by recipient email, tracking no., order no.", "SEARCH_BY_ae843": "Search by recipient, tracking no., or order no.", "REMAINS_8c1bf": "Remains {{fulfillmentStatus}}", "SORRY_ON_28cfd": "Sorry, only notification details of the last 90 days can be displayed.", "TRACKING_235fc": "Tracking #", "THE_TRACKI_197e0": "The tracking does not exist since it was deleted.", "REMAINS_e8306": "Remains {{tag}}", "ORDER_d4f0c": "Order #", "WEBHOOK_ST_f5843": "Webhook status would be kept for 15 days.", "ORDER_e8306": "ORDER DETAILS", "SHIPMENT_c1dcf": "SHIPMENT DETAILS", "DETAILS_ed8be": "{{notificationType}} details", "EMAILS_AM_f4930": "Emails & SMS", "KEEP_CUSTO_300f3": "Keep customers engaged. You can set <span1><lnk1>notifications to subscribers</lnk1></span1> and <span2><lnk2>notifications to yourself</lnk2></span2>.", "KEEP_CUSTO_04f9c": "Keep customers engaged with proactive shipping notifications.", "SETTINGS_02a08": "Settings", "CREATE_EMA_b629a": "Create email workflow", "THE_NEW_21565": "🎉 The new notification editor is out!", "TRY_OUT_TH_150ad": "Try out the new version", "YOU_CAN_NO_da5db": "You can now send advanced notifications based on destination regions and sub-statuses with our new editor. Don’t worry, we’ll automatically migrate all your existing data as well!", "THE_NEW_NO_66ec9": "The new notification editor is out!", "TRY_OUT_TH_4c372": "Try out the new version", "NO_THANKS_5673f": "No, thanks", "TO_USE_THE_a3a97": "To use the stuck on function, you need to upgrade your notification function to new version. Don't worry, we'll automatically migrate all your existing workflow as well!", "EMAIL_SUBJ_62dc9": "Email Subject", "EMAIL_BODY_4ad5a": "Email body (HTML)", "SAVE_EMAIL_d5a9c": "Save email template", "SMS_MESSAG_88842": "SMS message will be truncated to 500 characters if it is too long.", "ALL_SMS_SE_aabbb": "All SMS sent to India will use our <lnk>standard template</lnk> due to the new regulation.", "CHOOSE_A_T_93e48": "Choose a template", "STANDARD_T_62e80": "Standard template (free)", "CUSTOMIZE_T_62e80": "Customize template", "CONTENT_67b68": "Content", "FREE_SMS_C_f0751": "Free SMS currently does not support sending to India.", "SMS_NOTIFI_5f728": "SMS notifications require additional charges (<lnk>see SMS pricing</lnk>).", "SAVE_SMS_f6666": "Save SMS", "EMAIL_12331": "Email", "SMS_12331": "SMS", "PREVIEW_52a0c": "Preview {{tab}}", "NOTIFICATION_0f941": "Notifications", "PREVIEW_0f941": "Preview", "CLOSE_PREV_77746": "Close preview", "EDIT_WORK_6151c": "Edit workflow", "EDIT_EMAIL_b02b5": "Edit email template", "RENAME_3c94d": "<PERSON><PERSON>", "DELETE_04f02": "Delete", "ICON_5fd0e": "icon", "DEFAULT_cce79": "<PERSON><PERSON><PERSON>", "EDIT_738c4": "Edit", "ICON_5028e": "icon", "FREE_091fd": "Free", "SMS_NOTIFI_c3e8a": "SMS notifications require additional charges. <span><lnk>See SMS pricing</link<span>", "UNLOCK_THE_93aab": "Unlock the rest of notifications", "UPGRADE_PL_e98f7": "Upgrade plan to Pro or above to get access to the rest of notifications that you customized previously.", "UPGRADE_PL_648a1": "Upgrade plan now", "WORKFLOW_578d4": "Workflow name saved.", "EMAIL_D_e8897": "Email disabled", "EMAIL_E_e8897": "Email enabled", "SMS_D_e8897": "SMS disabled", "SMS_E_e8897": "SMS enabled", "CONFIRM_TO_E_42fbe": "Confirm to disable email notification", "CONFIRM_TO_S_42fbe": "Confirm to disable SMS notification", "ARE_YOU_SU_E_d2b2a": "Are you sure you want to disable email notification?", "ARE_YOU_SU_S_d2b2a": "Are you sure you want to disable SMS notification?", "DISABLE_07d59": "Disable", "DELETE_28bf1": "Delete “{{name}}“ workflow", "ONCE_YOU_be80e": "Once you delete a flow, it cannot be undone. Are you sure you want to continue?", "DELETE_fa01a": "Delete", "CONFIRM_T_4650f": "Confirm to disable SMS notification", "CONFIRM_T_b007c": "Confirm to disable SMS notification", "DISABLE_d560b": "Disable", "VERIFY_YOU_12d10": "Verify your sender email address ({{email}}) to get higher delivery rate.", "HOW_TO_VER_65391": "How to verify my sender email?", "CONTACT_SU_8f535": "Contact support", "UPGRADE_YO_3baa9": "Upgrade your plan to Essentials or higher to unlock this feature.", "UPGRADE_NO_f9410": "Upgrade now", "EDIT_WORKF_874ed": "Edit workflow name", "SAVE_4cf3b": "Save", "CANCEL_d4cbc": "Cancel", "NAME_3585a": "Name", "EDIT_SENDE_6d988": "Edit sender info", "SAVE_730ee": "Save", "CANCEL_5d740": "Cancel", "NAME_fed34": "Name", "EMAIL_ADDR_efc36": "Email address", "PLEASE_SEL_9cbd5": "Please select Store", "NOTE_YOU_27177": "Note: You can also <lnk>manage sender email</lnk> in the organization page.", "NOTE_NBSP_6ace5": "Note: <lnk>Contact support</lnk> to verify email address and ensure high delivery rate.", "HOW_TO_VER_536fa": "How to verify my sender email?", "SEND_FROM_961e6": "Send from", "EMAIL_7211d": "Email", "EDIT_9e46d": "Edit", "VERIFIED_715cb": "Verified", "UNVERIFIED_833a8": "Unverified", "REMOVED_a9a6d": "“Powered by AfterShip” removed", "ADDED_51b13": "“Powered by AfterShip” added", "REMOVE_PO_55d27": "Remove “Powered by AfterShip” footer", "VERIFY_SEN_443fb": "Verify send-from email domain", "VERIFY_8784a": "Verify", "CANCEL_12246": "Cancel", "CREATE_3_C_0f243": "Create 3 CNAME records with below values at your domain’s DNS settings to authorise AfterShip to send emails on your behalf.", "CNAME_47dec": "CNAME", "VALUE_47dec": "Value", "EMAIL_AND_fc336": "Email and SMS", "CHOOSE_WHEN_182731": "Choose when to send out notifications. Only one notification will be sent out for every delivery status to avoid spamming. You can choose to send notifications to customers, subscribers or yourself.", "IF_YOU_E_1231": "If you enable notifications to notify subscribers, it will also enable subscription features at your tracking page.", "SMS_NOTIFI_1987123": "<text1>SMS notifications</text1> <text2>require additional charges (<lnk>see SMS pricing</lnk>)</text2>", "TO_CUSTOME_123123": "To customers", "TO_SUBSC_098123": "To subscribers", "EDIT_NOTIFI_71234": "Edit notifications to subscribers at the tracking page settings.", "GO_TO_T_1231": "Go to tracking page settings", "TO_YOUR_S_123": "To yourself", "EDIT_NOTI_12712": "Edit notifications to yourself at the product settings.", "GO_TO_12123": "Go to settings", "RECOMMENDE_c8c54": "Recommended email workflows", "DONT_KNOW_e8f67": "Don’t know where to begin? Start with our recommended workflows.", "SHIPMENT_U_9b252": "Shipment updates to exception", "ORDER_REMA_e5992": "Order remains unfulfilled", "ESTIMATED_fe3ca": "Estimated delivery date revised", "CREATE_4206d": "Create", "UNAVAILABL_d5090": "Unavailable as your shipments are imported by CSV or public API", "CREATE_AN_e3c98": "Create an email workflow", "SHIPMENT_3f2d8": "Shipment status", "ORDER_STA_10c86": "Order status", "ORDER_STAT_96bc8": "Order status", "UNAVAILABL_6c742": "Unavailable as your shipments are imported by CSV or public API", "ESTIMATED_26bea": "Estimated delivery date", "CREATE_A_W_8ff38": "Create a workflow", "CREATE_395c5": "Create", "CANCEL_6d029": "Cancel", "FOR_8c3b8": "for", "THIS_FIELD_8c3b8": "This field is required", "PLEASE_ENTER_8c3b8": "Please enter a valid number", "ENTER_AN_8c3b8": "Enter an integer between 1 and 45", "SETTINGS_d33d6": "Settings", "EMAIL_SETT_cebc7": "Email settings", "WE_WILL_SE_db11e": "We will send out delivery notifications from this email address. Verify domain to increase the delivery rate.", "CONTACT_SU_aff8b": "Contact support", "SMS_SETTIN_e1c19": "SMS settings", "YOU_CAN_NO_9c1d1": "You can now schedule SMS to your contacts. View SMS pricing <lnk>View SMS pricing</lnk> to send text messages.", "DATE_TIM_69a08": "Date & time", "SET_UP_THE_78a22": "Set up the date format and timestamp of your email and SMS.", "IF_THE_CAR_e6b96": "If the carrier does not provide a timestamp, it won’t be shown regardless of your setting.", "FAILED_TO_b39c1": "Failed to schedule SMS, please try again.", "SMS_SCHED_fa890": "SMS scheduled successfully", "FAILED_TO_d10c0": "Failed to schedule SMS, please try again.", "SCHEDULE_S_1f133": "Schedule SMS", "EDIT_045dc": "Edit", "THE_SMS_WI_cb350": "The SMS will be sent during this period based on your customer’s time zone. If it’s not available, your organization’s time zone [{{timezone}}] will apply.", "SCHEDULE_S_5df85": "Schedule SMS", "CONFIRM_8f1df": "Confirm", "CANCEL_764a0": "Cancel", "SCHEDULE_T_303bf": "Schedule to send SMS between:", "DATE_FORM_dffbb": "Date format saved", "DATE_FORMA_02ea3": "Date format", "EDIT_357c9": "Edit", "EDIT_DATE_79ec7": "Edit date format", "SAVE_cd4f3": "Save", "CANCEL_88d5a": "Cancel", "WITH_TIMES_dd19d": "With timestamp", "NOTE_IF_T_09de2": "Note: If the carrier does not provide a timestamp, it won’t be shown regardless of your setting.", "WEBHOOKS_49ba0": "Webhooks", "ESTIMATED_123131": "Estimated delivery date (EDD)", "ALL_EDD_123123": "All EDD updates", "WEBHOOK_UR_2900b": "Webhook URLs", "EVENTS_234234": "Events", "CHOOSE_W_8123": "Choose which events you want to get notifications for.", "ADD_WEBHOO_5cbb0": "Add webhook URL", "CANCEL_1c09e": "Cancel", "WEBHOOK_S_50c47": "Webhook Secret created", "WEBHOOK_S_b52bc": "Webhook Secret activated", "UPDATE_81231": "Update", "WEBHOOK_SE_802c0": "Webhook secret", "EACH_NOTI_23234": "Each notification will be signed with this secret.", "INACTIVE_eb742": "Inactive", "ACTIVATE_123123": "Activate", "BEFORE_ACTIVATING_123123": "Before activating a new secret, make sure it's compatible with your system.", "DELETE_WEB_516b0": "Delete webhook", "DELETE_b9f4a": "Delete", "CANCEL_40870": "Cancel", "ARE_YOU_SU_c363f": "Are you sure you want to delete this Webhook URL?", "ADD_WEBHOO_67ff8": "Add webhook URL", "SAVE_50c32": "Save", "CANCEL_d50bc": "Cancel", "WEBHOOK_UR_a6978": "Webhook URL", "MUST_BEGIN_1c419": "Must begin with http or https", "ALL_STATUS_123123": "All status updates", "SPECIFIC_EVENTS_e7ccc": "Specific status updates", "THIS_EVENT_2e725": "This event", "SECRET_AND_129812": "Secret and URLs", "PREVIEW_SMS_612": "Preview SMS", "EMAILS_fg51": "Emails", "SMS_fa612g": "SMS", "ACTIVATE_NEW_fag161": "Activate new secret", "ACTIVATE_fag161": "Activate", "ACTIVATING_THIS_61bd651": "Activating this secret will replace your previous one. Make sure the new secret is compatible with your system first.", "NO_URLS_bfa61": "No URLs added yet.", "OUR_WEBHOOKS_2234": "Our webhooks can push JSON notifications to up to 4 URLs. They also support AWS API Gateway with IAM authentication. Learn more about <lnk>Webhook</lnk>", "SETTINGS_UPDATED_akj81": "Settings updated", "URL_SHOULD_aja81": "URL should begin with http or https", "NOTIFICATION_ai91": "notification", "NOTIFICATIONS_ai91": "notifications", "DISABLE_SC_ai18nf": "Disable scheduled SMS", "ONCE_THE_f13ay1": "Once the feature is disabled, your customers will receive SMS messages anytime during the day.", "DISABLE_d81a1": "Disable", "CANCEL_186dg1": "Cancel", "FACEBOOK_80b4d": "Facebook", "WHATS_APP_ed30c": "WhatsApp", "BETA_2276d": "Beta", "MANAGE_FACE_59973": "Manage in your tracking pages", "KEEPYOURCU_6073e": "Manage Facebook Messenger delivery notifications in the “Subscription” section of the tracking page editor.", "MANAGENOTI_4817f": "Go to tracking pages", "REQUESTACC_9655e": "Request access to WhatsApp notifications", "ADDANOTHER_d2792": "Add another level of customer engagement with WhatsApp notifications.", "THISFEATUR_110c1": "This feature is only available on the Enterprise plan. You can request access and we'll notify you once we've set it up for you.", "REQUESTACC_037ad": "Request access", "REQUESTREC_b8c5d": "Request received", "REQUESTALR_e192f": "Request already submitted", "DAYS_83kf3d3": "day(s)", "DEFAULT_FLOW_ENABLED_25kg45": "Email notification enabled", "NO_BLOCKS_JDq2324": "No blocks", "SHIPMENT_ITEMS_DSAJq42": "Shipment items", "PRODUCT_RECOMM_s2o341": "Product recommendation", "COPY_CONTENT_df3hk34": "Code copied to clipboard", "COPY_WEBHOOK_kjk4wer2": "Copy code", "UNABLE_EXPORT_w4hvd": "Unable to export notification history CSV at this time. Please try again.", "THE_CSV_p6p9f": "Notification history CSV has been exported. You will receive an email with a download link soon.", "A_MAXIMUM_5r2sw": "A maximum of 100,000 rows can be exported. Any rows over this limit will be excluded. Data will be exported based on the filters applied and will be sent to {{email}}.", "EXPORT_NOTIFICATIONS_t1zx5": "Export {{type}} notification tab history CSV"}