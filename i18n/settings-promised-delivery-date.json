{"REST_58b99": "Rest of world", "IN_ANOTHER_58b99": "In another zone", "COUNTRIES_58b99": "{{checkedCount}} of {{total}} countries/regions", "SEARCH_COU_8a1db": "Search countries or regions", "CHOOSE_SHI_87980": "Choose shipping zones", "SAVE_47770": "Save", "CANCEL_673b7": "Cancel", "STATE_LEVE_6f11d": "State-level zones only work for shipments imported from a connected Shopify store. For shipments imported using other methods, zones can only be set at the country/region level.", "CLEAR_bb768": "Clear", "DISMISS_95528": "<PERSON><PERSON><PERSON>", "GOT_IT_892e8": "Got it", "NEXT_88b52": "Next", "1_4_ade69": "1/4", "TO_SET_UP_4eac9": "To set up AI Predictive EDD, first choose whether you want it to predict a single date or a date range.", "2_4_02be3": "2/4", "OUR_AI_CAN_3b942": "Our AI can predict the EDD for 43 carriers worldwide.", "3_4_948ac": "3/4", "IF_YOU_PRE_fe9db": "If you prefer to manually control the EDD rather than use our AI, you can use the Custom EDD option.", "4_4_8cdb0": "4/4", "CUSTOM_EDD_abc50": "Custom EDD even lets you set the transit times for different shipping methods, carriers, and destinations.", "EXAMPLES_4e286": "Examples", "GOT_IT_4d80d": "Got it!", "TRACKING_PA_f9f90": "Tracking page", "TRACKING_PA_A_f9f90": "All customers", "EMAIL_NOTIF_f9f90": "Email notification", "EFFECT_IN_47b77": "Effect in tracking page", "EFFECT_IN_c9305": "Effect in email notification", "TRANSIT_TI_bd1c4": "Transit time", "SELECT_A_T_5907e": "Select a transit time for shipments to this shipping zone.", "SINGLE_DAY_14659": "Single day", "DATE_RANGE_bb151": "Date range", "BUSINESS_T_60f87": "to", "BUSINESS_D_60f87": "business days", "SHIPPING_M_ab235": "Shipping method", "FOR_SHOPIF_1_7f78a": "For Shopify users, this must match the rate name in your <lnk>Shipping and delivery</lnk> settings.", "FOR_SHOPIF_2_7f78a": "For Shopify users, this must match the rate name in your Shopify shipping settings settings.", "USE_SHIPPI_506e9": "Use shipping method", "ANY_SHIPPI_0806c": "Any shipping method", "CARRIER_58d3c": "Carrier", "SEARCH_fc676": "Search", "ORDER_CUT_1bc72": "Order cutoff time", "ADDITIONA_c6c2a": "Additional processing time", "ADDITIONA_C_c6c2a_zero": "{{count}} additional business day", "ADDITIONA_C_c6c2a_one": "{{count}} additional business day", "ADDITIONA_C_c6c2a_other": "{{count}} additional business days", "BUSINESS_95d14": "Business days", "ORDER_CUT_79c4c": "ORDER CUTOFF AND PROCESSING TIMES", "HANDICRAF_458a2": "Handicraft or other pre-sale industries", "EDIT_7b44a": "Edit", "THIS_WILL_788d6": "This will apply to all orders in your connected stores.", "INDUSTRY_3d1ff": "INDUSTRY", "FAST_3d1ff": "Fast-moving consumer goods industry", "EDD_3d1ff": "Estimated delivery date = Order created date + Order cutoff and processing time + Transit time", "HANDICRAFT_3d1ff": "Handicraft or other pre-sale industries", "EDD_2_3d1ff": "Estimated delivery date = Order fulfilled date + Transit time", "SELECT_AT_937d3": "Select at least one business day.", "SETTINGS_7c6e4": "Settings", "EDIT_ORDER_1c74b": "Edit order cutoff and processing times", "SUBMIT_f4910": "Submit", "CANCEL_a52dc": "Cancel", "ORDER_CUTO_c6cf9": "Order cutoff time", "TIME_87c18": "Time", "TIMEZONE_16f41": "Timezone", "ORDER_PROC_a4e91": "Order processing time", "SELECT_HOW_dd71b": "Select how long your warehouse needs to process an order.", "BUSINESS_D_f129e": "business days", "BUSINESS_D_39346": "Business days", "SELECT_WHI_57403": "Select which days your warehouse can process an order. Any U.S. holidays will be automatically excluded.", "CONFIRM_M_47716": "The Custom EDD will only appear on shipments created after you enable this feature (shipments created after ({{createdAt}}).", "CONFIRM_M_2_47716": "We noticed that you haven't updated your Custom EDD settings. Are sure you want to apply the default settings to all your newly added shipments?", "CONFIRM_M_3_47716": "Any new shipments will not show the Custom EDD.", "ENABLE_CUS_5f9cc": "Enable Custom EDD", "DISABLE_CUS_5f9cc": "Disable Custom EDD", "ENABLE_e99fb": "Enable", "DISABLE_e99fb": "Disable", "CANCEL_f9db3": "Cancel", "SYNC_PAST_8856c": "Sync past shipments ", "ADDITIONAL_23123": "Additional processing time must be greater than or equal to 0.", "ADDITIONAL_9023123111": "Additional processing time must be less than or equal to 200.", "ADDITIONAL_5112": "Additional processing time is required.", "CHANGE_IN_f8387": "Change industry", "THE_CALCU_06dcc": "The calculation rule of your orders’ estimated delivery dates will change accordingly. Are you sure you want to change the industry?", "CONFIRM_b460c": "Confirm", "CUSTOM_EDD_95769": "Custom EDD", "MANUALLY_C_84700": "Manually control your EDD by shipping type, carrier, and destination.", "SET_CUSTOM_f7d4a": "Set Custom EDD", "DISABLE_96077": "Disable", "ENABLE_d2b5d": "Enable", "ADD_NEW_TR_32c79": "Add new transit time", "SET_UP_TRA_72c1b": "Set up transit times based on the shipping zone and method.", "EDD_72c1b": "Estimated delivery dates", "EDD_A_72c1b": "go back <PERSON><PERSON><PERSON> page", "SHIPPING_Z_987c2": "Shipping zone", "SELECT_THE_dd818": "Select the destination countries/regions or states.", "NONE_SELEC_1392f": "None selected", "EDIT_d132e": "Edit", "ADD_76928": "Add", "SAVE_a2744": "Save", "FOR_AUSTRA_0b334": "For Australia Post, non-Shopify users need to provide the destination postal code via API or CSV for the AI engine to generate an EDD", "VIEW_CARRI_99e3c": "View carriers", "SEARCH_BY_51766": "Search by carrier name", "SHOWING_03016": "Showing {{length}} carriers", "CARRIERS_41d72": "CARRIERS", "OUR_AI_COV_a6d9c": "Our AI covers {{length}} worldwide carriers.", "VIEW_CARRI_6895b": "View carriers", "DATE_F_c13c9": "DATE FORMAT", "SINGLE_D_c13c9": "Single date", "CARRIERS_c13c9": "Carriers also provide a single date EDD. Our AI engine is typically 91% accurate.", "DATE_R_c13c9": "Date range", "THE_N_c13c9": "The number of days in the range is intelligently determined by our AI based on the carrier's past shipping performance.", "WITH_c13c9": "With an average 96% accuracy, the AI engine will most commonly calculate a 2-day date range.", "ENABLE_AI_9eed5": "Enable AI Predictive EDD", "DISABLE_A_eb889": "Disable AI Predictive EDD", "THE_AI_PR_65439": "The AI Predictive EDD will appear on all future shipments. It will also appear on existing shipments when the status updates.", "ANY_NEW_S_81b11": "Any new shipments will not show the AI Predictive EDD.", "CONFIRM_ff23b": "Confirm", "AI_PREDIC_00ca9": "AI Predictive EDD enabled", "AI_PREDIC_2db72": "AI Predictive EDD disabled", "AI_PREDIC_c5f79": "AI Predictive EDD updated", "AI_PREDICT_d7ff1": "AI Predictive EDD", "OUR_AI_WIL_3fa14": "Our AI will predict the EDD with outstanding accuracy when the carrier fails to provide one.", "SET_AI_PRE_142ab": "Set AI Predictive EDD", "DISABLE_b4f30": "Disable", "ENABLE_4d2d6": "Enable", "TRANSIT_TI_16956": "TRANSIT TIMES", "ADD_a797b": "Add", "DELETE_20fa9": "Delete", "CAN_T_ADD_75295": "Can't add the shipping time as you've exceeded the maximum limit in this zone.", "ADD_NEW_SH_daa9c": "Add new shipping method", "ADD_NEW_SH_c6aa3": "Add new shipping method", "YOUVE_EX_9d258": "You've exceed maximum word limit", "THE_SHIPP_88793": "The shipping method ({{value}}) already exists in this zone.", "PLEASE_AD_57371": "Please add a shipping method as (any shipping methods) already exists in this zone.", "CARRIER_I_a9f61": "Carrier is required", "THIS_CARR_6d238": "This carrier already exists in this zone.", "EDIT_b6c5f": "Edit transit times", "ADD_b6c5f": "Add new shipping method", "CANCEL_b6c5f": "Cancel", "BUSINESS_f588e_zero": "Same day", "BUSINESS_f588e_one": "{{count}} business day", "BUSINESS_f588e_other": "{{count}} business days", "BUSINESS_3d1ff": "{{start}} - {{end}} business days", "DELETE_SH_a2278": "Delete shipping method?", "FOR_NEW_S_6c096": "For new shipments, the Custom EDD will not show for this shipping method.", "CANCEL_fa142": "Cancel", "DELETE_c3b7d": "Delete", "ANY_SHIPPI_2e9f9": "Any shipping methods", "EDIT_58b99": "Edit", "DELETE_58b99": "Delete", "DELETE_SH_d9435": "Delete shipping zone?", "FOR_NEW_S_4f9e6": "For new shipments, the Custom EDD will not show for this shipping zone.", "CANCEL_34c63": "Cancel", "DELETE_da1c5": "Delete", "CHOOSE_58b99": "Choose at least 1 country or region.", "DATE_1_58b99": "Date range is required.", "DATE_2_58b99": "Date range must be an integer.", "DATE_3_58b99": "Date range must be greater than or equal to 0.", "DATE_4_58b99": "Date range must be less than or equal to 200.", "DATE_5_58b99": "The end date must be greater than the start date.", "ESTIMATED_13283": "Estimated delivery dates", "PDD_ST_1_f588e": "You can choose between our AI Predictive EDD or set custom ones yourself. The date will appear on tracking pages, and in emails and SMS. <lnk1>FAQ page</lnk1> <lnk2>Show example</lnk2>", "PDD_ST_2_f588e": "If you enable this feature, your customers will be able to see the estimated delivery date on your tracking page and in your delivery notifications. <lnk1>FAQ page</lnk1> <lnk2>Show example</lnk2>", "SETTINGS_9819231": "Settings", "OUR_AI_CAN_f26bb": "Our AI can provide customers with an EDD even when the carrier doesn't. Plus it's 91% accurate on average.", "THIS_ADVAN_eaceb": "This advanced feature is free to use until June 27, 2022. To continue using it after that, contact sales.", "ESTIMAT_3d1ff": "⌛️ Estimated delivery dates is calculating now", "IT_WILL_TA_c13c9": "It will take up to 3 hours to finish the calculation depends on the quantity of your shipments. The rules of ‘order cutoff & processing time and transit time’ cannot be updated during the calculation process.", "SUNDAY_f9e1k7": "Sunday", "MONDAY_f9e1k7": "Monday", "TUESDAY_f9e1k7": "Tuesday", "WEDNESDAY_f9e1k7": "Wednesday", "THURSDAY_f9e1k7": "Thursday", "FRIDAY_f9e1k7": "Friday", "SATURDAY_f9e1k7": "Saturday"}