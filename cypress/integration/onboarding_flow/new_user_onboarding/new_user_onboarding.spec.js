import {Then, When, Given} from 'cypress-cucumber-preprocessor/steps';

Given('the user has an organization', () => {
	cy.server();
	cy.callSecure('POST', '/json/admin/settings/profile', {
		store_name: 'just test',
		store_url: 'https://www.aftership.com',
		monthly_shipments: 'Less than 1,000',
	});
});
Given('the user has an active subscription', () => {
	cy.server();
	cy.get('@newUserEmail').then(({email}) => {
		cy.callBilling('POST', '/customer', {
			email,
		});
		cy.callBilling('POST', '/subscriptions', {
			plan: 'AS_TRIAL',
		});
	});
});

Then('I can see the organization modal', () => {
	cy.get('[role=dialog]:contains("Tell us a little about yourself")').should(
		'be.visible'
	);
});
Then('I can see the start free trial modal', () => {
	cy.get('[role=dialog]:contains("Welcome to AfterShip")').should(
		'be.visible'
	);
});
Then('I can see the courier selection modal', () => {
	cy.get('[role=dialog]:contains("Select couriers you use")').should(
		'be.visible'
	);
});

When('I press ESC', () => {
	cy.get('body').type('{esc}');
});

When('I submit the form:', ({rawTable}) => {
	const fields = rawTable[0];
	const values = rawTable[1];
	for (let i = 0; i < fields.length; i++) {
		if (fields[i] === 'monthly_shipments') {
			cy.get('select:contains("Please select...")').select(values[i]);
		} else {
			cy.get(`input[name="${fields[i]}"]`)
				.clear()
				.type(values[i]);
		}
	}
	cy.server();
	cy.route('POST', '/json/admin/settings/profile').as('updateProfile');
	cy.contains('button', 'Continue').click();
	cy.wait('@updateProfile');
});

When('I click the button "Start 7-day free trial"', () => {
	cy.route('POST', '/api-billing/customer').as('createCustomerReq');
	cy.route('POST', '/api-billing/subscriptions').as('createSubscriptionReq');
	cy.contains('button', 'Start 7-day free trial').click();
	cy.wait('@createCustomerReq');
	cy.wait('@createSubscriptionReq');
});

When('I click the button "Start tracking"', () => {
	cy.contains('button', 'Start tracking').click();
});

Then('I can not see the organization modal', () => {
	cy.get('[role=dialog]:contains("Tell us a little about yourself")').should(
		'not.be.visible'
	);
});

Then('I can not see the start free trial modal', () => {
	cy.get('[role=dialog]:contains("Welcome to AfterShip")').should(
		'not.be.visible'
	);
});

Then('I can not see the courier selection modal', () => {
	cy.get('[role=dialog]:contains("Select couriers you use")').should(
		'not.be.visible'
	);
});
