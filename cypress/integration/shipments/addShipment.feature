Feature: Add shipment

	Rules:
	- Tracking number & courier is required;
	- If courier has required fields, the required fields also required;
	- If courier has optional fields, show the optional fields but not required;

	Scenario: A user try to add a shipment without required fields and optional fields.
		Given I am logged in as "<EMAIL>"
		And courier ups no need required fields and optional fields
		When I go to "shipments/add"
		Then I should see the tracking number field
		And I should see the courier field
		And I should see the save button is disabled
		When I fill the form:
			| tracking_number    | courier |
			| 1ZR1093R0319249891 | UPS     |
		Then I select the courier "UPS"
		Then I should see the save button is enabled
		When I click the save button
		Then I should back to "shipments"
		And I should see the successful toast
		When I go the shipment detail
		Then I should see the tracking number is "1ZR1093R0319249891"

	Scenario: A user try to add a shipment with required fields but no optional fields.
		Given I am logged in as "<EMAIL>"
		And courier bpost has required field "tracking_postal_code"
		When I go to "shipments/add"
		But I should not see the tracking postal code field
		When I fill the form:
			| tracking_number | courier |
			| **********      | Bpost   |
		Then I select the courier "Bpost"
		Then I should see the tracking postal code field
		And I should see the save button is disabled
		When I fill the tracking postal code field with "12345"
		Then I should see the save button is enabled
		When I click the save button
		Then I should back to "shipments"

	Scenario: A user try to add a shipment wih optional fields but no required fields.
		Given I am logged in as "<EMAIL>"
		And courier international-seur has optional field "tracking_ship_date"
		When I go to "shipments/add"
		Then I should not see the tracking ship date field
		When I fill the form:
			| tracking_number | courier            |
			| **********      | International Seur |
		Then I select the courier "International Seur"
		Then I should see the tracking ship date field
		And I should see the save button is enabled
		When I click the save button
		Then I should back to "shipments"

	Scenario: A user try to add a shipment with required fields, but failed.
		Given I am logged in as "<EMAIL>"
		And courier total-express has 2 required fields "tracking_account_number" and "tracking_key"
		When I go to "shipments/add"
		When I I fill the form:
			| tracking_number        | courier       |
			| 9400115901883796187571 | Total Express |
		Then I should see the tracking account number field
		And I should see the tracking key field
		And I should see the save button is disabled
