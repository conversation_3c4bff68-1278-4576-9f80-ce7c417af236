import {Given, When, Then} from 'cypress-cucumber-preprocessor/steps';

const findInput = label => {
	return cy
		.contains(label)
		.closest('.Polaris-Labelled__LabelWrapper')
		.siblings('.Polaris-Connected')
		.find('input')
		.clear();
};

const getShipmentId = () => {
	const last = arr => arr[arr.length - 1];
	return cy.location().then(str => last(str.split('/')));
};

And(/courier ups no need required fields and optional fields/, () => {});
When(/I go to "(.*)"/, path => {
	cy.visit(path);
	cy.server();
});
Then(/I should see the tracking number field/, () => {
	cy.contains('Tracking no.');
});
And(/I should see the courier field/, () => {
	cy.contains('Courier');
});
And(/I should see the save button is disabled/, () => {
	cy.contains('Save shipment').should('have.attr', 'disabled');
});
When(/I fill the form:/, table => {
	cy.route('POST', new RegExp('/json/private/couriers/detect')).as(
		'auto-detect'
	);
	cy.handleRawTable(table).then(data =>
		Promise.all(
			data.map(({tracking_number, courier}) => {
				findInput('Tracking no.').type(tracking_number);
				cy.wrap(tracking_number).as('tracking_number');
				cy.wait('@auto-detect');
				findInput('Courier').type(courier);
			})
		)
	);
});
Then(/I select the courier "(.*)"/, courier => {
	cy.get('.Polaris-OptionList-Option button')
		.contains(courier)
		.should('be.visible')
		.then(el => {
			Cypress.$(el).click();
		});
});
Then(/I should see the save button is enabled/, () => {
	cy.contains('Save shipment').should('not.have.attr', 'disabled');
});
When(/I click the save button/, () => {
	cy.route('POST', new RegExp('/json/private/trackings'), {
		data: {tracking: {tracking_number: 'tracking_number', id: 'id'}},
	}).as('add_track');
	cy.contains('Save shipment').click();
	cy.wait(['@add_track']).then(
		({
			request: {
				body: {tracking},
			},
		}) => {
			cy.get('@tracking_number').then(tracking_number =>
				expect(tracking.tracking_number).eq(tracking_number)
			);
		}
	);
});
Then(/I should back to "(.*)"/, path => {});
And(/I should see the successful toast/, () => {
	cy.contains('View shipment');
});
When(/I go the shipment detail/, () => {});
Then(/I should see the tracking number is "(.*)"/, () => {});

And(/courier bpost has required field "(.*)"/, postalCode => {});
But(/I should not see the tracking postal code field/, () => {});
Then(/I should see the tracking postal code field/, () => {
	cy.contains('Postal Code');
});
And(/I should see the save button is disabled/, () => {});
When(/I fill the tracking postal code field with "(.*)"/, postalCode => {
	findInput('Postal Code').type(postalCode);
});

And(/courier international-seur has optional field "(.*)"/, shipDate => {});
Then(/I should not see the tracking ship date field/, () => {});
Then(/I should see the tracking ship date field/, () => {
	cy.contains('Ship Date');
});

And(
	/courier total-express has 2 required fields "(.*)" and "(.*)"/,
	(accountNumber, trackingKey) => {}
);
Then(/I should see the tracking account number field/, () => {});
And(/I should see the tracking key field/, () => {});
