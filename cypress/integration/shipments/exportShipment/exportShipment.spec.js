/// <reference types="Cypress" />
import {Before, Then, When, Given} from 'cypress-cucumber-preprocessor/steps';
import dayjs from 'dayjs';

Before(() => {
	cy.viewport(1200, 768);
});

Then('the user settings {string} is {string}', () => {});

Then('current time is {string}', () => {});

When('I visit path {string}', path => {
	cy.server();
	cy.route('POST', new RegExp('/json/admin/trackings/export/csv')).as(
		'exportRequest'
	);
	cy.fixture('trackings.json').then(trackingsResp => {
		cy.route('GET', new RegExp('/data/trackings/v1/search'), trackingsResp);
	});
	cy.visit(path);
});

Then('I expect that button {string} is displayed', buttonText => {
	cy.contains('button', buttonText)
		.should('be.visible')
		.as('exportBtn');
});

When('I click the button {string}', () => {
	cy.get('@exportBtn').click();
});

Then('I expect that a modal is displayed', () => {
	cy.contains('[role=dialog]', 'Export shipments by CSV')
		.should('be.visible')
		.as('dialog');
});

Then('I expect that the modal contains a button', () => {
	cy.get('@dialog')
		.get('button')
		.should('be.visible');
});

Then('I expect that the button label matches {string}', buttonText => {
	cy.get('@dialog')
		.get('button')
		.contains(buttonText)
		.as('dialogBtn');
});

When('I click the modal button {string}', () => {
	cy.get('@dialogBtn').click();
});

Then('I expect that a export request has been sent', () => {});

Then('I expect that the request body like:', () => {
	// timezone probl;em
	const ONE_DAY = 86400;
	const formatDate = (date, diff = 0) => {
		return dayjs(date)
			.subtract(diff, 'day')
			.unix();
	};

	cy.wait(['@exportRequest']).then(({request}) => {
		const {tag, created_at_min, created_at_max} = request.body;
		const expectStartDate = formatDate(Date.now(), 119);
		const expectEndDate = formatDate();

		expect(tag, 'tag is string').to.be.a('string');
		expect(
			Math.abs(formatDate(created_at_min) - expectStartDate),
			'expect lessThan ONE_DAY'
		).to.lessThan(ONE_DAY);
		expect(
			Math.abs(formatDate(created_at_max) - expectEndDate),
			'expect lessThan ONE_DAY'
		).to.lessThan(ONE_DAY);
	});
});

Then('I expect that button {string} is not displayed', buttonText => {
	cy.contains('button', buttonText)
		.should('not.be.visible')
		.as('exportBtn');
});

Then('I expect the error toast is {string}', errorText => {
	cy.get('.Polaris-Banner')
		.contains(errorText)
		.should('be.visible');
});

Then('I expect that the path includes {string}', search => {
	cy.url().should('include', search);
});

Then('I click the tab button {string}', buttonText => {
	cy.get('button.Polaris-Tabs__Tab')
		.contains(buttonText)
		.click();
});

Then('I expect that the request body include tag is {string}', text => {
	cy.wait(['@exportRequest']).then(({request}) => {
		const {tag} = request.body;
		expect(tag, `expect tag is ${text}`).equal(text);
	});
});
