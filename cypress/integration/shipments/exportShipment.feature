Feature: Export shipment list by CSV
	User can export current shipments in the list by CSV.
	Scenario: A user has shipment and try to export shipment by default settings.
		Given I am logged in as "<EMAIL>"
		And the user settings "timezone" is "+8"
		And current time is "2019-10-30T14:00:00+08:00:00"
		When I visit path "/shipments"
		Then I expect that button "Export" is displayed
		When I click the button "Export"
		Then I expect that a modal is displayed
		And I expect that the modal contains a button
		And I expect that the button label matches "Export shipments"
		When I click the modal button "Export shipments"
		Then I expect that a export request has been sent
		And I expect that the request body like:
			"""
			{
			created_at_max: "2019-10-30T14:00:00+08:00:00"
			created_at_min: "2019-10-01T00:00:00+08:00:00"
			tag: ""
			}
			"""

	Scenario Outline: A user has shipment and try to export shipment by applied filter.
		Given I am logged in as "<EMAIL>"
		When I visit path "/shipments"
		Then I expect that button "Export" is displayed
		Then I click the tab button "<tabButton>"
		Then I click the button "Export"
		Then I expect that a modal is displayed
		And I expect that the modal contains a button
		And I expect that the button label matches "Export shipments"
		When I click the modal button "Export shipments"
		Then I expect that a export request has been sent
		And I expect that the request body include tag is "<tagName>"

		Examples:
			| tabButton | tagName |
			| Pending   | Pending |

	# FIXME: ABU-5668
	# Scenario: A user has not shipment and try to export shipment by default settings.
	# 	Given I am logged in as "<EMAIL>"
	# 	And the user settings "timezone" is "+8"
	# 	And current time is "2019-10-30T14:00:00+08:00:00"
	# 	When I visit path "/shipments"
	# 	Then I expect that button "Export" is not displayed
	# 	When I visit path "/shipments?page=1"
	# 	Then I expect that button "Export" is displayed
	# 	When I click the button "Export"
	# 	Then I expect that a modal is displayed
	# 	And I expect that the modal contains a button
	# 	And I expect that the button label matches "Export shipments"
	# 	When I click the modal button "Export shipments"
	# 	Then I expect the error toast is "Make sure you have shipments in the list."

	Scenario: shipments page query be expected reset after export CSV.
		Given I am logged in as "<EMAIL>"
		When I visit path "/shipments?page=1&delivery-status=expired"
		Then I expect that button "Export" is displayed
		When I click the button "Export"
		Then I expect that a modal is displayed
		And I expect that the modal contains a button
		And I expect that the button label matches "Export shipments"
		When I click the modal button "Export shipments"
		Then I expect that a export request has been sent
		Then I expect that the path includes "page=1&delivery-status=expired"

