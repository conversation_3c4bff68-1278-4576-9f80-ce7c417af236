/// <reference types="Cypress" />
import {Then} from 'cypress-cucumber-preprocessor/steps';
const {billingApi} = Cypress.env();

Then('I am a user', plan => {
	cy.server();
	cy.route(
		'POST',
		'https://admin.aftership.dev/cf-workers/reCAPTCHAv3/verify',
		{
			success: true,
			hostname: 'localhost',
			score: 0.8,
			action: '_settings_billing',
		}
	);
	cy.route('POST', billingApi + '/subscriptions').as('subscriptions');
});

Then('I can see the text on button is {string}', text => {
	cy.url().then(url => {
		if (url.includes('/settings/billing/payment-method')) {
			cy.get('[data-test-id="PAYMENT-METHOD-MODAL-CONTENT"]')
				.parents('[role="dialog"]')
				.contains('button', text)
				.as('userButton');
		} else {
			cy.get('[data-test-id="SUBSCRIBE-MODAL-CONTENT"]')
				.parents('[role="dialog"]')
				.contains('button', text)
				.as('userButton');
		}
	});
});

Then('I click the button', () => {
	cy.get('@userButton').click();
});

Then('I can see the page goto {string}', address => {
	if (address === '/settings/billing') {
		cy.wait('@subscriptions');
	}
	if (address.indexOf('http') !== 0) {
		address = 'http://localhost:3303' + address;
	}
	cy.url().should('eq', address);
});
