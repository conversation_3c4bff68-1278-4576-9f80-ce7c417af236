/// <reference types="Cypress" />
import {Then} from 'cypress-cucumber-preprocessor/steps';

Then('I am a bot', plan => {
	cy.server();
	cy.route(
		'POST',
		'https://admin.aftership.dev/cf-workers/reCAPTCHAv3/verify',
		{
			success: true,
			hostname: 'localhost',
			score: 0.1,
			action: '_settings_billing',
		}
	);
});

Then('I visit the page address {string}', address => {
	cy.visit((address = 'http://localhost:3303' + address), {
		onBeforeLoad(win) {
			cy.stub(win, 'open').as('windowOpen');
		},
	});
});

Then('I can see the text on button is {string}', text => {
	cy.url().then(url => {
		if (url.includes('/settings/billing/payment-method')) {
			cy.get('[data-test-id="PAYMENT-METHOD-MODAL-CONTENT"]')
				.parents('[role="dialog"]')
				.contains('button', text)
				.as('botButton');
		} else {
			cy.get('[data-test-id="SUBSCRIBE-MODAL-CONTENT"]')
				.parents('[role="dialog"]')
				.contains('button', text)
				.as('botButton');
		}
	});
});

Then('I click the button', () => {
	cy.get('@botButton').click();
});

Then('I can see a new page opened with {string}', address => {
	cy.get('@windowOpen').should('be.calledWith', address);
});
