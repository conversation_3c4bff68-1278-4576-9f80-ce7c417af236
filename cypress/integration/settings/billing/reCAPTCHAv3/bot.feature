Feature: Real user and bot will see differen button

	Scenario Outline: <PERSON><PERSON> will see "Contact sales" button
		Given I am a bot
		And I am logged in as "<EMAIL>"
		When I visit the page address "<pageAddress>"
		Then I can see the text on button is "Contact sales"
		When I click the button
		Then I can see a new page opened with "https://www.aftership.com/contact-sales"

		Examples:
			| pageAddress 															|
			| /settings/billing/payment-method 					|
			| /settings/billing/choose-plan/essential		|
