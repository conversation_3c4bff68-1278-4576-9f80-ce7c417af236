/// <reference types="Cypress" />
import {Then, When, Given} from 'cypress-cucumber-preprocessor/steps';

Given('I am a Free Trial user', () => {});

When('I visit {string}', path => {
	cy.server();
	cy.route('POST', new RegExp('/api-billing/subscriptions')).as(
		'subscription'
	);
	cy.visit(path);
});

Then("I should see there's a Forever free plan", () => {
	cy.get('.Polaris-Stack')
		.contains('Forever Free')
		.get('.Polaris-Heading')
		.should('be.visible');
});

When('I click {string} button of this plan', () => {
	cy.get('.Polaris-Stack')
		.contains('.Polaris-Card__Section', 'Forever Free')
		.contains('Choose this plan')
		.click()
		.as('chooseBtn');
});

When('I click {string} button of Growth plan', text => {
	cy.contains('.Polaris-Card__Section', 'Growth').as('item');

	cy.get('@item')
		.contains('button', 'Choose this plan')
		.click()
		.as('chooseBtn');
});

Then('I should see a modal pops up', () => {
	cy.contains('[role=dialog]', 'Limited time offer')
		.should('be.visible')
		.as('modal');
});

Then('I should see a subscribe modal', () => {
	cy.contains('[role=dialog]', 'Subscribe to Growth Plan')
		.should('be.visible')
		.as('modal');
});

And(
	'I have to share, like or leave a review so that I can click the button {string}',
	() => {
		cy.get('@modal')
			.get('.Polaris-Button')
			.contains('OK, I’ve shared')
			.as('subscriptBtn');
	}
);

Then('I click the share button of Facebook', () => {
	cy.get('@modal')
		.get('.Polaris-Link')
		.contains('Share')
		.click();
});

Then('I should see a new tab open with default share content & link', () => {});

Then('I go back to choose a plan page', () => {});

And('I click the button {string}', text => {
	cy.get('@modal')
		.get('button')
		.contains(text)
		.click();
});

Then('I should subscribe to the free plan successfully', () => {
	cy.wait('@subscription');
});

And('I should be redirected to {string}', path => {
	cy.url().should('include', path);
});

And('see the current plan', () => {});

And('I am subscribed to Essential plan', () => {});

Then("I should see there's a Growth plan", () => {
	cy.get('.Polaris-Heading')
		.contains('Growth')
		.should('be.visible');
});

When('I choose {string} in the choose plan page', buttonText => {
	cy.get('.Polaris-Stack')
		.contains(buttonText)
		.get('button')
		.contains('Choose this plan')
		.click()
		.as('chooseBtn');
});

And('I confirm to downgrade', () => {});

Then(
	'I should see the downgrade notification banner in the current plan card in the billing page',
	() => {}
);

And("there's status badge in the choose plan page", () => {});
