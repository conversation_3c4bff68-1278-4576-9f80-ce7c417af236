# FIXME: ABU-5668
Feature: discount plan
# Scenario: visit discount plan page and see baremetrics cancellation reason modal, and cancel anyway
# 		Given I am logged in as "<EMAIL>"
# 		And my current plan is "PRO"
# 		When I try to cancel the plan
# 		# Then I should see a cancellation reason modal
# 		# And I should see a "Continue" button in the modal
# 		# When I click the "Continue" button without selecting a reason
# 		# Then I should see "Please select a cancellation reason"
# 		# And I should see <reasons> in the modal
# 		# 	| reason                       |
# 		# 	| Missing features I need      |
# 		# 	| Technical issues             |
# 		# 	| Switching to another product |
# 		# 	| Too expensive                |
# 		# 	| Other                        |
# 		# When I try to select "Too expensive"
# 		# And I click the "Continue" button
# 		Then I should see the discounted plans list
# 		And I should see a still cancel link in the page
# 		When I click the still cancel button
# 		Then I should see a cancel modal
# 		When I confirm the cancel modal
# 		Then I should see a toast msg said "Your plan will be canceled."

# 	Scenario: subscribe to a discount plan
# 		Given I am logged in as "<EMAIL>"
# 		And my current plan is "PRO"
# 		When I try to cancel the plan
# 		# Then I should see a cancellation reason modal
# 		# And I should see a "Continue" button in the modal
# 		# When I try to select "Too expensive"
# 		# And I click the "Continue" button
# 		Then I should see the discounted plans list
# 		When I subscribe the "Pro" discount plan
# 		Then I should see a subscription modal
# 		When I confirm the subscription modal
# 		Then I can see the page goto "/settings/billing"

# 	Scenario: cannot visit by directly input the URL
# 		Given I am logged in as "<EMAIL>"
# 		And my current plan is "PRO"
# 		When I visit the page address "/settings/billing/choose-plan/discount"
# 		Then I should not see discount plans in choose plan page
