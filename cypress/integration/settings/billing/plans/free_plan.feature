#RTC-5143

Feature: Free plan

    # FIXME: update this BDD
    Scenario: Free trial user subscribes to Forever free plan

#     Given I am a Free Trial user
#     Given I am logged in as "<EMAIL>"
#     When I visit "/settings/billing/choose-plan"
#     Then I should see there's a Forever free plan
#     When I click "Choose this plan" button of this plan
#     Then I should see a modal pops up
#     And I have to share, like or leave a review so that I can click the button "OK, I’ve shared"
#     When I click the share button of Facebook
#     Then I should see a new tab open with default share content & link
#     When I go back to choose a plan page
#     And I click the button "OK, I’ve shared"
#     Then I should subscribe to the free plan successfully
#     And I should be redirected to "/billing"
#     And see the current plan

# Scenario: Growth plan
#     Given I am logged in as "<EMAIL>"
#     When I visit "/settings/billing/choose-plan"
#     Then I should see there's a Growth plan
#     When I click "Choose this plan" button of Growth plan
#     Then I should see a subscribe modal
#     And I click the button "Confirm plan"
#     Then I should subscribe to the free plan successfully
#     And I should be redirected to "/billing"
#     When I visit "/settings/billing/choose-plan"

# Scenario: Subscribed user downgrade to Forever free
#     Given I am logged in as "<EMAIL>"
#     When I visit "/settings/billing/choose-plan"
#     And I am subscribed to Essential plan
#     When I choose "Forever Free" in the choose plan page
#     And I confirm to downgrade
#     Then I should see the downgrade notification banner in the current plan card in the billing page
#     And there's status badge in the choose plan page