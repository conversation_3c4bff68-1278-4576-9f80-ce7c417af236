import {When, Then, And} from 'cypress-cucumber-preprocessor/steps';

const {billingApi} = Cypress.env();

When('I try to cancel the plan', () => {
	cy.server();
	cy.route('GET', billingApi + '/me', {
		data: {
			pending_for_removal: false,
			created_at: '2018-11-26T07:53:14.912859000Z',
			updated_at: '2019-01-22T09:07:40.968000000Z',
			shopify_customers: [],
			stripe_customer: {
				id: 'cus_E2kO7IAbK9LaKV',
				user_id: '5bfba62e2852e41500a49311',
				updated_at: '2019-01-22T09:07:40.968000000Z',
				created_at: '2018-11-26T07:53:14.912859000Z',
			},
			braintree_paypal_payment_methods: [],
		},
	}).as('billingMe');
	cy.route('POST', '**/v1/events', {}).as('postCancelReason');
	cy.visit('http://localhost:3303/settings/billing/choose-plan');
	// cy.window()
	// 	.its('barecancel')
	// 	.should('have.property', 'created', true);
	// cy.window()
	// 	.its('barecancel')
	// 	.should('have.property', 'vue');
	cy.contains('Cancel subscription').click();
});

Then('I should see a cancellation reason modal', () => {
	cy.contains("We're sad to see you go").should('be.visible');
});

Then('I should see a "Continue" button in the modal', () => {
	cy.contains('button', 'Continue')
		.as('continueBtn')
		.should('be.visible');
});

When('I click the "Continue" button without selecting a reason', () => {
	cy.get('@continueBtn').click();
});

Then('I should see "Please select a cancellation reason"', () => {
	cy.contains('Please select a cancellation reason').should('be.visible');
});

Then('I should see <reasons> in the modal', dataTable => {
	const reasons = dataTable.rawTable.map(row => row[0]);
	reasons.forEach(element => {
		cy.contains(element).should('be.visible');
	});
});

When('I try to select "Too expensive"', () => {
	cy.contains('Too expensive').click();
});

And('I click the "Continue" button', () => {
	cy.get('@continueBtn').click();
	cy.wait('@postCancelReason');
});

Then('I should see the discounted plans list', () => {
	cy.contains('We offer special discount plans for you', {
		timeout: 60000,
	}).should('be.visible');
});
