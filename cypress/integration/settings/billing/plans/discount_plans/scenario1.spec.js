import {When, Then, And} from 'cypress-cucumber-preprocessor/steps';
import './common.spec';

Then('I should see a still cancel link in the page', () => {
	cy.contains('Still cancel subscription')
		.as('stillCancelBtn')
		.should('be.visible');
});

When('I click the still cancel button', () => {
	cy.get('@stillCancelBtn').click();
});

Then('I should see a cancel modal', () => {
	cy.contains('Cancel Pro Plan').should('be.visible');
});

When('I confirm the cancel modal', () => {
	cy.route('DELETE', '**/subscriptions/**').as('cancelPlanReq');
	cy.get('input#cancel-plan-input').type('CANCEL');
	cy.contains('button', 'Confirm cancellation')
		.should('be.enabled')
		.click();
});

Then('I should see a toast msg said {string}', msg => {
	cy.wait('@cancelPlanReq');
	cy.contains(msg).should('be.visible');
});
