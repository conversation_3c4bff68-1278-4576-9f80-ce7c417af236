import {When, Then, And} from 'cypress-cucumber-preprocessor/steps';
import './common.spec';

When('I subscribe the {string} discount plan', plan => {
	cy.contains('.Polaris-Card__Section', plan)
		.contains('button', 'Choose this plan')
		.click();
});

Then('I should see a subscription modal', () => {
	cy.contains('Subscribe to Pro Plan').should('be.visible');
});

When('I confirm the subscription modal', () => {
	cy.route('POST', '**/subscriptions').as('confirmPlan');
	cy.contains('button', 'Confirm plan').click();
	cy.wait('@confirmPlan');
});
