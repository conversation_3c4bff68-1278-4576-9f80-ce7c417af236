Feature: Enterprise user behaviour

	Scenario: Should not see "change plan"
		Given I am logged in as "<EMAIL>"
		When I visit the page address "/settings/billing"
		Then I should not see "change plan" on the page

	Scenario: Should be redirected to billing page when visit directly
		Given I am logged in as "<EMAIL>"
		When I visit the page address "/settings/billing/choose-plan"
		Then I can see the page goto "/settings/billing"
