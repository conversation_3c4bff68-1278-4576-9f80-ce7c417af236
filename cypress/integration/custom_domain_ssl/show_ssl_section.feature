Feature: Enable/Disable SSL

# 	Scenario Outline: User with custom domain should see SSL section
# 		Given I am logged in as "<email>"
# 		And my plan is "<plan>"
# 		And I have a custom domain as "<domain>"
# 		When I go to tracking pages list
# 		Then I should see a custom domain section
# 		And I should see the custom domain is "<domain>"
# 		And I should see SSL section

# 		Examples:
# 		| email | plan | domain |
# 		| <EMAIL> | Essential | track001.max-test.site |
