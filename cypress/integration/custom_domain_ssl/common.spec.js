/// <reference types="Cypress" />
import {Then} from 'cypress-cucumber-preprocessor/steps';

Then('my plan is {string}', plan => {
	cy.server();
});

Then('I have a custom domain as {string}', domain => {});

// Custom domain feature is acutally at /tracking-pages
Then('I go to tracking pages list', () => {
	cy.visit('http://localhost:3303/tracking-pages');
});

Then('I have not enable SSL', () => {
	cy.route(
		'GET',
		'https://secure.aftership.io/json/admin/users/custom-domains/ssl',
		{meta: {code: 200}, data: {status: 'disabled'}}
	);
	cy.route(
		'POST',
		'https://secure.aftership.io/json/admin/users/custom-domains/ssl',
		{
			meta: {code: 200},
			data: {cloudflare_hostname_id: '1', status: 'initializing'},
		}
	).as('enableSSLRequest');
});

Then('I have enabled SSL', () => {
	cy.route(
		'GET',
		'https://secure.aftership.io/json/admin/users/custom-domains/ssl',
		{
			meta: {code: 200},
			data: {cloudflare_hostname_id: '1', status: 'active'},
		}
	);
	cy.route(
		'DELETE',
		'https://secure.aftership.io/json/admin/users/custom-domains/ssl',
		{meta: {code: 200}, data: {cloudflare_hostname_id: '1'}}
	);
});

Then('I should see "Enable SSL" button', () => {
	cy.get('[data-test-id="CUSTOM-DOMAIN-SSL"]').contains(
		'button',
		/Enable.+SSL/
	);
});

Then('I should see "Disable SSL" button', () => {
	cy.get('[data-test-id="CUSTOM-DOMAIN-SSL"]').contains(
		'button',
		/Disable.+SSL/
	);
});

Then('I should not see the "Disable SSL" button', () => {
	cy.get('[data-test-id="CUSTOM-DOMAIN-SSL"]')
		.find('button')
		.should('not.contain', /Disable.+SSL/);
});

Then('I click "Enable SSL" button', () => {
	cy.get('[data-test-id="CUSTOM-DOMAIN-SSL"]')
		.contains('button', /Enable.+SSL/)
		.click();
});

Then('I click "Disable SSL" button', () => {
	cy.get('[data-test-id="CUSTOM-DOMAIN-SSL"]')
		.contains('button', /Disable.+SSL/)
		.click();
});

Then('I should see the "Enable SSL" button is disabled', () => {
	cy.get('[data-test-id="CUSTOM-DOMAIN-SSL"]').contains(
		'button[disabled]',
		/Enabling.+SSL/
	);
});
