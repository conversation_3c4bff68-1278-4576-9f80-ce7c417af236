Feature: Enable/Disable SSL

# 	Background:
# 		Given I am logged in as "<EMAIL>"
# 		And my plan is "Essential"
# 		And I have a custom domain as "track001.max-test.site"

# 	Scenario: Non-enterpiese user with custom domain enable SSL
# 		Given I have not enable SSL
# 		When I go to tracking pages list
# 		Then I should see "Enable SSL" button
# 		When I click "Enable SSL" button
# 		Then I should see a enable ssl confirm dialog
# 		And I should see the "Enable" button
# 		When I click the "Enable" button
# 		Then I should see the "Enable SSL" button is disabled

# 	Scenario: Non-enterpiese user with custom domain disable SSL
# 		Given I have enabled SSL
# 		When I go to tracking pages list
# 		Then I should see "Disable SSL" button
# 		When I click "Disable SSL" button
# 		Then I should see a disable ssl confirm dialog
# 		And I should see the "Disable" button
# 		When I click the "Disable" button
# 		Then I should not see the "Disable SSL" button
# 		And I should see "Enable SSL" button
