/// <reference types="Cypress" />
import {Then} from 'cypress-cucumber-preprocessor/steps';
import '../common.spec';

Then('I should see a enable ssl confirm dialog', () => {
	cy.get('[data-test-id="ENABLE-SSL-MODAL-CONTENT"]');
});

Then('I should see a disable ssl confirm dialog', () => {
	cy.get('[data-test-id="DISABLE-SSL-MODAL-CONTENT"]');
});

Then('I should see the "Enable" button', () => {
	cy.get('[data-test-id="ENABLE-SSL-MODAL-CONTENT"]')
		.parents('[role="dialog"]')
		.contains('button', 'Enable');
});

Then('I click the "Enable" button', () => {
	cy.get('[data-test-id="ENABLE-SSL-MODAL-CONTENT"]')
		.parents('[role="dialog"]')
		.contains('button', 'Enable')
		.click();
});

Then('I should see the "Disable" button', () => {
	cy.get('[data-test-id="DISABLE-SSL-MODAL-CONTENT"]')
		.parents('[role="dialog"]')
		.contains('button', 'Disable');
});

Then('I click the "Disable" button', () => {
	cy.get('[data-test-id="DISABLE-SSL-MODAL-CONTENT"]')
		.parents('[role="dialog"]')
		.contains('button', 'Disable')
		.click();
});
