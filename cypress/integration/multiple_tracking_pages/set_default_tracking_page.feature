Feature: Set default tracking page.

	Rules:
		- Must have a default tracking page
		- Can only have 1 default tracking page
		- Default tracking page cannot be deleted

	Sc<PERSON>rio: A user try to set a tracking page as default.
		Given I am logged in as "<EMAIL>"
		And I have 2 tracking pages
		When I visit the page address "/tracking-pages"
		Then I should see 2 tracking pages
		And I should see the "Set default" disabled for the default page
		And I should see the "Set default" available for the non default page
		When I set the non default page as default
		Then I should see the default badge for the new default page
		And I should not see the default badge for the old default page
