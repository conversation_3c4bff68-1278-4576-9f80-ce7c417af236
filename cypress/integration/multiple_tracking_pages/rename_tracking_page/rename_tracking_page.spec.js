/// <reference types="Cypress" />
import {Then, When} from 'cypress-cucumber-preprocessor/steps';
import '../common.spec';

// Scenario 1
let oldPageName;
let newPageName;
Then('I rename the tracking page to a new name', () => {
	cy.get('@pageItems')
		.find('.Polaris-ResourceItem__Container:contains("Default")')
		.as('defaultPage')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();
	cy.get('@defaultPage')
		.find('.Polaris-TextStyle--variationStrong')
		.invoke('text')
		.then(pageName => {
			oldPageName = pageName;
			// making new page name does not partially equal the old name
			newPageName = `${oldPageName.slice(0, -4)}-${Math.floor(
				Math.random() * Math.floor(1000)
			)}`;
			cy.get('.Polaris-PositionedOverlay')
				.as('dropDown')
				.should('be.visible');
			cy.get('@dropDown')
				.contains('Rename')
				.click();
			cy.get('[role=dialog]:contains("Rename tracking page")')
				.as('renameModal')
				.should('be.visible');
			cy.get('@renameModal')
				.find('input[name=pageName]')
				.clear()
				.type(newPageName);
			cy.get('@renameModal')
				.find('button:contains("Rename")')
				.click();
		});
});

Then(
	'I should see the tracking page renamed in the tracking pages list',
	() => {
		cy.get(
			`.Polaris-ResourceItem__Container:contains("${newPageName}")`
		).should('be.visible');
		cy.get(
			`.Polaris-ResourceItem__Container:contains("${oldPageName}")`
		).should('not.be.visible');
	}
);

// Scenario 2
let firstPageName;
let secondPageName;
Then("I should see 2 tracking pages' name", () => {
	cy.get('@pageItems')
		.eq(0)
		.as('firstPage');
	cy.get('@pageItems')
		.eq(1)
		.as('secondPage');
	cy.get('@firstPage')
		.find('.Polaris-TextStyle--variationStrong')
		.invoke('text')
		.then(pageName => {
			firstPageName = pageName;
		});
	cy.get('@secondPage')
		.find('.Polaris-TextStyle--variationStrong')
		.invoke('text')
		.then(pageName => {
			secondPageName = pageName;
		});
});

Then('I rename the second page with the same name as the first page', () => {
	cy.get('@secondPage')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();

	cy.get('.Polaris-PositionedOverlay')
		.as('dropDown')
		.should('be.visible');
	cy.get('@dropDown')
		.contains('Rename')
		.click();
	cy.get('[role=dialog]:contains("Rename tracking page")')
		.as('renameModal')
		.should('be.visible');
	cy.get('input[name=pageName]')
		.clear()
		.type(firstPageName);
	cy.get('@renameModal')
		.find('button:contains("Rename")')
		.should('be.disabled');
});

Then('I should see an tracking page existed error message', () => {
	cy.get('@renameModal')
		.contains('.Polaris-InlineError', 'Tracking page name already existed')
		.should('be.visible');
	cy.get('@renameModal')
		.find('button:contains("Cancel")')
		.click();
});

// Scenario 3
When('I rename the tracking page to {string}', pageName => {
	cy.get('@pageItems')
		.eq(0)
		.as('firstPage')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();
	cy.get('.Polaris-PositionedOverlay')
		.as('dropDown')
		.should('be.visible');
	cy.get('@dropDown')
		.contains('Rename')
		.click();
	cy.get('[role=dialog]:contains("Rename tracking page")')
		.as('renameModal')
		.should('be.visible');
	cy.get('input[name=pageName]')
		.clear()
		.type(pageName);
	cy.get('@renameModal')
		.find('button:contains("Rename")')
		.should('be.disabled');
});

Then('I should see an invalid character error message', () => {
	cy.get('@renameModal')
		.contains(
			'.Polaris-InlineError',
			'Invalid name. Name should be number, letter, hyphen or underline.'
		)
		.should('be.visible');
	cy.get('@renameModal')
		.find('button:contains("Cancel")')
		.click();
});
