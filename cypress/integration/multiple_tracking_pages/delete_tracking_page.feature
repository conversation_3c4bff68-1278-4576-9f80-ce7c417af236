Feature: Delete tracking page
	Scenario: Delete a tracking page
		Given I am logged in as "<EMAIL>"
		And I have 2 tracking pages
		When I visit the page address "/tracking-pages"
		Then I should see 2 tracking pages
		And I should see "Delete" disabled in default tracking page
		And I should see "Delete" action in non default tracking page
		When I click delete
		Then I should see a confirmation dialog
		When I click modal delete
		Then I should see 1 tracking pages
