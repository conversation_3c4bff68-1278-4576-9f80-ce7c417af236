/// <reference types="Cypress" />
import {Then} from 'cypress-cucumber-preprocessor/steps';
import {orderBy} from 'lodash';
import '../common.spec';

Then('I should see tracking pages list', () => {
	cy.get('[data-test-id="tracking-pages-list"]').should('be.visible');
});

Then(
	'I should see 3 tracking pages are sorted in ascending order by created time',
	() => {
		cy.wait('@getTrackingPages').then(xhr => {
			const pageNames = orderBy(
				xhr.response.body.data.tracking_pages,
				'created_at',
				'asc'
			).map(page => page.page_name);
			cy.get('.Polaris-ResourceItem__Container').as('pageItems');
			cy.get('@pageItems').should('have.length', 3);
			cy.get('@pageItems')
				.eq(0)
				.should('contain', pageNames[0]);
			cy.get('@pageItems')
				.eq(1)
				.should('contain', pageNames[1]);
			cy.get('@pageItems')
				.eq(2)
				.should('contain', pageNames[2]);
		});
	}
);
