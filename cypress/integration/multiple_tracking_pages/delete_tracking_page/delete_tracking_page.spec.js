/// <reference types="Cypress" />
import {Then, When} from 'cypress-cucumber-preprocessor/steps';
import '../common.spec';

Then('I should see "Delete" disabled in default tracking page', () => {
	cy.get('@pageItems')
		.get('.Polaris-ResourceItem__Container:contains("Default")')
		.find('.Polaris-ResourceItem__Disclosure')
		.as('defaultCardActionBtn')
		.click();
	cy.get('.Polaris-PositionedOverlay')
		.as('dropDown')
		.should('be.visible');
	cy.get('@dropDown')
		.contains('Delete')
		.should('be.disabled');
	// close dropdown
	cy.get('@defaultCardActionBtn').click();
});

Then('I should see "Delete" action in non default tracking page', () => {
	cy.get('@pageItems')
		.get('.Polaris-ResourceItem__Container:not(:contains("Default"))')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();
	cy.get('.Polaris-PositionedOverlay')
		.as('dropDown')
		.should('be.visible');
	cy.get('@dropDown')
		.contains('Delete')
		.as('deleteBtn')
		.should('not.be.disabled');
});

When('I click delete', () => {
	cy.get('@deleteBtn').click();
});

When('I click modal delete', () => {
	cy.get('@deleteModal')
		.find('.Polaris-Button--primary')
		.contains('Delete')
		.click();
});

Then('I should see a confirmation dialog', () => {
	cy.get('.Polaris-Modal-Dialog__Modal')
		.as('deleteModal')
		.contains('Delete tracking page')
		.should('be.visible');
});
