Feature: Rename a tracking page.

	Rules:
	- Same as add tracking page.

	Scenario: A user try to rename the tracking page.
		Given I am logged in as "<EMAIL>"
		And I have 1 tracking pages
		When I visit the page address "/tracking-pages"
		Then I should see 1 tracking pages
		And I rename the tracking page to a new name
		Then I should see the tracking page renamed in the tracking pages list

	<PERSON><PERSON><PERSON>: A user try to rename the tracking page to an existing name, but failed.
		Given I am logged in as "<EMAIL>"
		And I have 2 tracking pages
		When I visit the page address "/tracking-pages"
		Then I should see 2 tracking pages
		Then I should see 2 tracking pages' name
		When I rename the second page with the same name as the first page
		Then I should see an tracking page existed error message

	<PERSON><PERSON><PERSON>: A user try to rename the tracking page with invalid characters, but failed.
		Given I am logged in as "<EMAIL>"
		And I have 1 tracking pages
		When I visit the page address "/tracking-pages"
		Then I should see 1 tracking pages
		When I rename the tracking page to "tracking page"
		Then I should see an invalid character error message
