/// <reference types="Cypress" />
import {Then, When} from 'cypress-cucumber-preprocessor/steps';
import '../common.spec';
let createdPage;
let firstPageName;

Then('I should see an add tracking page button', () => {
	// Ry.zou: I don't know why it exists two primary action buttons here,
	// I have to select it this way.
	cy.contains('button', 'Add tracking page').as('addButton');
	cy.get('@addButton').should('be.visible');
});

When('I click add tracking page button', () => {
	cy.get('@addButton').click();
});
When('I should not able to click add tracking page button', () => {
	cy.get('@addButton').should('be.disabled');
});

Then('I should see a dialog', () => {
	cy.contains('[role=dialog]', 'Add tracking page')
		.as('addModal')
		.should('be.visible');
	cy.get('input[name=pageName]').as('input');
});

When('I input tracking page name as {string}', pageName => {
	cy.get('@input')
		.clear()
		.type(pageName);
});
Then('I should see an error msg {string}', errMsg => {
	cy.contains('.Polaris-Labelled__Error', errMsg);
});

When('I click submit button', () => {
	cy.contains('button', 'Save').as('submitBtn');
	cy.route('POST', '/v5/tracking_pages').as('createPageRequest');
	cy.get('@submitBtn').click();
	cy.wait('@createPageRequest').then(xhr => {
		createdPage = xhr.response.body.data.tracking_page;
	});
});

Then('I should not see the dialog', () => {
	cy.get('@addModal').should('not.be.visible');
});

Then('I am redirected to the created tracking page detail', () => {
	cy.url().should('include', createdPage.id);
});

When('I go back to the tracking pages list', () => {
	cy.get('[role=navigation] .Polaris-Breadcrumbs__Breadcrumb').click();
});

Then('the last tracking page name is {string}', pageName => {
	cy.get('@pageItems')
		.eq(-1)
		.contains(pageName);
});

Then('I should see upgrade dialog', () => {
	cy.contains('[role=dialog]', 'Upgrade and contact sales').should(
		'be.visible'
	);
});
