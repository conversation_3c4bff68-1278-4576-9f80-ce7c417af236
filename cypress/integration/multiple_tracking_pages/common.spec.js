/// <reference types="Cypress" />
import {Given, Then, When} from 'cypress-cucumber-preprocessor/steps';

Given('I have {int} tracking page/pages', number => {
	cy.server();
	cy.route('GET', '/v5/tracking_pages').as('getTrackingPages');
	cy.callSecure('get', '/v5/tracking_pages').then(res => {
		const pages = res.body.data.tracking_pages;
		const count = pages.length;

		if (count < number) {
			const countToCreate = number - count;
			const pageMock = {
				page_name: 'tracking-page',
				store_name: 'mock-store',
				store_url: 'http://mock-store.com',
				default: false,
				theme_name: 'sake',
				theme_settings: {
					lookup_options: {
						enable_order_number: false,
						enable_tracking_number: true,
					},
				},
			};
			for (let i = 0; i < countToCreate; i++) {
				cy.wait(10);
				cy.callSecure('post', '/v5/tracking_pages', {
					tracking_page: {
						...pageMock,
						page_name: `tracking-page-${i}-${Math.floor(
							Math.random() * Math.floor(1000)
						)}`,
					},
				});
			}
		} else if (count > number) {
			const countToDelete = count - number;
			for (let i = 0; i <= countToDelete; i++) {
				if (!pages[i].default) {
					cy.callSecure(
						'delete',
						'/v5/tracking_pages/' + pages[i].id
					);
				}
			}
		}
	});
});

Then('I should see {int} tracking page/pages', count => {
	cy.wait('@getTrackingPages');
	cy.get('.Polaris-ResourceList__ItemWrapper').as('pageItems');
	cy.get('@pageItems').should('have.length', count);
});
