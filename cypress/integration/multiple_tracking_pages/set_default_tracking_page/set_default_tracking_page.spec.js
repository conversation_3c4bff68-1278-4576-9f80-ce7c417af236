/// <reference types="Cypress" />
import {Then, When} from 'cypress-cucumber-preprocessor/steps';
import '../common.spec';

let oldDefaultPageName;
let newDefaultPageName;
Then('I should see the "Set default" disabled for the default page', () => {
	cy.get('@pageItems')
		.find('.Polaris-ResourceItem__Container:contains("Default")')
		.as('defaultPage')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();
	cy.get('@defaultPage')
		.find('.Polaris-TextStyle--variationStrong')
		.invoke('text')
		.then(pageName => (oldDefaultPageName = pageName));
	cy.get('.Polaris-PositionedOverlay')
		.as('dropDown')
		.should('be.visible');
	cy.get('@dropDown')
		.contains('Set default')
		.should('be.disabled');
	cy.contains('Tracking pages list').click();
});

Then(
	'I should see the "Set default" available for the non default page',
	() => {
		cy.get('@pageItems')
			.find('.Polaris-ResourceItem__Container:not(:contains("Default"))')
			.as('nonDefaultPage')
			.find('.Polaris-ResourceItem__Disclosure')
			.click();
		cy.get('@nonDefaultPage')
			.find('.Polaris-TextStyle--variationStrong')
			.invoke('text')
			.then(pageName => (newDefaultPageName = pageName));
		cy.get('.Polaris-PositionedOverlay')
			.as('dropDown')
			.should('be.visible');
		cy.get('@dropDown')
			.contains('Set default')
			.as('setDefaultBtn')
			.should('not.be.disabled');
	}
);

When('I set the non default page as default', () => {
	cy.get('@setDefaultBtn').click();
});

Then('I should see the default badge for the new default page', () => {
	cy.get(
		`.Polaris-ResourceItem__Container:contains("${newDefaultPageName}")`
	).should('contain', 'Default');
});

Then('I should not see the default badge for the old default page', () => {
	cy.get(
		`.Polaris-ResourceItem__Container:contains("${oldDefaultPageName}")`
	).should('not.contain', 'Default');
});
