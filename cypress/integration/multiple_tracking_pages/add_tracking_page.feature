Feature: Add more tracking pages
    Scenario: Enterprise merchant add tracking page
# 	Given I am logged in as "<EMAIL>"
# 	And I have 1 tracking page
# 	When I visit the page address "/tracking-pages"
# 	Then I should see 1 tracking pages
# 	And I should see an add tracking page button
# 	When I click add tracking page button
# 	Then I should see a dialog
# 	When I input tracking page name as "@another"
# 	Then I should see an error msg "Invalid name. Name should be number, letter, hyphen or underline."
# 	When I input tracking page name as "another"
# 	And I click submit button
# 	Then I am redirected to the created tracking page detail
# 	When I go back to the tracking pages list
# 	Then I should see 2 tracking pages
# 	And the last tracking page name is "another"

# Scenario: Enterprise can only add 6 tracking page
# 	Given I am logged in as "<EMAIL>"
# 	And I have 6 tracking pages
# 	When I visit the page address "/tracking-pages"
# 	Then I should see an add tracking page button
# 	And I should not able to click add tracking page button

# Scenario: Non-enterprise merchant can't add tracking page
# 	Given I am logged in as "<EMAIL>"
# 	And I have 1 tracking page
# 	When I visit the page address "/tracking-pages"
# 	Then I should see an add tracking page button
# 	When I click add tracking page button
# 	Then I should see upgrade dialog
