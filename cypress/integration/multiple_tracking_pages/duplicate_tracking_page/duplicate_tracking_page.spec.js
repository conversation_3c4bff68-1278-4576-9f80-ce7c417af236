/// <reference types="Cypress" />
import {Then, When} from 'cypress-cucumber-preprocessor/steps';
import '../common.spec';

// Scenario 1
let oldPageName;
let newPageName;
Then('I duplicate the default page', () => {
	cy.get('@pageItems')
		.find('.Polaris-ResourceItem__Container:contains("Default")')
		.as('defaultPage')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();
	cy.get('@defaultPage')
		.find('.Polaris-TextStyle--variationStrong')
		.invoke('text')
		.then(pageName => {
			oldPageName = pageName;
			newPageName = `copy-of-${pageName}`;

			cy.get('.Polaris-PositionedOverlay')
				.as('dropDown')
				.should('be.visible');
			cy.get('@dropDown')
				.contains('Duplicate')
				.click();
		});
});

Then('I should see the page name dialog', () => {
	cy.get('[role=dialog]:contains("Duplicate tracking page")')
		.as('duplicateModal')
		.should('be.visible');
});
Then('I should see the default page name is prefixed by "copy-of-"', () => {
	cy.get('@duplicateModal')
		.find('input[name=pageName]')
		.should('have.value', newPageName);
});
When('I confirm the tracking page', () => {
	cy.get('@duplicateModal')
		.find('button:contains("Duplicate")')
		.click();
});
Then('I should see the original tracking page is default', () => {
	cy.get('@pageItems')
		.find('.Polaris-ResourceItem__Container:contains("Default")')
		.should('contain', oldPageName);
});
Then('I should not see the duplicated tracking page is default page', () => {
	cy.get('@pageItems')
		.find('.Polaris-ResourceItem__Container:not(:contains("Default"))')
		.should('contain', newPageName);
});

// Scenario 2

When("I change the page name to the same as the original page's name", () => {
	cy.get('@duplicateModal')
		.find('input[name=pageName]')
		.clear()
		.type(oldPageName);
});

Then('I should see an tracking page existed error message', () => {
	cy.get('@duplicateModal')
		.contains('.Polaris-InlineError', 'Tracking page name already existed')
		.should('be.visible');
	cy.get('@duplicateModal')
		.find('button:contains("Cancel")')
		.click();
});

// Scenario 3
Then('I should see duplicate action is disabled', () => {
	cy.get('@pageItems')
		.eq(0)
		.as('firstPage')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();
	cy.get('.Polaris-PositionedOverlay')
		.as('dropDown')
		.should('be.visible');
	cy.get('@dropDown')
		.contains('Duplicate')
		.should('be.disabled');
});

// Scenario 4
When('I click duplicate tracking page action', () => {
	cy.get('@pageItems')
		.eq(0)
		.as('firstPage')
		.find('.Polaris-ResourceItem__Disclosure')
		.click();
	cy.get('.Polaris-PositionedOverlay')
		.as('dropDown')
		.should('be.visible');
	cy.get('@dropDown')
		.contains('Duplicate')
		.click();
});

Then('I should see upgrade dialog', () => {
	cy.contains('[role=dialog]', 'Upgrade and contact sales').should(
		'be.visible'
	);
	cy.get('.Polaris-Modal-CloseButton').click();
});
