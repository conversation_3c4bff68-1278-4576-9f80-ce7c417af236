Feature: View tracking pages list
	<PERSON><PERSON><PERSON>: The list should be sorted in ascending order by created time
		Given I am logged in as "<EMAIL>"
		And I have 3 tracking pages
		When I visit the page address "/tracking-page"
		Then I can see the page goto "/tracking-pages"
		And I should see tracking pages list
		And I should see 3 tracking pages are sorted in ascending order by created time
