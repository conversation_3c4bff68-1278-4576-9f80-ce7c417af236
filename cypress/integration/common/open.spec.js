/// <reference types="Cypress" />
import {Given, Then} from 'cypress-cucumber-preprocessor/steps';

Then('I visit home page', () => {
	cy.visit('http://localhost:3303');
});

Then('I go to tracking page', () => {
	cy.visit('http://localhost:3303/tracking-page');
});

Then('I visit the page address {string}', address => {
	if (address.indexOf('http') !== 0) {
		address = 'http://localhost:3303' + address;
	}
	cy.visit(address);
});

Then('I can see the page goto {string}', address => {
	if (address.indexOf('http') !== 0) {
		address = 'http://localhost:3303' + address;
	}
	cy.url().should('eq', address);
});
