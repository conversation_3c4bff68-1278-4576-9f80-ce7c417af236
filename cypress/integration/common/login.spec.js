import {Given, Then} from 'cypress-cucumber-preprocessor/steps';

Given('I logged in as {string} with password {string}', (user, password) => {
	cy.login(user, password);
});

Given('I am logged in as {string}', email => {
	cy.login(email, '**********');
});

// Make sure to delete the user after scenario
// using "Then I delete my account"
// Given('I am logged in as a newly registered user', () => {
// 	const uniqueId = () => `${Math.floor(Math.random() * Math.floor(100000))}`;
// 	const username = `onboarding+${uniqueId()}`;
// 	const email = `${username}@aftership.com`;
// 	const fullName = `${username}`;
// 	cy.createUser({
// 		email,
// 		fullName,
// 	}).then(({email, password}) => {
// 		cy.wrap({email: email}).as('newUserEmail');
// 		cy.login(email, password);
// 	});
// });

Then('I delete my account', () => {
	cy.deleteUser();
});
