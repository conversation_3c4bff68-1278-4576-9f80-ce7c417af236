// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This is will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

/* eslint-disable-next-line */
const uniqueId = () => `${Math.floor(Math.random() * ************)}`;
const KcAdminClient = require('keycloak-admin').default;

const getAuthToken = () => {
	return cy.window().then(({sso}) => {
		let authInfo;

		authInfo = JSON.parse(localStorage.getItem('access_token'));

		return authInfo ? `${authInfo.token}` : null;
	});
};

const getLegacyToken = ({accountId, token, tokenType = 'Bearer'}) => {
	const audience =
		'api.aftership.com api.returnscenter.com apps.aftership.com';
	const scope = 'returnscenter-retailer aftership-user';
	return cy
		.request({
			method: 'get',
			url: `${Cypress.env(
				'businessApi'
			)}/accounts/${accountId}/legacy-token?scope=${scope}&audience=${audience}`,
			headers: {
				Accept: 'application/json',
				Authorization: `${tokenType} ${token}`,
			},
		})
		.then(
			({
				body: {
					data: {legacy_token},
				},
			}) => {
				return legacy_token;
			}
		);
};

const setOrgId = async ({accountId, token, tokenType = 'Bearer'}) => {
	return cy
		.request({
			method: 'get',
			url: `https://api.automizely.io/businesses/v1/memberships?account_id=${accountId}`,
			headers: {
				'Content-Type': 'application/json',
				Authorization: `${tokenType} ${token}`,
			},
			params: {
				account_id: accountId,
			},
		})
		.then(
			({
				body: {
					data: {memberships},
				},
			}) => {
				const orgList = memberships.map(item => ({
					...item.organization,
					role: item.role,
				}));

				const orgId =
					orgList && orgList[0] ? orgList[0].id : 'mockOrgId';
				localStorage.setItem('am_org_id', JSON.stringify(orgId));

				return orgId;
			}
		);
};

const redirect = url => {
	cy.visit(url);
};

const storeLegacyToken = legacy_token => {
	localStorage.setItem(
		'access_token',
		JSON.stringify({
			type: 'bearer',
			token: legacy_token,
		})
	);
};

const getAccessToken = (username, password) => {
	return cy
		.request({
			method: 'post',
			form: true,
			url:
				'https://accounts.automizely.io/auth/realms/business/protocol/openid-connect/token',
			body: {
				grant_type: 'password',
				client_id: 'admin-cli',
				username: username,
				password: password,
			},
		})
		.then(({body: {access_token}}) => ({
			accountId: parseJwt(access_token).account_id,
			token: access_token,
		}));
};

const parseJwt = token => {
	const base64Url = token.split('.')[1];
	const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
	const jsonPayload = decodeURIComponent(
		atob(base64)
			.split('')
			.map(function(c) {
				return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
			})
			.join('')
	);

	return JSON.parse(jsonPayload);
};

Cypress.Commands.add('login', (email, password) => {
	ciLogin(email, password);
});

let createdEmail;

Cypress.Commands.add('deleteUser', (email = createdEmail) => {
	cy.request({
		method: 'post',
		url: 'https://accounts.aftership.io/session',
		body: {
			email,
			password: Cypress.env('MASTER_PASSWORD'),
		},
	}).then(() => {
		cy.request({
			method: 'delete',
			url: 'https://accounts.aftership.io/user',
			body: {
				password: Cypress.env('MASTER_PASSWORD'),
			},
		});
	});
});

Cypress.Commands.add('callSecure', (method, url, body) => {
	return getAuthToken().then(access_token => {
		return cy.request({
			method,
			url: 'https://secure.aftership.io' + url,
			auth: {
				bearer: access_token,
			},
			body,
		});
	});
});

Cypress.Commands.add('callBilling', (method, url, body) => {
	return getAuthToken().then(access_token => {
		return cy.request({
			method,
			url: 'https://billing.aftership.io' + url,
			auth: {
				bearer: access_token,
			},
			body,
		});
	});
});

Cypress.Commands.add('handleRawTable', ({rawTable}) => {
	const titles = rawTable.shift();

	if (!titles) return [];

	const list = rawTable.reduce(
		(sum, current) => [
			...sum,
			titles.reduce(
				(obj, title, index) => ({
					...obj,
					[title]: current[index],
				}),
				{}
			),
		],
		[]
	);
	return list;
});

const ciLogin = (email, password) => {
	return getAccessToken(email, password)
		.then(args => {
			localStorage.setItem(
				'original_access_token',
				JSON.stringify({
					type: 'bearer',
					token: args.token,
				})
			);
			localStorage.setItem('accountId', JSON.stringify(args.accountId));
			return setOrgId(args).then(() => getLegacyToken(args));
		})
		.then(storeLegacyToken);
};
