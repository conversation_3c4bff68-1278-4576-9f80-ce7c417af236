{"meta": {"code": "OK", "message": "OK"}, "data": {"subscriptions": [{"id": "4a23a4fc-b550-4b74-892a-972c615e415a", "plan": {"id": "8682e48dfd0a40b8a9fdea25dda07516", "code": "AS_UNLIMITED_FREE_2020JUN", "name": "Free", "metadata": {"category": "Free", "variant": "normal"}, "product": {"code": "aftership", "name": "Free"}, "service": {"code": "AS_UNLIMITED_FREE_2020JUN", "name": "Free", "features": [{"code": "AS_FEATURE_SHIPMENTS_PAGINATION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_SEARCH", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_FILTER", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_CSV_IMPORT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_CSV_EXPORT", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_WEBHOOK", "options": {}, "enable": false}, {"code": "AS_FEATURE_REPORTS_SHIPMENTS_REPORTS", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_CUSTOM_DOMAIN", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_SSL", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_FREE_SSL", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_MULTIPLE_LANGUAGE", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_NOSTO_PLUGIN", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_MULTIPLE_TRACKING_PAGE", "options": {}, "enable": false}, {"code": "AS_FEATURE_API_PUBLIC_API", "options": {}, "enable": false}, {"code": "AS_FEATURE_API_DEFAULT_RATE_LIMIT", "options": {"duration": "1", "value": "0"}, "enable": true}, {"code": "AS_FEATURE_API_CUSTOMIZE_RATE_LIMIT", "options": {}, "enable": false}], "quotas": [{"usage_type": "tracking", "unit_label": "shipment", "quota": 50, "extra_pricing": {"currency": "", "scheme": "", "amount": 0}, "is_allow_extra": false}], "meters": [{"usage_type": "sms", "unit_label": "message"}], "credits": []}, "billing_interval": {"unit": "day", "count": 1, "display_text": "day"}, "trial_period": {"unit": "day", "count": 0, "display_text": "unavailable"}, "status": "active", "type": "main", "is_enterprise": false, "channel": {"code": "default"}, "level": 0, "dependency_level": {"start": 0}, "pricing": {"currency": "USD", "scheme": "per_unit", "amount": 0}, "gateways": [{"id": "88eed0daea0d43e7b63ae2d2309e6e0b", "type": "stripe"}]}, "quantity": 1, "status": "active", "organization": {"id": "dc99637c91fe408a863af0ad41a52660"}, "current_period": {"start_at": "2020-08-21T10:37:46+00:00", "end_at": "2020-09-20T10:37:46+00:00"}, "maintained_by": "legacy-billing", "gateway": {"id": "88eed0daea0d43e7b63ae2d2309e6e0b", "type": "stripe"}, "active": true, "upcoming": {"quantity": 1, "plan": {"id": "8682e48dfd0a40b8a9fdea25dda07516", "code": "AS_UNLIMITED_FREE_2020JUN", "name": "Free", "metadata": {"category": "Free", "variant": "normal"}, "product": {"code": "aftership", "name": "Free"}, "service": {"code": "AS_UNLIMITED_FREE_2020JUN", "name": "Free", "features": [{"code": "AS_FEATURE_SHIPMENTS_PAGINATION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_SEARCH", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_FILTER", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_CSV_IMPORT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SHIPMENTS_CSV_EXPORT", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_CUSTOMER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_SUBSCRIBER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_EMAIL_TO_YOURSELF_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_WEBHOOK", "options": {}, "enable": false}, {"code": "AS_FEATURE_REPORTS_SHIPMENTS_REPORTS", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_CUSTOM_DOMAIN", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_SSL", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_FREE_SSL", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_MULTIPLE_LANGUAGE", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_NOSTO_PLUGIN", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_MULTIPLE_TRACKING_PAGE", "options": {}, "enable": false}, {"code": "AS_FEATURE_API_PUBLIC_API", "options": {}, "enable": false}, {"code": "AS_FEATURE_API_DEFAULT_RATE_LIMIT", "options": {"duration": "1", "value": "0"}, "enable": true}, {"code": "AS_FEATURE_API_CUSTOMIZE_RATE_LIMIT", "options": {}, "enable": false}], "quotas": [{"usage_type": "tracking", "unit_label": "shipment", "quota": 50, "extra_pricing": {"currency": "", "scheme": "", "amount": 0}, "is_allow_extra": false}], "meters": [{"usage_type": "sms", "unit_label": "message"}], "credits": []}, "billing_interval": {"unit": "day", "count": 1, "display_text": "day"}, "trial_period": {"unit": "day", "count": 0, "display_text": "unavailable"}, "status": "active", "type": "main", "is_enterprise": false, "channel": {"code": "default"}, "level": 0, "dependency_level": {"start": 0}, "pricing": {"currency": "USD", "scheme": "per_unit", "amount": 0}, "gateways": [{"id": "88eed0daea0d43e7b63ae2d2309e6e0b", "type": "stripe"}]}}, "payment_status": "paid", "type": "normal", "revision": -1, "created_at": "", "updated_at": ""}, {"id": "f1c79fef-0060-42f1-ab40-7a130d983f22", "plan": {"id": "d8d7bc2be894491fb17e2fb803fd263f", "code": "AS_TRIAL", "name": "Trial", "metadata": {"variant": "normal"}, "product": {"code": "aftership", "name": "AfterShip Free Trial"}, "service": {"code": "AS_TRIAL", "name": "Trial", "features": [{"code": "AS_FEATURE_SMS_TO_CUSTOMER_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_CUSTOMER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_SUBSCRIBER_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_INFO_RECEIVED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_IN_TRANSIT", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_OUT_FOR_DELIVERY", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_AVAILABLE_FOR_PICKUP", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_DELIVERED", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_EXCEPTION", "options": {}, "enable": false}, {"code": "AS_FEATURE_SMS_TO_YOURSELF_FAILED_ATTEMPT", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_CUSTOM_DOMAIN", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_SSL", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_FREE_SSL", "options": {}, "enable": false}, {"code": "AS_FEATURE_TRACKING_PAGE_MULTIPLE_TRACKING_PAGE", "options": {}, "enable": false}, {"code": "AS_FEATURE_API_DEFAULT_RATE_LIMIT", "options": {"duration": "86400", "value": "100"}, "enable": true}, {"code": "AS_FEATURE_API_CUSTOMIZE_RATE_LIMIT", "options": {}, "enable": false}], "quotas": [], "meters": [{"usage_type": "sms", "unit_label": "message"}], "credits": []}, "billing_interval": {"unit": "day", "count": 14, "display_text": "14 days"}, "trial_period": {"unit": "day", "count": 14, "display_text": "14 days"}, "status": "active", "type": "trial", "is_enterprise": false, "channel": {"code": "default"}, "level": 0, "dependency_level": {"start": 0}, "pricing": {"currency": "USD", "scheme": "per_unit", "amount": 0}, "gateways": [{"id": "88eed0daea0d43e7b63ae2d2309e6e0b", "type": "stripe"}]}, "quantity": 1, "status": "canceled", "organization": {"id": "dc99637c91fe408a863af0ad41a52660"}, "current_period": {"start_at": "2020-07-22T10:06:07+00:00", "end_at": "2020-08-05T10:06:07+00:00"}, "maintained_by": "legacy-billing", "gateway": {"id": "88eed0daea0d43e7b63ae2d2309e6e0b", "type": "stripe"}, "active": false, "upcoming": null, "payment_status": "paid", "type": "normal", "revision": -1, "created_at": "", "updated_at": ""}], "usage": 50}}