<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1" />
		<title>Info - AfterShip</title>
		<link
			rel="apple-touch-icon"
			sizes="57x57"
			href="https://assets.aftership.com/favicons/apple-touch-icon-57x57.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="60x60"
			href="https://assets.aftership.com/favicons/apple-touch-icon-60x60.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="72x72"
			href="https://assets.aftership.com/favicons/apple-touch-icon-72x72.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="76x76"
			href="https://assets.aftership.com/favicons/apple-touch-icon-76x76.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="114x114"
			href="https://assets.aftership.com/favicons/apple-touch-icon-114x114.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="120x120"
			href="https://assets.aftership.com/favicons/apple-touch-icon-120x120.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="144x144"
			href="https://assets.aftership.com/favicons/apple-touch-icon-144x144.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="152x152"
			href="https://assets.aftership.com/favicons/apple-touch-icon-152x152.png"
		/>
		<link
			rel="apple-touch-icon"
			sizes="180x180"
			href="https://assets.aftership.com/favicons/apple-touch-icon-180x180.png"
		/>
		<link
			rel="icon"
			type="image/png"
			href="https://assets.aftership.com/favicons/favicon-32x32.png"
			sizes="32x32"
		/>
		<link
			rel="icon"
			type="image/png"
			href="https://assets.aftership.com/favicons/favicon-194x194.png"
			sizes="194x194"
		/>
		<link
			rel="icon"
			type="image/png"
			href="https://assets.aftership.com/favicons/favicon-96x96.png"
			sizes="96x96"
		/>
		<link
			rel="icon"
			type="image/png"
			href="https://assets.aftership.com/favicons/android-chrome-192x192.png"
			sizes="192x192"
		/>
		<link
			rel="icon"
			type="image/png"
			href="https://assets.aftership.com/favicons/favicon-16x16.png"
			sizes="16x16"
		/>
		<link
			rel="manifest"
			href="https://assets.aftership.com/favicons/manifest.json"
		/>
		<link
			rel="shortcut icon"
			href="https://assets.aftership.com/favicons/favicon.ico"
		/>
		<meta name="msapplication-TileColor" content="#faa41a" />
		<meta
			name="msapplication-TileImage"
			content="https://assets.aftership.com/favicons/mstile-144x144.png"
		/>
		<meta
			name="msapplication-config"
			content="https://assets.aftership.com/favicons/browserconfig.xml"
		/>
		<meta name="theme-color" content="#ffffff" />
		<meta name="application-name" content="AfterShip" />

		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css?family=Lato:300,400,700%7COpen+Sans:300,400,400italic,600,600italic,700,700italic"
		/>
		<style>
			html {
				font-size: 16px;
				-webkit-tap-highlight-color: transparent;
				-webkit-text-size-adjust: 100%;
			}

			body {
				background-color: #eee;
				font-family: Lato, Open Sans, Helvetica, Arial, sans-serif;
				color: #333;
				font-size: 14px;
				margin: 0;
			}
			* {
				box-sizing: border-box;
			}
			.board {
				margin-top: 100px;
				display: inline-block;
				position: relative;
				left: 50%;
				-ms-transform: translateX(-50%);
				transform: translateX(-50%);
			}
			.logo--full {
				width: 222px;
				height: 45px;
				margin-top: 7px;
				margin-bottom: 1.5rem !important;
				margin-left: auto;
				margin-right: auto;
				display: block;
				background-image: url(https://assets.aftership.com/img/logo-aftership.svg);
				background-size: cover;
				background-repeat: no-repeat;
			}

			.card {
				position: relative;
				display: block;
				margin-bottom: 0.75rem;
				background-color: #fff;
				border: 1px solid #e5e5e5;
				border-radius: 0.25rem;
			}
			li,
			ol,
			p {
				margin-top: 0;
				line-height: 26px;
				margin-bottom: 1rem;
			}
			.btn {
				border-width: 0;
				transition: background-color 0.15s ease-out;
				text-transform: uppercase;
				font-size: 14px;
				display: inline-block;
				font-weight: 400;
				text-align: center;
				white-space: nowrap;
				vertical-align: middle;
				cursor: pointer;
				padding: 0.375rem 2rem;
				line-height: 1.5;
				border-radius: 0.25rem;
				color: #fff;
				background-color: #e77f11;
				border-color: #e77f11;
				text-decoration: none;
			}
		</style>
		<script>
			const token = new URLSearchParams(location.search).get('token');
			function handleUnsubscribe() {
				window.location.replace(
					'{{ AFTERSHIP_BFF_URL }}/aftership/admin/email/unsubscribe/' +
						token
				);
			}
		</script>
	</head>
	<body>
		<div id="page-container">
			<div class="board">
				<a class="logo--full m-b-2" href="/" title="Home"
					><div
						style="
							position: absolute;
							width: 1px;
							height: 1px;
							padding: 0;
							margin: -1px;
							overflow: hidden;
							clip: rect(0, 0, 0, 0);
							border: 0;
						"
					>
						<p style="margin-bottom: 0">Home</p>
					</div></a
				>
				<div
					style="
						text-align: center;
						background-color: #fbfbfb;
						border: 1px solid #ddd;
						padding: 1.5rem;
						position: relative;
						display: block;
						margin-bottom: 0.75rem;
					"
				>
					<p
						style="
							border-bottom: 1px solid #ddd;
							padding-bottom: 1rem;
							padding-right: 1rem;
							padding-left: 1rem;
							margin-bottom: 20px;
						"
					>
						You have now subscribed to all notifications
					</p>
					<p style="margin: 0 1rem">
						<a
							class="btn"
							href="javascript:void()"
							onclick="handleUnsubscribe(event)"
							href="/subscribe/eyJvcmdhbml6YXRpb25faWQiOiI0ZTY4NzA1OGFiZDk0MTdiOTBmMzI0MTcwNTg0MTNiYSIsInR5cGUiOiJlbWFpbCIsInRvIjoiaHguenVvQGFmdGVyc2hpcC5jb20ifQ=="
							>Click to unsubscribe from all future
							notifications</a
						>
					</p>
				</div>
			</div>
		</div>
	</body>
</html>
