<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<title>AfterShip Admin - authorize</title>
		<script
			type="text/javascript"
			src="%ACCOUNTS_BUSINESS%/auth/auth.js"
		></script>
	</head>

	<body>
		<script>
			(function (getAuth) {
				const auth = getAuth({
					clientId: 'aftership',
					realm: 'business',
					forceLogin: false,
				});
				try {
					var query = new URLSearchParams(location.search);
					if (query.get('continue') === 'register') {
						auth.register('{{ PUBLIC_URL }}');
					} else {
						auth.login('{{ PUBLIC_URL }}');
					}
				} catch (e) {
					auth.login('{{ PUBLIC_URL }}');
				}
			})(window.getAuth);
		</script>
	</body>
</html>
