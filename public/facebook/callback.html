<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta http-equiv="X-UA-Compatible" content="ie=edge" />
		<title>Connecting Facebook...</title>
	</head>

	<body>
		<script>
			function getQueryVariable(variable) {
				var query = window.location.search.substring(1);
				var vars = query.split('&');
				for (var i = 0; i < vars.length; i++) {
					var pair = vars[i].split('=');
					if (decodeURIComponent(pair[0]) == variable) {
						return decodeURIComponent(pair[1]);
					}
				}
				return '';
			}

			var token = sessionStorage.getItem('csrf_token');
			var code = getQueryVariable('code');
			var state = getQueryVariable('state');

			if (token === state) {
				window.opener.postMessage(
					{
						source: 'secure',
						type: 'FACEBOOK_CONNECT',
						result: {code},
					},
					'*'
				);
			}
			if (!state || !token || token !== state) {
				alert(
					`invalid code: ${code}, state: ${state}, token: ${token}`
				);
			}
			window.close();
		</script>
	</body>
</html>
