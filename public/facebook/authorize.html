<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta http-equiv="X-UA-Compatible" content="ie=edge" />
		<title>AfterShip - admin</title>
	</head>

	<body>
		<script>
			function getSafeProductLink(url) {
				const oldUrl = new URL(url);
				if (
					!/(^|\.)(aftership|automizely|returnscenter|postmen|facebook)\.(io|com)$/.test(
						oldUrl.hostname
					)
				) {
					return 'https://admin.aftership.com/';
				}

				return url;
			}
			var url = document.location.search.split('url=')[1];

			window.location.href = getSafeProductLink(url);
		</script>
	</body>
</html>
