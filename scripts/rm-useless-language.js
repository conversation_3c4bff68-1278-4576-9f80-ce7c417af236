const path = require('path');
const fs = require('fs');
const shell = require('shelljs');
const os = require('os');

function extract(config) {
	const {keyword = 't'} = config;
	const grep = os.platform() === 'darwin' ? 'ggrep' : 'grep';
	const tempFile = path.join(process.cwd(), `./_translate${+new Date()}.txt`);

	const result = shell
		.exec(
			`${grep} -Phrzoe "(?<=[^a-zA-Z]${keyword}\\()[\\n\\s\\t]*([\\"\\\`\\'])([\\d\\D]*?)\\1(?=([\\n\\s\\t]*)[\\),])" ${config.path}`,
			{silent: true}
		)
		.exec(`${grep} -Pzoe "(?<=([\\"\\\`\\']))[\\d\\D]*(?=\\1)" `, {
			silent: true,
		})
		.to(tempFile);
	if (result.code === 1) {
		try {
			fs.rmSync(tempFile);
		} catch (error) {}
		console.log('\x1b[31m', 'No match content');
		return [];
	}
	if (result.code !== 0) throw new Error(result.stderr);

	const content = fs.readFileSync(tempFile, {encoding: 'utf-8'});
	fs.rmSync(tempFile);
	const words = content
		.split('\x00')
		.map(item => item.trim())
		.filter(Boolean);

	return [...new Set(words)];
}

function main(filePath) {
	if (!filePath) {
		console.log('please input file path!!!');
		return;
	}
	const fullFilePath = path.join(process.cwd(), filePath);
	if (!fs.existsSync(fullFilePath)) {
		console.log(`Can't find the file(${fullFilePath})!!!`);
		return;
	}
	const allWords = extract({path: 'src'});

	const files = fs.readdirSync(fullFilePath);
	files.forEach(file => {
		if (!file.endsWith('.json')) {
			return;
		}
		const filePath = path.join(fullFilePath, file);
		const languages = require(filePath);
		const usedLanguages = {};
		allWords.forEach(word => {
			if (languages[word]) {
				usedLanguages[word] = languages[word];
			}
		});
		fs.writeFileSync(filePath, JSON.stringify(usedLanguages, null, 2));
		console.log(`${filePath} remove useless successfully`);
	});
}

const filePath = process.argv[2];
main(filePath);
