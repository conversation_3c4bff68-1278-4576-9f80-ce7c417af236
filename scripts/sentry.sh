
export SENTRY_URL=https://sentry.automizely.org
# export SENTRY_AUTH_TOKEN=inject from upload-assets.js
export SENTRY_LOG_LEVEL=info
export SENTRY_ORG=sentry
export SENTRY_PROJECT=admin-aftership-com
export SENTRY_ENVIRONMENT=$APP_ENV
export SENTRY_RELEASE=$(git rev-parse HEAD)

npx sentry-cli releases new -p $SENTRY_PROJECT $SENTRY_RELEASE
npx sentry-cli releases set-commits $SENTRY_RELEASE --auto
npx sentry-cli releases files $SENTRY_RELEASE upload-sourcemaps -d $SENTRY_ENVIRONMENT --url-prefix "~/static/js" --validate --wait ./build/static/js
npx sentry-cli releases finalize $SENTRY_RELEASE
npx sentry-cli releases deploys $SENTRY_RELEASE new -e $SENTRY_ENVIRONMENT
