const chalk = require('chalk');
const {cpus, totalmem} = require('os');
// const {exec} = require('child_process');

// 方便 CI/CD 配置参考
const showEnvInfo = function () {
	const {NODE_ENV, APP_ENV, APP_NAME, NODE_OPTIONS} = process.env;

	console.log(
		chalk.gray.bold(`
NODE_ENV     = ${NODE_ENV}
NODE_OPTIONS = ${NODE_OPTIONS}
APP_ENV      = ${APP_ENV}
APP_NAME     = ${APP_NAME}

CPU    - ${cpus().length} cores
Memory - ${totalmem() / 1024 / 1024 / 1024}GB
`)
	);

	// 	exec('df -h', (error, stdout, stderr) => {
	// 		if (error) {
	// 			console.error(`error: ${error}`);
	// 			return;
	// 		}
	// 		if (stderr) {
	// 			console.error(`stderr: ${stderr}`);
	// 			return;
	// 		}

	// 		console.log(chalk.gray.bold(`
	// ${stdout}`));
	// 	});
};

module.exports = {showEnvInfo};
