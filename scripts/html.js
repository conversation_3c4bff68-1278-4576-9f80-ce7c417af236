const {template} = require('lodash');
const {resolve} = require('path');
const {readFileSync, writeFileSync, existsSync, mkdirSync} = require('fs');
const chalk = require('chalk');

const getEnvironment = require('../config/env');

const BUILD_OUT_DIR = 'build';

const publicPaths = {
	testing: 'https://admin.aftership.io/',
	'release-plum': 'https://release-plum-admin.aftership.io/',
	'testing-banana': 'https://release-banana-admin.aftership.io/',
	'release-banana': 'https://release-banana-admin.aftership.io/',
	'release-kiwi': 'https://release-kiwi-admin.aftership.io/',
	'release-pear': 'https://release-pear-admin.aftership.io/',
	'release-northstar': 'https://release-northstar-admin.aftership.io/',
	'release-walnut': 'https://release-walnut-admin.aftership.io/',
	'release-bean': 'https://release-bean-admin.aftership.io/',
	'release-okra': 'https://release-okra-admin.aftership.io/',
	'release-incy': 'https://release-incy-admin.aftership.io/',
	'testing-incy': 'https://testing-incy-admin.aftership.io/',
	'release-core': 'https://release-core-admin.aftership.io/',
	'release-tidy': 'https://release-tidy-admin.aftership.io/',
	staging: 'https://staging-admin.aftership.com/',
	production: 'https://admin.aftership.com/',
};

const legacyCompanyPublicPath = '/tracking/';
const modernCompanyPublicPath = '/company/tracking/';
const legacyPublicPath = '/';
const modernPublicPath = '/tracking/';
const publicPathMap = {
	'company.aftership.com_tracking': legacyCompanyPublicPath,
	'admin.aftership.com_company_tracking': modernCompanyPublicPath,
	'admin.aftership.com': legacyPublicPath,
	'admin.aftership.com_tracking': modernPublicPath,
	// for legacy analytics email renderer
	'analytics-renderer-as.automizely.com': legacyPublicPath,
};

let publicPath = '/';

if (
	[
		'company.aftership.com_tracking',
		'admin.aftership.com_company_tracking',
		'analytics-renderer-as.automizely.com',
	].includes(process.env.APP_NAME)
) {
	publicPath = publicPathMap[process.env.APP_NAME] || '/';
} else {
	publicPath = `${
		publicPaths[process.env.APP_ENV]?.replace(/\/$/, '') || ''
	}${publicPathMap[process.env.APP_NAME] || '/'}`;
}

console.log('HTML JS - publicPath =>', publicPath);

const envs = getEnvironment(publicPath.slice(0, -1));

for (const html of [
	`${BUILD_OUT_DIR}/sso/authorize/index.html`,
	`${BUILD_OUT_DIR}/email/subscribe.html`,
	`${BUILD_OUT_DIR}/email/unsubscribe.html`,
	`${BUILD_OUT_DIR}/email/error.html`,
]) {
	const path = resolve(__dirname, '..', html);
	const content = readFileSync(path, 'utf-8');
	const result = template(content, {
		interpolate: /{{([\s\S]+?)}}/g,
	})(envs.raw);
	writeFileSync(path, result);
}

const posthogHost = {
	development: 'https://app.posthog.com',
	testing: 'https://app.posthog.com',
	staging: 'https://app.posthog.com',
	production: 'https://app.posthog.com',
};

const posthogId = {
	development: 'phc_ahNJ2cj22XVULhAG1ULaJBZHeQj4nsCh5uERPsckhko',
	testing: 'phc_ahNJ2cj22XVULhAG1ULaJBZHeQj4nsCh5uERPsckhko',
	staging: 'phc_ahNJ2cj22XVULhAG1ULaJBZHeQj4nsCh5uERPsckhko',
	production: 'phc_ahNJ2cj22XVULhAG1ULaJBZHeQj4nsCh5uERPsckhko',
};

function configPosthog() {
	const path = resolve(__dirname, `../${BUILD_OUT_DIR}/posthog/index.html`);
	const content = readFileSync(path, 'utf-8');

	writeFileSync(
		path,
		content
			.replace('{posthogId}', posthogId[process.env.APP_ENV])
			.replace('{posthogHost}', posthogHost[process.env.APP_ENV])
	);
}
configPosthog();

console.log(chalk.bgGreen.bold(`\nFinished HTML processing\n`));
