/* eslint no-console: "off" */
const path = require('path');
const aws = require('aws-sdk');
const {execSync} = require('child_process');
const chalk = require('chalk');
const {uploadAssets} = require('@aftership/deploy-frontend-assets');

const s3 = new aws.S3();
// const cloudfront = new aws.CloudFront();

const buckets = {
	testing: 'admin.aftership.io',
	// `testing-banana` & `release-banana` are the same env for us
	// we use sub domain `release-banana` for AppEnv `testing-banana`
	'release-banana': 'release-banana-admin.aftership.io',
	'release-northstar': 'release-northstar-admin.aftership.io',
	'testing-banana': 'release-banana-admin.aftership.io',
	'release-walnut': 'release-walnut-admin.aftership.io',
	'release-okra': 'release-okra-admin.aftership.io',
	'testing-walnut': 'release-walnut-admin.aftership.io',
	'testing-incy': 'testing-incy-admin.aftership.io',
	'release-incy': 'release-incy-admin.aftership.io',
	'release-tidy': 'release-tidy-admin.aftership.io',
	'release-pear': 'release-pear-admin.aftership.io',
	'release-bean': 'release-bean-admin.aftership.io',
	'release-kiwi': 'release-kiwi-admin.aftership.io',
	'release-plum': 'release-plum-admin.aftership.io',
	'release-sloe': 'release-sloe-admin.aftership.io',
	'release-nike': 'release-nike-admin.aftership.io',
	'release-gray': 'release-gray-admin.aftership.io',
	'release-core': 'release-core-admin.aftership.io',
	staging: 'staging-admin.aftership.com',
	production: 'admin.aftership.com',
};

const companyBuckets = {
	testing: 'company.aftership.io',
	staging: 'staging-company.aftership.com',
	production: 'company.aftership.com',

	// `testing-banana` & `release-banana` are the same env for us
	// we use sub domain `release-banana` for AppEnv `testing-banana`
	// Admin <-> Company
	'release-banana': 'release-incy-company.aftership.io',
	'testing-banana': 'release-incy-company.aftership.io',
	'release-northstar': 'release-incy-company.aftership.io',
	'release-okra': 'release-incy-company.aftership.io',
	'release-walnut': 'release-incy-company.aftership.io',
	'testing-incy': 'release-incy-company.aftership.io',
	'release-incy': 'release-incy-company.aftership.io',
	'release-tidy': 'release-incy-company.aftership.io',
	'release-pear': 'release-incy-company.aftership.io',
	'release-bean': 'release-incy-company.aftership.io',
	'release-kiwi': 'release-incy-company.aftership.io',
	'release-plum': 'release-incy-company.aftership.io',
	'release-sloe': 'release-incy-company.aftership.io',
	'release-nike': 'release-incy-company.aftership.io',
	'release-gray': 'release-incy-company.aftership.io',
	'release-core': 'release-incy-company.aftership.io',
};

/*
  @description get Sentry ApiKey form S3 & upload source maps
  @see https://aftership.atlassian.net/browse/TSRE-14635 for s3 AuthKey structure
  {
    "SENTRY_AUTH_TOKEN": "XXX",
    "REMARK": "API Auth Tokens of bot.devops"
  }
*/
async function uploadSourceMaps() {
	if (!['testing', 'staging', 'production'].includes(process.env.APP_ENV)) {
		console.warn(
			chalk.bgYellow(
				'source map will only be uploaded in testing, staging, production environment'
			)
		);
		return;
	}
	let sentryAuthToken = await s3
		.getObject({
			Bucket: 'aftership.keys',
			Key: 'sentry/sentry_token.json',
		})
		.promise()
		.then(data => {
			return JSON.parse(data.Body.toString()).SENTRY_AUTH_TOKEN;
		});
	execSync('sh ./scripts/sentry.sh', {
		stdio: 'inherit',
		env: {
			...process.env,
			SENTRY_AUTH_TOKEN: sentryAuthToken,
		},
	});
}

// 弃用
async function uploadCompany() {
	const bucketDir = 'tracking';
	const bucketSubdirectory = `/${bucketDir}`;

	await uploadAssets({
		storageType: 's3',
		options: {
			buckets: companyBuckets,
			bucketSubdirectory,
			cacheSetting: [
				{
					key: `${bucketDir}/**/*.+(html|json|xml)`,
					age: 120,
				},
				{
					key: `${bucketDir}/**/__federation_shared*.js`,
					age: 60,
				},
			],
			purgeCache: {
				enabled: true,
				bucketSubdirectory,
			},
			assetsPath: path.resolve(__dirname, '../build_company'),
		},
	});

	console.log(
		chalk.bgGreen.bold('\n🍻🍻🍻🍻 Company Finish Upload 🍻🍻🍻🍻\n')
	);
}

async function uploadAdmin() {
	await uploadAssets({
		storageType: 's3',
		options: {
			buckets: buckets,
			cacheSetting: [
				{
					key: '**/*.+(html|json|xml)',
					age: 'no-cache',
				},
				{
					key: '**/__federation_shared*.js',
					age: 60,
				},
			],
			purgeCache: {
				enabled: true,
			},
			assetsPath: path.resolve(__dirname, '../build'),
		},
	});

	console.log(
		chalk.bgGreen.bold('\n🍻🍻🍻🍻 Admin Finish Upload 🍻🍻🍻🍻\n')
	);
}

async function main() {
	await uploadAdmin();
	// await uploadCompany(); // 弃用

	console.log(
		chalk.underline('\n🚧🚧🚧🚧 Uploading source maps to Sentry 🚧🚧🚧🚧\n')
	);

	try {
		await uploadSourceMaps();
	} catch (error) {
		console.log(chalk.red.bold('\n🔴🔴🔴 Sentry upload error 🔴🔴🔴\n'));
		console.error(error);
	}

	console.log(chalk.bgGreen.bold('\n🍻🍻🍻🍻 Finish Upload 🍻🍻🍻🍻\n'));
}

main().catch(e => {
	console.log(
		chalk.bgRed.bold(
			'\n🔴🔴🔴 Error: Something went wrong while uploading assets 🔴🔴🔴\n',
			e
		)
	);

	process.exit(1);
});
