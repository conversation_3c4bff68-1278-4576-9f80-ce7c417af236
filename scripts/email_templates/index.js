const fs = require('fs');
const path = require('path');

const {reactRender} = require('@aftership/react-email-module');

const templatesFiles = [
	'tracking_default_design_templates.json',
	'order_default_design_templates.json',
	'tracking_stuck_design_templates.json',
	'order_stuck_design_templates.json',
	'edd_status_update/design.json',
];
const generateTemplates = fileName => {
	const file = path.resolve(`./${fileName}`);
	const rawDefaultTemplate = fs.readFileSync(file);
	const defaultTemplates = JSON.parse(rawDefaultTemplate);

	const newTemplates = defaultTemplates.map(tpl => {
		const html = reactRender({
			productCode: 'aftership',
			content: tpl.template_settings.content,
		});

		return {
			...tpl,
			content: html,
		};
	});
	fs.writeFileSync(file, JSON.stringify(newTemplates, null, 2));
};

for (let i = 0; i < templatesFiles.length; i++) {
	generateTemplates(templatesFiles[i]);
}
