/** ⚠️ 必要的参数 */
const YOUR_TOKEN = '';
const VARIANT_ID = '';

/** 用来批量上传 i18n 到 meerkat */

const AuthToken = `Bearer ${YOUR_TOKEN}`;
const fs = require('fs');
const path = require('path');
const inquirer = require('inquirer');

// 模块关键词映射，用于推荐模块选择
const MODULE_KEYWORDS = {
	settings: ['settings', 'format', 'locale', 'timezone'],
	shipments: ['shipment', 'order', 'delivery', 'tracking', 'courier'],
	dashboard: ['dashboard'],
	btp: ['tracking', 'page', 'editor', 'checkpoint', 'progress', 'btp'],
	notifications: ['notification'],
};

// 分析翻译键值，推荐可能的模块
const analyzeTranslations = translations => {
	// 计算每个模块的匹配分数
	const moduleScores = {};

	// 初始化分数
	Object.keys(MODULE_KEYWORDS).forEach(module => {
		moduleScores[module] = 0;
	});

	// 分析所有翻译的键和值
	Object.entries(translations).forEach(([key, value]) => {
		const textToAnalyze = `${key} ${value}`.toLowerCase();

		Object.entries(MODULE_KEYWORDS).forEach(([module, keywords]) => {
			keywords.forEach(keyword => {
				if (textToAnalyze.includes(keyword.toLowerCase())) {
					moduleScores[module] += 1;
				}
			});
		});
	});

	// 按分数排序模块
	return Object.entries(moduleScores)
		.sort((a, b) => b[1] - a[1])
		.map(([module, score]) => ({
			name: `${module} (匹配度: ${score})`,
			value: module,
		}));
};

// 创建日期文件夹
const createDateFolder = () => {
	const now = new Date();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, '0');
	const day = String(now.getDate()).padStart(2, '0');
	const dateFolder = `${year}${month}${day}`;

	const folderPath = path.join(__dirname, '..', 'i18n', dateFolder);

	if (!fs.existsSync(folderPath)) {
		fs.mkdirSync(folderPath, {recursive: true});
	}

	return {dateFolder, folderPath};
};

// 使用 inquirer 让用户选择模块
const selectModule = async translations => {
	const sortedModules = analyzeTranslations(translations);

	// 添加自定义选项
	const choices = [
		...sortedModules,
		new inquirer.Separator(),
		{
			name: '自定义模块名称',
			value: 'custom',
		},
	];

	const {module} = await inquirer.prompt([
		{
			type: 'list',
			name: 'module',
			message: '请选择翻译所属的模块:',
			choices: choices,
		},
	]);

	// 如果用户选择自定义，则提示输入自定义模块名称
	if (module === 'custom') {
		const {customModule} = await inquirer.prompt([
			{
				type: 'input',
				name: 'customModule',
				message: '请输入自定义模块名称:',
				validate: input =>
					input.trim() !== '' ? true : '模块名称不能为空',
			},
		]);
		return customModule;
	}

	return module;
};

const main = async () => {
	// read i18nJson
	const i18nJsonToUpload = require('../admin.aftership.com-translation.json');
	const sourceFilePath = path.join(
		__dirname,
		'..',
		'admin.aftership.com-translation.json'
	);

	// 读取 i18nJson，顺序调用 fetch
	const i18Entries = Object.entries(i18nJsonToUpload);

	const successList = [];
	const failedList = [];
	const errorList = [];

	for (const [key, value] of i18Entries) {
		console.log('%c 🚀 uploading....,', key, value);

		// ⚠️ 参数需要按需修改，如 token、project_code、variant_id 等
		const body = {
			variant_id: `${VARIANT_ID}`,
			project_code: 'aftership',
			metadata: '{}',
			resource_code: key,
			allow_business_override: false,
			translations: [{language_code: 'en', content: value}],
			auto_translate_language_codes: ['de', 'it', 'fr', 'es', 'zh-Hans'],
		};

		try {
			const result = await fetch(
				'https://meerkat.aftership.org/meerkat/v2/employee/resources',
				{
					headers: {
						accept: '*/*',
						'accept-language': 'en-US,en;q=0.9',
						authorization: AuthToken,
						'content-type': 'text/plain;charset=UTF-8',
						'sec-ch-ua':
							'"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
						'sec-ch-ua-mobile': '?0',
						'sec-ch-ua-platform': '"macOS"',
						'sec-fetch-dest': 'empty',
						'sec-fetch-mode': 'cors',
						'sec-fetch-site': 'cross-site',
						Referer: 'https://meerkat.automizely.org/',
						'Referrer-Policy': 'strict-origin-when-cross-origin',
					},
					body: JSON.stringify(body),
					method: 'POST',
				}
			).then(res => res.json());
			console.log('result', result);
			if (result.meta.code === 20100) {
				successList.push(`${key}: ${value}`);
			} else {
				failedList.push(`${key}: ${value}`);
			}
		} catch (error) {
			console.error('error', error);
			errorList.push(JSON.stringify(error));
		}
	}

	// 将 successList 结果输出到文件
	const successListFile = path.join(__dirname, 'successList.log');
	const failedListFile = path.join(__dirname, 'failedList.log');
	const errorListFile = path.join(__dirname, 'errorList.log');

	// console.log('failedList', failedList);
	// success count
	console.log('✅ success count', successList.length);
	// failed count
	console.log('❌ failedList count', failedList.length);
	// error count
	console.log('⛔️ error count', errorList.length);

	fs.writeFileSync(successListFile, successList.join('\n'));
	fs.writeFileSync(failedListFile, failedList.join('\n'));
	fs.writeFileSync(errorListFile, errorList.join('\n'));

	// 使用 inquirer 让用户选择模块
	console.log('🔍 分析翻译内容...');
	const selectedModule = await selectModule(i18nJsonToUpload);
	console.log(`📋 已选择模块: ${selectedModule}`);

	// 创建日期文件夹
	const {dateFolder, folderPath} = createDateFolder();
	console.log(`📁 创建文件夹: ${folderPath}`);

	// 保存翻译数据到模块文件
	const moduleFilePath = path.join(folderPath, `${selectedModule}.json`);
	fs.writeFileSync(moduleFilePath, JSON.stringify(i18nJsonToUpload, null, 2));
	console.log(`💾 已保存翻译到: ${moduleFilePath}`);

	// 删除源文件
	try {
		fs.unlinkSync(sourceFilePath);
		console.log(`🗑️ 源文件已删除: ${sourceFilePath}`);
	} catch (err) {
		console.error(`⚠️ 无法删除源文件: ${err.message}`);
	}
};

main();
