#!/bin/bash

# Set color variables
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# project path
PROJECT_PATH="${1:-.}"
FULL_PROJECT_PATH=$(cd "$PROJECT_PATH" && pwd)

echo -e "\n${BLUE}===== Project Basic Information Check =====${NC}"
echo -e "Check path: $FULL_PROJECT_PATH\n"

# Check if project exists
if [ ! -d "$FULL_PROJECT_PATH" ]; then
  echo -e "${RED}Error: Project path \"$FULL_PROJECT_PATH\" does not exist${NC}"
  exit 1
fi

# Check package.json
check_package_json() {
  echo -e "${BLUE}📦 Checking package.json...${NC}"
  
  PACKAGE_JSON_PATH="$FULL_PROJECT_PATH/package.json"
  
  if [ ! -f "$PACKAGE_JSON_PATH" ]; then
    echo -e "  ${RED}❌ package.json does not exist${NC}"
    return
  fi

  # Display package.json content
  echo -e "  ${YELLOW}----------------------------------------${NC}"
  cat "$PACKAGE_JSON_PATH" | sed 's/^/  /'
  echo -e "  ${YELLOW}----------------------------------------${NC}"
}

# Check Node.js and Yarn info
check_node_and_yarn() {
  echo -e "\n${BLUE}🔧 Checking Node.js and Yarn...${NC}"
  
  # Check Node.js version
  if command -v node > /dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    echo -e "  📝 Node.js version: $NODE_VERSION"
  else
    echo -e "  ${YELLOW}⚠️ Node.js is not installed or not in PATH${NC}"
  fi
  
  # Check npm version
  if command -v npm > /dev/null 2>&1; then
    NPM_VERSION=$(npm --version)
    echo -e "  📝 npm version: $NPM_VERSION"
  else
    echo -e "  ${YELLOW}⚠️ npm is not installed or not in PATH${NC}"
  fi
  
  # Check Yarn version
  if command -v yarn > /dev/null 2>&1; then
    YARN_VERSION=$(yarn --version)
    echo -e "  📝 Yarn version: $YARN_VERSION"
  else
    echo -e "  ${YELLOW}ℹ️ Yarn is not installed or not in PATH${NC}"
  fi
}

# Check Git info
check_git_info() {
  echo -e "\n${BLUE}🔄 Checking Git info...${NC}"
  
  # Switch to project directory
  cd "$FULL_PROJECT_PATH" || return
  
  if [ ! -d ".git" ]; then
    echo -e "  ${RED}❌ .git directory not found, this is not a Git repository${NC}"
    return
  fi
  
  # Get current branch
  if BRANCH=$(git branch --show-current 2>/dev/null); then
    echo -e "  📝 Current branch: $BRANCH"
  else
    echo -e "  ${RED}❌ Unable to get current branch${NC}"
  fi
  
  # Get last commit
  if LAST_COMMIT=$(git log -1 --pretty=format:"%h - %s (%an, %ar)" 2>/dev/null); then
    echo -e "  📝 Last commit: $LAST_COMMIT"
  else
    echo -e "  ${RED}❌ Unable to get last commit${NC}"
  fi
  
  # Get tag info
  TAGS=$(git tag -l --sort=-v:refname 2>/dev/null | head -n 5)
  if [ ! -z "$TAGS" ]; then
    echo -e "  📝 Recent tags:"
    echo "$TAGS" | while read -r tag; do
      if [ ! -z "$tag" ]; then
        echo -e "    - $tag"
      fi
    done
  fi
}

# Check project basics
check_project_basics() {
  echo -e "\n${BLUE}📁 Project basics...${NC}"
  
  # Switch to project directory
  cd "$FULL_PROJECT_PATH" || return
  
  # Check file count (ignore .gitignore patterns if possible)
  if command -v git > /dev/null 2>&1; then
    FILE_COUNT=$(git ls-files --others --exclude-standard --cached | wc -l)
    echo -e "  📝 Total file count (git tracked & not ignored): $FILE_COUNT"
  else
    FILE_COUNT=$(find . -type d -name node_modules -prune -o -type f -print | wc -l)
    echo -e "  📝 Total file count (excluding node_modules): $FILE_COUNT"
  fi
}

# Execute checks
check_package_json
check_node_and_yarn
check_git_info
check_project_basics

echo -e "\n${GREEN}✅ Finish lint & check!${NC}"