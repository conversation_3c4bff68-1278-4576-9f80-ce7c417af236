// i18n-resolve-plugin.ts

const runtimePlugin = function () {
	return {
		name: 'i18n-resolve-plugin',
		resolveShare(args) {
			const {pkgName} = args;

			if (pkgName !== 'i18next') {
				return args;
			}
			const resolvedDependency = args.resolver();

			if (!resolvedDependency) {
				return args;
			}
			/**
			 * Override the resolver of i18next
			 * @link https://github.com/module-federation/core/issues/2995
			 */
			args.resolver = () => ({
				...resolvedDependency,
				loaded: false,
				lib: undefined,
				loading: new Promise(async resolve => {
					const originalFactory = await resolvedDependency.get();
					// Mark the original factory as loaded
					resolvedDependency.lib = () => originalFactory().default;
					resolvedDependency.loaded = true;
					resolve(() => {
						return originalFactory().default;
					});
				}),
			});

			return args;
		},
	};
};
export default runtimePlugin;
