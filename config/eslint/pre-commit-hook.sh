# !/bin/bash

# list all ts/tsx files that changed between index & HEAD
# example output:
# A  folder/filename.ts
# R  folder/old_filename.tsx  folder/new_filename.tsx
# D  folder/useless_file.ts
status_list=$(git diff --cached --name-status | awk '$NF ~ /\.tsx?$/{print $0}')
commit_sha1=$(git rev-parse --verify -q MERGE_HEAD || git rev-parse --verify -q HEAD)

while read line; do
	check_path=$(awk '{print $2}' <<< $line)
	file_path=$(awk '{print $NF}' <<< $line)
	# if file status is not deleted,
	# search history to find out if it has the added/created commit
	if [[ "$line" =~ ^[^D] ]]; then
		count=$(git log $commit_sha1 --follow --diff-filter=AC --format="" \
			--name-status --since='2021-08-02T00:00:00+08:00' \
			--date-order -- $check_path | wc -l)
		if [[ $count -gt 0 ]]; then
			result+="$file_path "
		fi
	fi
done <<< "$status_list"

eslint -c config/eslint/.eslintrc.typescript.js --ignore-path config/eslint/.eslintignore --fix $result
