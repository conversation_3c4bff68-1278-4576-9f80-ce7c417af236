# !/bin/bash

# list all ts/tsx files that changed(except delete) between HEAD & the closet tag
# example output:
# A  folder/filename.ts
# R  folder/old_filename.tsx  folder/new_filename.tsx
prev_tag=$(git tag --sort=committerdate | tail -n 1)
status_list=$(git diff --cached --name-status --diff-filter=d $prev_tag \
	| awk '$NF ~ /\.tsx?$/ {print $NF}')

if [[ -z "$status_list" ]]; then
	exit 0
fi

# search history to find out if it has the added/created commit
while read line; do
	count=$(git log --follow --diff-filter=AC --format="" \
		--name-status --since='2021-08-02T00:00:00+08:00' \
		--date-order -- $line | wc -l)
	if [[ $count -gt 0 ]]; then
		result+="$line "
	fi
done <<< "$status_list"

if [[ -z "$result" ]]; then
	exit 0
fi

eslint -c config/eslint/.eslintrc.typescript.js --ignore-path config/eslint/.eslintignore $result
