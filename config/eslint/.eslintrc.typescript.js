module.exports = {
	parser: '@typescript-eslint/parser',
	parserOptions: {
		ecmaFeatures: {
			jsx: true,
		},
	},
	plugins: ['@typescript-eslint', 'jsx-a11y', 'unused-imports'],
	extends: [
		'eslint:recommended',
		'plugin:@typescript-eslint/recommended',
		'plugin:import/recommended',
		'plugin:import/typescript',
		'plugin:react/recommended',
		'plugin:react-hooks/recommended',
		'prettier',
	],
	rules: {
		'@typescript-eslint/ban-ts-comment': 'off',
		'@typescript-eslint/no-empty-function': 'off',
		'no-use-before-define': 'off',
		'@typescript-eslint/no-use-before-define': ['error'],
		'no-unused-vars': 'off',
		'@typescript-eslint/explicit-module-boundary-types': 'off',
		'react/prop-types': 'off',
		'react/require-default-props': 'off',
		'react/display-name': 'off',
		'react/no-unescaped-entities': 'off',
		'unused-imports/no-unused-imports': 'error',
		'@typescript-eslint/no-unused-vars': [
			'error',
			{argsIgnorePattern: '^_', destructuredArrayIgnorePattern: '^_'},
		],
		'import/extensions': [
			'warn',
			'ignorePackages',
			{js: 'never', jsx: 'never', ts: 'never', tsx: 'never'},
		],
		'react/react-in-jsx-scope': 'off',
		'react/jsx-filename-extension': ['error', {extensions: ['.tsx']}],
		'import/order': [
			'warn',
			{
				groups: [
					'builtin',
					'external',
					'internal',
					'parent',
					'sibling',
					'index',
					'object',
				],
				alphabetize: {order: 'asc'},
				'newlines-between': 'always',
			},
		],
		'import/no-unresolved': [
			'error',
			{ignore: ['.module.s?css$', 'aftershipNotification']},
		],
		'sort-keys': 'off',
		'jsx-a11y/anchor-is-valid': 'off',
		camelcase: 'off',
		'object-curly-spacing': ['error'],
	},
	settings: {
		'import/resolver': {
			typescript: {},
			node: {
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
		},
		react: {
			version: 'detect',
		},
	},
};
