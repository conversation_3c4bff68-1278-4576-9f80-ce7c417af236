module.exports = {
	extends: ['aftership/react', 'prettier'],
	plugins: ['prettier'],
	env: {
		jest: true,
	},
	settings: {
		'import/resolver': {
			node: {},
			typescript: {},
			jest: {
				jestConfigFile: 'jest.config.js',
			},
		},
	},
	rules: {
		'react/prop-types': [1],
		'react/forbid-prop-types': 0,
		'no-empty': 0,
		'no-shadow': 0,
		'max-lines': ['warn', 500],
		'@typescript-eslint/ban-ts-ignore': 'off',
	},
	overrides: [
		{
			files: ['*.spec.jsx', '*.spec.js'],
			rules: {
				'max-lines': ['warn', 2000],
				'import/first': ['off'],
			},
		},
		{
			files: ['*.jsx', '*.js'],
			rules: {
				'jsx-a11y/anchor-is-valid': ['off'],
				'jsx-a11y/no-static-element-interactions': ['off'],
				'import/extensions': ['off'],
				'no-console': ['off'],
				camelcase: 'off',
			},
		},
	],
};
