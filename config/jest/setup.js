// https://github.com/facebook/jest/issues/4545

const mockPerformanceMark = jest.fn();
window.performance.mark = mockPerformanceMark;

global.requestAnimationFrame = function (callback) {
	setTimeout(callback, 0);
};

global.localStorage = {
	getItem: key => key,
	setItem: () => {},
};

global.crypto = {
	getRandomValues: arr => require('crypto').randomBytes(arr.length),
};

// global.AFTERSHIP_TRANS = key => key;
global.open = () => {};
global.scrollTo = () => {};
global.gtag = () => {};

global.console.error = jest.fn();

jest.mock('@aftership/automizely-product-auth', () => ({
	...jest.requireActual('@aftership/automizely-product-auth'),
	getOrganizationSync: jest.fn().mockReturnValue({id: 'test-id'}),
	getOrganization: jest.fn().mockReturnValue({id: 'test-id'}),
	useAuth: jest
		.fn()
		.mockReturnValue([{organization: {id: 'test-id', name: 'test'}}]),
}));

jest.mock('react-query', () => ({
	...jest.requireActual('react-query'),
	useQuery: jest.fn().mockReturnValue({}),
}));

jest.mock('react-i18next', () => ({
	...jest.requireActual('react-i18next'),
	useTranslation: jest.fn().mockReturnValue({t: jest.fn(key => key)}),
}));

// eslint-disable-next-line no-underscore-dangle
global.__USER_TIMEZONE__ = 'Asia/Hong_Kong';

global.scroll = () => {};

jest.mock('@aftership/automizely-billing-ui-react', () => ({
	...jest.requireActual('@aftership/automizely-billing-ui-react'),
	usePayment: jest.fn().mockReturnValue({}),
	useBillingChannels: jest.fn().mockReturnValue({channels: []}),
	useFreeTrial: jest.fn().mockReturnValue({
		subscribe() {},
		loading: false,
	}),
	useBillingPlans: jest.fn().mockReturnValue({}),
	useSubscribedFeatures: jest.fn().mockReturnValue({allFeatures: []}),
	usePlanTrial: jest.fn().mockReturnValue({
		activePlanTrial: null,
		planTrials: [],
		trialingPlan: null,
	}),
	useIsPayWithShopify: jest.fn().mockReturnValue({
		isPayWithShopify: true,
	}),
}));

jest.mock('hooks/billings/useFeatureCode', () => ({
	__esModule: true,
	useSubscribeIfNot: jest.fn().mockReturnValue(() => {}),
	useFeatureAvailableWrapper: jest.fn().mockReturnValue(() => ({
		available: true,
		action: () => {},
		isTrial: false,
		targetPlan: undefined,
	})),
	useFeatureAvailable: jest.fn().mockReturnValue(() => false),
}));

jest.mock('@aftership/emailcat', () => ({
	__esModule: true,
	default: jest.fn().mockReturnValue(() => true),
}));

global.matchMedia = jest.fn().mockImplementation(query => {
	return {
		matches: false,
		media: query,
		onchange: null,
		addListener: jest.fn(),
		removeListener: jest.fn(),
	};
});

const {configure} = require('enzyme');
const Adapter = require('@wojtekmaj/enzyme-adapter-react-17');

configure({adapter: new Adapter()});
