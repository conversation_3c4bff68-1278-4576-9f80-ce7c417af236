/* eslint-disable */
const fs = require('fs');
const path = require('path');
const paths = require('./paths');
const {execSync} = require('child_process');

const newRelicCodeTesting = fs.readFileSync(
	path.join(paths.appPublic, 'js/newRelic.testing.js')
);
const newRelicCodeProduction = fs.readFileSync(
	path.join(paths.appPublic, 'js/newRelic.production.js')
);

// Tools like Cloud9 rely on this.
const DEFAULT_PORT = Number(process.env.PORT) || 3303;
const HOST = process.env.HOST || '0.0.0.0';

const {
	NODE_ENV = 'development',
	APP_ENV = 'development',
	CI,
	CYPRESS,
} = process.env;

const API_ENV =
	process.env.API_ENV || (APP_ENV !== 'production' ? 'testing' : '');

const isDev = NODE_ENV === 'development';

// We support resolving modules according to `NODE_PATH`.
// This lets you use absolute paths in imports inside large monorepos:
// https://github.com/facebookincubator/create-react-app/issues/253.
// It works similar to `NODE_PATH` in Node itself:
// https://nodejs.org/api/modules.html#modules_loading_from_the_global_folders
// Note that unlike in Node, only *relative* paths from `NODE_PATH` are honored.
// Otherwise, we risk importing Node.js core modules into an app instead of Webpack shims.
// https://github.com/facebookincubator/create-react-app/issues/1023#issuecomment-265344421
// We also resolve them to make sure all tools using them work consistently.
const appDirectory = fs.realpathSync(process.cwd());
process.env.NODE_PATH = (process.env.NODE_PATH || '')
	.split(path.delimiter)
	.filter(folder => folder && !path.isAbsolute(folder))
	.map(folder => path.resolve(appDirectory, folder))
	.join(path.delimiter);

// Grab NODE_ENV and REACT_APP_* environment variables and prepare them to be
// injected into the application via DefinePlugin in Webpack configuration.
const REACT_APP = /^REACT_APP_/i;
function getBillingApiV2() {
	const isProduction = APP_ENV === 'production' || APP_ENV === 'staging';
	const isRelease = APP_ENV.includes('release');
	const isStaging = APP_ENV === 'staging';
	if (isProduction) {
		return `https://api.automizely.com/billing/v2`;
	}
	if (isStaging) {
		return `https://staging-api.automizely.com/billing/v2`;
	}
	if (isRelease) {
		return 'https://release-incy-api.automizely.io/billing/v2';
	}
	return 'https://release-incy-api.automizely.io/billing/v2';
}

const getPerkFederationDomain = () => {
	const PERK_FEDERATION_DOMAIN = {
		testing: 'https://sdks.am-static.io/admin-perks',
		staging: 'https://staging-sdks.am-static.com/admin-perks',
		production: 'https://sdks.am-static.com/admin-perks',
	};
	return PERK_FEDERATION_DOMAIN[APP_ENV] || PERK_FEDERATION_DOMAIN.testing;
};

const getAppsFederationDomain = () => {
	const [env, app_env] = APP_ENV.split('-');
	let domain;
	switch (env) {
		case 'development':
		// domain = 'http://localhost:3000';
		// break;
		case 'testing':
			domain = 'https://apps.automizely.io';
			break;
		case 'staging':
			domain = 'https://staging-apps.automizely.com';
			break;
		case 'production':
			domain = 'https://apps.automizely.com';
			break;
		case 'release':
			domain = 'https://release-incy-apps.automizely.io';
			break;
		default:
			domain = 'https://apps.automizely.com';
	}
	return domain;
};

const getAppsFederationBffDomain = () => {
	const APPS_FEDERATION_BFF_DOMAIN = {
		testing: 'https://bff-api.automizely.io',
		staging: 'https://staging-bff-api.automizely.com',
		production: 'https://bff-api.automizely.com',
	};
	return (
		APPS_FEDERATION_BFF_DOMAIN[APP_ENV] ||
		APPS_FEDERATION_BFF_DOMAIN.testing
	);
};

function getTrackingCopilotApi() {
	if (APP_ENV === 'production') {
		return 'https://long-time.aftershipapi.com/tracking-copilot/v1';
	}
	if (APP_ENV === 'staging') {
		return 'https://staging-api.automizely.com/tracking-copilot/v1';
	}

	return 'https://api.automizely.io/tracking-copilot/v1';
}

function getDataDrivenApi() {
	if (APP_ENV === 'production' || APP_ENV === 'staging') {
		return 'https://api.automizely.com/data-driven';
	}

	return 'https://api.automizely.io/data-driven';
}

function getAftershipApi() {
	if (APP_ENV === 'release-banana') {
		return 'https://release-incy-admin-api.aftership.io/v1';
	}
	if (APP_ENV === 'production') {
		return `https://admin-api.aftership.com/v1`;
	}
	if (APP_ENV === 'staging') {
		return `https://staging-admin-api.aftership.com/v1`;
	}

	return `https://admin-api.aftership.io/v1`;
}

function getAftershipGraphqlUrl() {
	if (APP_ENV === 'production') {
		return `https://admin-api.aftership.com/graphql`;
	}
	if (APP_ENV === 'staging') {
		return `https://staging-admin-api.aftership.com/graphql`;
	}
	if (APP_ENV === 'release-incy') {
		return `https://release-incy-admin-api.aftership.io/graphql`;
	}
	// for rbac test
	if (APP_ENV === 'release-northstar') {
		return `https://release-incy-admin-api.aftership.io/graphql`;
	}
	return `https://admin-api.aftership.io/graphql`;
}

function getAutomizelyInternalGraphqlDomain() {
	let domain;
	switch (APP_ENV) {
		case 'production':
			domain = 'https://bff-trackings.as-in.com';
			break;
		case 'staging':
			domain = 'https://staging-bff-trackings.automizely.com';
			break;
		case 'release-sloe':
			domain = 'https://release-kiwi-bff-trackings.as-in.io';
			break;
		case 'release-okra':
			domain = 'https://release-pear-bff-trackings.as-in.io';
			break;
		default:
			if (process.env.USE_LOCAL_BFF) {
				domain = 'http://localhost:9003';
			} else {
				domain = 'https://bff-trackings.as-in.io';
			}
	}
	return domain;
}

function getAutomizelyGraphqlDomain() {
	let domain;
	switch (APP_ENV) {
		case 'production':
			domain = 'https://api.automizely.com';
			break;
		case 'staging':
			domain = 'https://staging-api.automizely.com';
			break;
		case 'release-tidy':
			domain = 'https://release-incy-api.automizely.io';
			break;
		case 'release-plum':
			domain = 'https://api.automizely.io';
			break;
		case 'release-kiwi':
			domain = 'https://release-pear-api.automizely.io';
			break;
		case 'release-banana':
			domain = 'https://release-incy-api.automizely.io';
			break;
		case 'release-pear':
			domain = 'https://testing-pear-api.automizely.io';
			break;
		case 'release-sloe':
			domain = 'https://release-kiwi-api.automizely.io';
			break;
		case 'release-walnut':
			domain = 'https://testing-pear-api.automizely.io';
			break;
		case 'release-core':
			domain = 'https://release-core-api.automizely.io';
			break;
		case 'release-nike':
			domain = 'https://release-nike-api.automizely.io';
			break;
		case 'release-bean':
			domain = 'https://api.automizely.io';
			break;
		case 'release-incy':
			domain = 'https://release-tidy-api.automizely.io';
			break;
		case 'release-okra':
			domain = 'https://release-incy-api.automizely.io';
			break;
		case 'release-gray':
			domain = 'https://release-tidy-api.automizely.io';
			break;
		default:
			if (process.env.USE_LOCAL_BFF) {
				domain = 'http://localhost:9003';
			} else {
				domain = 'https://api.automizely.io';
			}
	}
	return domain;
}

function getOrganizationUrlDomain() {
	let domain;
	switch (APP_ENV) {
		case 'production':
			domain = 'https://organization.automizely.com';
			break;
		case 'staging':
			domain = 'https://staging-organization.automizely.com';
			break;
		case 'release-sloe':
			domain = 'https://release-incy-organization.automizely.io';
			break;

		default:
			if (process.env.USE_LOCAL_BFF) {
				domain = 'https://release-incy-organization.automizely.io';
			} else {
				domain = 'https://organization.automizely.io';
			}
	}
	return domain;
}

function getAutomizelyGraphqlUrl() {
	return `${getAutomizelyGraphqlDomain()}/aftership/admin/graphql`;
}

function getAutomizelyInternalGraphqlUrl() {
	if (process.env.USE_LOCAL_BFF) {
		return `${getAutomizelyInternalGraphqlDomain()}/aftership/admin/graphql`;
	} else {
		return `${getAutomizelyInternalGraphqlDomain()}/aftership/internal/graphql`;
	}
}

function getBillingEnv() {
	if (APP_ENV === 'production' || APP_ENV === 'staging') {
		return APP_ENV;
	}
	if (APP_ENV === 'development') {
		return 'testing-pear';
	}
	return 'testing-pear';
}

const getPlatformEnv = () => {
	const [env] = APP_ENV.split('-');
	switch (env) {
		case 'development':
		case 'release':
		case 'testing':
			return 'testing';
		case 'staging':
			return 'staging';
		case 'production':
		default:
			return 'production';
	}
};

function getFacebookClientId() {
	switch (APP_ENV) {
		case 'development':
			return '271167520048622';
		default:
		case 'testing':
			return '391195571719400';
		case 'production':
		case 'staging':
			return '1202656803224981';
	}
}

function getUsernameUrl() {
	let url = '';
	let env = APP_ENV;
	switch (APP_ENV) {
		case 'release-pear':
			url = 'https://release-incy-username.aftership.io';
			break;
		case 'release-walnut':
			url = 'https://release-kiwi-username.aftership.io';
			break;
		case 'release-kiwi':
		case 'release-banana':
			url = 'https://release-kiwi-username.aftership.io';
			env = 'release-kiwi';
			break;
		case 'release-incy':
			url = 'https://release-incy-username.aftership.io';
			env = 'release-incy';
			break;
		case 'release-tidy':
			url = 'https://release-tidy-username.aftership.io';
			env = 'release-tidy';
			break;
		case 'release-nike':
			url = 'https://release-nike-username.aftership.io';
			env = 'release-nike';
			break;
		case 'release-gray':
			url = 'https://release-incy-username.aftership.io';
			env = 'release-incy';
			break;
		case 'release-core':
			url = 'https://release-core-username.aftership.io';
			break;
		case 'testing':
			url = 'https://username.aftership.io';
			break;
		case 'staging':
			url = 'https://staging-username.aftership.com';
			break;
		case 'production':
			url = 'https://username.aftership.com';
			break;
		default:
			url = 'https://username.aftership.io';
	}
	return {url, env};
}

function getTrackingPageIframeUrl() {
	let url = '';
	// preview 模式时，iframe 会优先使用 env 环境发生请求，而不是 url 的环境。
	let env = APP_ENV;
	if (process.env.USE_LOCAL_PREVIEW) {
		url = 'http://localhost:3001';
	} else {
		const {url: usernameUrl, env: usernameEnv} = getUsernameUrl();
		url = usernameUrl + '/editorPreview';
		env = usernameEnv;
	}

	return `${url}?env=${env}`;
}

function getTrackingClipIframeUrl() {
	let url = '';
	let env = APP_ENV;
	if (process.env.USE_LOCAL_PREVIEW) {
		url = 'http://localhost:3002';
	} else {
		const {url: usernameUrl, env: usernameEnv} = getUsernameUrl();
		url = usernameUrl + '/clipEditorPreview';
		env = usernameEnv;
	}

	return `${url}?env=${env}`;
}

function getTrackingClipSDKUrl() {
	let url = '';
	if (process.env.USE_LOCAL_PREVIEW) {
		url = 'http://localhost:3333/tracking-clips/sdk.js';
	} else {
		const {url: usernameUrl} = getUsernameUrl();
		url = usernameUrl + '/tracking-clips/sdk.js';
	}

	return `${url}`;
}

function getTrackingEmailClipUrl() {
	let url = '';
	if (process.env.USE_LOCAL_PREVIEW) {
		url = 'http://localhost:3333/api/tracking-clips/email';
	} else {
		const {url: usernameUrl} = getUsernameUrl();
		url = usernameUrl + '/api/tracking-clips/email';
	}

	return `${url}`;
}

function getTrackingClipMockPreviewUrl() {
	let {url} = getUsernameUrl();
	url = url + '/clipEditorPreview/#/mock/';

	return `${url}`;
}

const getViteGMapAPIKey = () => {
	if (APP_ENV === 'production' || APP_ENV === 'staging') {
		return 'AIzaSyCWqRsBmAXfbFmD469vM_nRYl-jZqIcjOw';
	} else if (APP_ENV === 'release-sloe' || process.env.USE_LOCAL_BFF) {
		return 'AIzaSyCxzaKaITXJkjRuUOMZEHEsxqolQ8jO93E';
	}

	return '';
};

const BFF_API_AS_DOMAIN = {
	testing: 'https://bff-api.aftership.io',
	staging: 'https://staging-bff-api.aftership.com',
	production: 'https://bff-api.aftership.com',
};
function getClientEnvironment(publicUrl) {
	const isProduction = APP_ENV === 'production' || APP_ENV === 'staging';
	const prefix = APP_ENV === 'production' ? '' : `${APP_ENV}-`;
	const GIT_COMMIT_SHA1 = execSync('git rev-parse HEAD').toString().trim();

	const raw = Object.keys(process.env)
		.filter(key => REACT_APP.test(key))
		.reduce(
			(acc, key) => {
				acc[key] = process.env[key];
				return acc;
			},
			{
				// Useful for determining whether we’re running in production mode.
				// Most importantly, it switches React into the correct mode.
				NODE_ENV,
				APP_ENV,
				CYPRESS,
				// Only the value of "production" or "testing", for which no need to distinguish various kind of testing env
				APP_ENV_COMMON: isProduction ? 'production' : 'testing',
				// Useful for resolving the correct path to static assets in `public`.
				// For example, <img src={process.env.PUBLIC_URL + '/img/logo.png'} />.
				// This should only be used as an escape hatch. Normally you would put
				// images into the `src` and `import` them in code to get their paths.
				PUBLIC_URL: publicUrl,
				AFTERSHIP_BFF_URL: getAutomizelyGraphqlDomain(),
				DOMAIN: isProduction ? 'aftership.com' : 'aftership.io',
				ACCOUNTS_AFTERSHIP: getOrganizationUrlDomain(),
				ACCOUNTS_BUSINESS: isProduction
					? `https://${prefix}business.automizely.com`
					: 'https://business.automizely.io',
				TRACKING_COPILOT_API: getTrackingCopilotApi(),
				DATA_DRIVEN_API: getDataDrivenApi(),
				AUTOMIZELY_NOTIFICATION_CENTER_API: isProduction
					? `https://api.automizely.com/notification-center`
					: `https://api.automizely.io/notification-center`,
				SSO_DOMAIN: isProduction
					? 'accounts.aftership.com'
					: 'accounts.aftership.io',
				// TODO: fix it to proper testing env once api is ready
				ANALYTICS_API: isProduction
					? 'https://analytics.aftershipapi.com'
					: isDev
					? 'http://localhost:3303/api-analytics'
					: 'https://analytics.aftershipapi.io',
				// https://testing-dassai-secure.aftership.io'
				BFF_API_AS:
					BFF_API_AS_DOMAIN[APP_ENV] || BFF_API_AS_DOMAIN.testing,
				SECURE_AFTERSHIP: isProduction
					? `https://secure.aftership.com`
					: 'https://secure.aftership.io',
				BUSINESS_API: isProduction
					? 'https://api.automizely.com/businesses/v1'
					: 'https://release-incy-api.automizely.io/businesses/v1',
				BILLING_API_V2: getBillingApiV2(),
				AFTERSHIP_API: getAftershipApi(),
				POSTMEN_API: isProduction
					? `https://api.postmen.com`
					: isDev
					? 'http://localhost:3303/api-postmen'
					: 'https://api.postmen.io',
				ADD_APPS_URL: isProduction
					? `https://${prefix}organization.automizely.com/connections`
					: 'https://organization.automizely.io/connections',
				PAYMENT_URL: isProduction
					? `https://${prefix}organization.automizely.com/payment`
					: 'https://organization.automizely.io/payment',
				ACCOUNT_PROFILE_URL: isProduction
					? `https://${prefix}business.automizely.com/profile`
					: 'https://business.automizely.io/profile',
				// just a random generated UUID
				CLIENT_ID: 'ebad4444-f01e-4f75-aa83-742553ba10a6',
				// NOTE: REMOVE
				BUTTON_ORIGIN: isProduction
					? `https://button.aftership.com`
					: 'https://button.aftership.io',
				DEFAULT_TRACK_PAGE: 'https://track.aftership.com',
				STRIPE_API_KEY: isProduction
					? 'pk_live_f3GqXCRiVdXOAxvENqd5wMwl'
					: 'pk_test_oNOyGAkYyixi0x39zaOzZ9wX',
				NEW_RELIC_CODE: isProduction
					? newRelicCodeProduction
					: newRelicCodeTesting,
				GTM_ID: 'GTM-PRV3TML', // enable GTM in local too.
				GTAG_ID: isProduction ? 'G-1TKVZPN4QH' : 'G-82HPXE066K',
				BRAINTREE_TOKEN: isProduction
					? 'production_24r79249_3zmpfys9gx32vngp'
					: 'sandbox_mfz9b6ff_w9przv564p7q5cqn',
				RETURNS_CENTER_URL: isProduction
					? 'https://admin.returnscenter.com'
					: 'https://admin.returnscenter.io',
				RETURNS_CENTER_HOST_URL: isProduction
					? 'http://admin.aftership.com/returns'
					: 'http://admin.aftership.io/returns',
				POSTMEN_URL: isProduction
					? 'https://admin.postmen.com'
					: 'https://admin.postmen.io',
				MESSAGES_URL: isProduction
					? 'https://messages.automizely.com'
					: 'https://messages.automizely.io',
				MARKETING_URL: isProduction
					? 'https://marketing.automizely.com'
					: 'https://marketing.automizely.io',
				DROPSHIPPING_URL: isProduction
					? 'https://dropshipping.automizely.com'
					: 'https://dropshipping.automizely.io',
				REVIEWS_URL: isProduction
					? 'https://reviews.automizely.com'
					: 'https://reviews.automizely.io',
				SHOPPING_URL: isProduction
					? 'https://shopping.automizely.com'
					: 'https://shopping.automizely.io',
				ADMIN_PAGEBUILDER: isProduction
					? `https://pages.automizely.com`
					: 'https://pages.automizely.io',
				ADS_URL: isProduction
					? 'https://admin.automizelyads.com'
					: 'https://admin.automizelyads.io',
				LOYALTY_URL: isProduction
					? 'https://loyalty.automizely.com/'
					: 'https://loyalty.automizely.io',
				EDD_WIDGET_URL: isProduction
					? 'https://widgets.automizely.com'
					: 'https://widgets.automizely.io',
				TRACKING_APP_BLOCK_ID: isProduction
					? '3375afd5-c790-4ee3-abbc-ae4972ab72ed'
					: '0e6e1317-b604-48ab-8d89-e30c0a81392e',
				UPDATE_SHOPIFY_SCOPE_URL: isProduction
					? 'https://accounts.aftership.com/auth/realms/business/integrations/oauth/shopify/aftership'
					: 'https://accounts.aftership.io/auth/realms/business/integrations/oauth/shopify/aftership',
				UPDATE_BIGCOMMERCE_SCOPE_URL: isProduction
					? 'https://accounts.aftership.com/auth/realms/business/integrations/oauth/bigcommerce/aftership'
					: 'https://accounts.aftership.io/auth/realms/business/integrations/oauth/bigcommerce/aftership',
				TRACKING_PAGE_PREVIEW_IFRAME_URL: getTrackingPageIframeUrl(),
				TRACKING_CLIP_PREVIEW_IFRAME_URL: getTrackingClipIframeUrl(),
				TRACKING_CLIP_SDK_URL: getTrackingClipSDKUrl(),
				TRACKING_EMAIL_CLIP_URL: getTrackingEmailClipUrl(),
				TRACKING_CLIP_MOCK_PREVIEW_URL: getTrackingClipMockPreviewUrl(),
				AM_CONSENT_SDK_URL: isProduction
					? 'https://sdks.am-static.com/cookie-banner/sdk.js'
					: 'https://sdks.am-static.io/cookie-banner/sdk.js',
				PERK_FEDERATION_DOMAIN: getPerkFederationDomain(),
				APPS_FEDERATION_DOMAIN: getAppsFederationDomain(),
				APPS_FEDERATION_BFF_DOMAIN: getAppsFederationBffDomain(),
				BILLING_ENV: getBillingEnv(),
				PLATFORM_ENV: getPlatformEnv(),
				AFTERSHIP_GRAPHQL_URL: getAftershipGraphqlUrl(),
				AUTOMIZELY_GRAPHQL_URL: getAutomizelyGraphqlUrl(),
				AUTOMIZELY_INTERNAL_GRAPHQL_URL:
					getAutomizelyInternalGraphqlUrl(),
				GIT_COMMIT_SHA1,
				FACEBOOK_CLIENT_ID: getFacebookClientId(),
				COMPANY_CONSOLE_ENV: process.env.USE_COMPANY_CONSOLE,
				CLOUDFLARE_STREAM_DOMAIN: isProduction
					? 'customer-vu4lobcs8gwny8yb.cloudflarestream.com'
					: 'customer-umtkgfxxroyfsrnk.cloudflarestream.com',
				VITE_GMAP_API_KEY: getViteGMapAPIKey(),
			}
		);
	// Stringify all values so we can feed into Webpack DefinePlugin
	const stringified = {
		'process.env': Object.keys(raw).reduce((acc, key) => {
			acc[key] = JSON.stringify(raw[key]);
			return acc;
		}, {}),
	};

	return {raw, stringified};
}

exports = module.exports = getClientEnvironment;

exports.DEFAULT_PORT = DEFAULT_PORT;
exports.HOST = HOST;
exports.getAutomizelyGraphqlDomain = getAutomizelyGraphqlDomain;
