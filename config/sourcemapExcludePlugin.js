function sourcemapExcludePlugin(opts) {
	return {
		name: 'sourcemap-exclude-plugin',
		transform: (code, id) => {
			if (opts?.excludeNodeModules && id.includes('node_modules')) {
				return {
					code,
					// https://github.com/rollup/rollup/blob/master/docs/plugin-development/index.md#source-code-transformations
					map: {mappings: ''},
				};
			}
			return {
				code: code,
				map: null,
			};
		},
	};
}

module.exports = sourcemapExcludePlugin;
