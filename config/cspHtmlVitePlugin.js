const getCspConfigs = env => ({
	'base-uri': "'self'",
	'object-src': "'none'",
	'script-src': [
		"'unsafe-inline'",
		"'self'",
		"'unsafe-eval'",
		'js.hs-scripts.com',
		'js-agent.newrelic.com',
		'bam.nr-data.net',
		'assets.aftership.com',
		'js.stripe.com',
		'cdnjs.cloudflare.com',
		'*.hotjar.com',
		'*.google.com',
		'*.gstatic.com',
		'*.gstatic.cn',
		'*.google-analytics.com',
		'*.googletagmanager.com',
		'*.googleapis.com',
		'ajax.cloudflare.com',
		'cdn4.mxpnl.com',
		'*.paypal.com',
		'https://forms.hsforms.com',
		'https://meetings.hubspot.com/',
		'www.recaptcha.net',
		'sentry.automizely.org',
		'www.paypalobjects.com',
		'js.braintreegateway.com',
		'cdn.mxpnl.com',
		'*.aftership.com',
		'*.myshopify.com',
		env.ACCOUNTS_AFTERSHIP,
		env.ACCOUNTS_BUSINESS,
		env.AM_CONSENT_SDK_URL,
		`*.${env.DOMAIN}`,
		'https://www.googleadservices.com',
		'https://googleads.g.doubleclick.net',
		'https://*',
		'*.am-static.com',
		'*.am-static.io',
		env.PERK_FEDERATION_DOMAIN,
		env.APPS_FEDERATION_DOMAIN,
		'https://app.getbeamer.com/',
		// 'localhost:3001',
		'*.aftership.io',
		'*.aftership.com',
		'*.getbeamer.com',
	],
	'style-src': [
		"'unsafe-inline'",
		"'self'",
		"'unsafe-eval'",
		'*.googleapis.com',
		'*.gstatic.com',
		'*.gstatic.cn',
		'*.google.com',
		'*.google-analytics.com',
		'*.aftership.com',
		`*.${env.DOMAIN}`,
		'*.am-static.com',
		'*.am-static.io',
		'https://*',
		env.APPS_FEDERATION_DOMAIN,
		'*.getbeamer.com',
		env.CLOUDFLARE_STREAM_DOMAIN,
	],
	'frame-src': [
		"'self'",
		'*.hotjar.com',
		'js.stripe.com',
		'www.recaptcha.net',
		'*.google.com',
		'*.paypal.com',
		'*.myshopify.com',
		'https://forms.hsforms.com',
		'https://meetings.hubspot.com/',
		env.ACCOUNTS_AFTERSHIP,
		env.SSO_DOMAIN,
		'*.aftership.io',
		'*.aftership.com',
		'*.zoominfo.com',
		`*.${env.DOMAIN}`,
		'*.crisp.help',
		'https://widgets.automizely.com',
		'https://bid.g.doubleclick.net',
		'https://platform.twitter.com',
		'https://www.facebook.com/',
		'https://web.facebook.com/',
		'https://username.am-static.com',
		'https://username.am-static.io',
		'*.am-static.com',
		'*.am-static.io',
		'https://app.getbeamer.com/',
		'datastudio.google.com',
		'*.getbeamer.com',
		'https://www.youtube.com/',
		// uncomment this line if you're developing tracking page iframe locally
		// 'http://localhost:3000',
	],
	'img-src': [
		"'self'",
		'data: blob:',
		'*.shopify.com',
		'*.aftership.com',
		'*.bigcommerce.com',
		'*.google.com',
		'*.google-analytics.com',
		'*.googleapis.com',
		'*.gstatic.com',
		'*.gstatic.cn',
		'*.paypal.com',
		'https://forms.hsforms.com',
		'https://meetings.hubspot.com/',
		'*.doubleclick.net',
		`*.${env.DOMAIN}`,
		'https://www.googletagmanager.com',
		'aftership.am-usercontent.io',
		'aftership.am-usercontent.com',
		'www.google.com.hk',
		'https://*',
		'*.am-static.com',
		'*.am-static.io',
		'*.getbeamer.com',
	],
	'connect-src': [
		...[
			"'self'",
			'data:',
			'*.stripe.com',
			'*.paypal.com',
			'https://forms.hsforms.com',
			'https://meetings.hubspot.com/',
			'*.braintreegateway.com',
			'bam.nr-data.net',
			'api.mixpanel.com',
			env.ANALYTICS_API,
			env.SECURE_AFTERSHIP,
			'*.aftership.com',
			`*.${env.DOMAIN}`,
			's3.dualstack.us-east-1.amazonaws.com',
			'wss://*.crisp.chat',
			'https://*',
			'*.am-static.com',
			'*.am-static.io',
			env.APPS_FEDERATION_BFF_DOMAIN,
			'*.getbeamer.com',
		],
		...(env.NODE_ENV === 'development' ? ['*'] : []),
	],
	'default-src': ["'self'", 'data:', '*.aftership.com', `*.${env.DOMAIN}`],
	'font-src': [
		"'self'",
		'data:',
		'*.gstatic.com',
		'*.gstatic.cn',
		'*.hotjar.com',
		'*.aftership.com',
		`*.${env.DOMAIN}`,
		'https://*',
		'*.am-static.com',
		'*.am-static.io',
		'*.getbeamer.com',
	],
	'media-src': [
		'*.am-static.com',
		'*.am-static.io',
		'blob:',
		env.CLOUDFLARE_STREAM_DOMAIN,
	],
	'worker-src': ["'self'", 'blob:'],
});

/**
 * @return {import('vite').Plugin}
 */
const csp = env => {
	const config = getCspConfigs(env);

	if (process.env.USE_LOCAL_PREVIEW) {
		config['frame-src'].push('http://localhost:3001');
		config['frame-src'].push('http://localhost:3002');
	}

	const header = Object.entries(config)
		.map(
			([key, value]) =>
				`${key} ${typeof value === 'string' ? value : value.join(' ')}`
		)
		.join(';');

	return {
		name: 'vite:csp',

		transformIndexHtml: {
			enforce: 'post',
			transform: html => ({
				html,
				tags: [
					{
						tag: 'meta',
						attrs: {
							'http-equiv': 'Content-Security-Policy',
							content: header,
						},
						injectTo: 'head-prepend',
					},
				],
			}),
		},
	};
};

module.exports = csp;
