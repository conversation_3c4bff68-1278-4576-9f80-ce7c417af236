import '@testing-library/jest-dom';
import {fetch} from 'cross-fetch';
import {vi} from 'vitest';

global.fetch = fetch;
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	value: vi.fn().mockImplementation(query => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: vi.fn(), // deprecated
		removeListener: vi.fn(), // deprecated
		addEventListener: vi.fn(),
		removeEventListener: vi.fn(),
		dispatchEvent: vi.fn(),
	})),
});
vi.mock('@aftership/automizely-billing-ui-react', () => ({}));
