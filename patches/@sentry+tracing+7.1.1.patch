diff --git a/node_modules/@sentry/tracing/cjs/browser/metrics/index.js b/node_modules/@sentry/tracing/cjs/browser/metrics/index.js
index ee57ab2..7d2bab8 100644
--- a/node_modules/@sentry/tracing/cjs/browser/metrics/index.js
+++ b/node_modules/@sentry/tracing/cjs/browser/metrics/index.js
@@ -33,14 +33,14 @@ function startTrackingWebVitals(reportAllChanges = false) {
     if (performance.mark) {
       global.performance.mark('sentry-tracing-init');
     }
-    _trackCLS();
+    _trackCLS(reportAllChanges);
     _trackLCP(reportAllChanges);
-    _trackFID();
+    _trackFID(reportAllChanges);
   }
 }
 
 /** Starts tracking the Cumulative Layout Shift on the current page. */
-function _trackCLS() {
+function _trackCLS(reportAllChanges) {
   // See:
   // https://web.dev/evolving-cls/
   // https://web.dev/cls-web-tooling/
@@ -53,7 +53,7 @@ function _trackCLS() {
     (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && utils.logger.log('[Measurements] Adding CLS');
     _measurements['cls'] = { value: metric.value, unit: 'millisecond' };
     _clsEntry = entry ;
-  });
+  }, reportAllChanges);
 }
 
 /** Starts tracking the Largest Contentful Paint on the current page. */
@@ -74,7 +74,7 @@ function _trackLCP(reportAllChanges) {
 }
 
 /** Starts tracking the First Input Delay on the current page. */
-function _trackFID() {
+function _trackFID(reportAllChanges) {
   getFID.getFID(metric => {
     var entry = metric.entries.pop();
     if (!entry) {
@@ -86,7 +86,7 @@ function _trackFID() {
     (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && utils.logger.log('[Measurements] Adding FID');
     _measurements['fid'] = { value: metric.value, unit: 'millisecond' };
     _measurements['mark.fid'] = { value: timeOrigin + startTime, unit: 'second' };
-  });
+  }, reportAllChanges);
 }
 
 /** Add performance related spans to a transaction */
diff --git a/node_modules/@sentry/tracing/cjs/idletransaction.js b/node_modules/@sentry/tracing/cjs/idletransaction.js
index 5ed1943..86c180c 100644
--- a/node_modules/@sentry/tracing/cjs/idletransaction.js
+++ b/node_modules/@sentry/tracing/cjs/idletransaction.js
@@ -6,7 +6,7 @@ var transaction = require('./transaction.js');
 
 var DEFAULT_IDLE_TIMEOUT = 1000;
 var DEFAULT_FINAL_TIMEOUT = 30000;
-var HEARTBEAT_INTERVAL = 5000;
+var HEARTBEAT_INTERVAL = 10000;
 
 /**
  * @inheritDoc
diff --git a/node_modules/@sentry/tracing/esm/browser/metrics/index.js b/node_modules/@sentry/tracing/esm/browser/metrics/index.js
index f876e04..d66547d 100644
--- a/node_modules/@sentry/tracing/esm/browser/metrics/index.js
+++ b/node_modules/@sentry/tracing/esm/browser/metrics/index.js
@@ -28,14 +28,14 @@ function startTrackingWebVitals(reportAllChanges = false) {
     if (performance.mark) {
       global.performance.mark('sentry-tracing-init');
     }
-    _trackCLS();
+    _trackCLS(reportAllChanges);
     _trackLCP(reportAllChanges);
-    _trackFID();
+    _trackFID(reportAllChanges);
   }
 }
 
 /** Starts tracking the Cumulative Layout Shift on the current page. */
-function _trackCLS() {
+function _trackCLS(reportAllChanges) {
   // See:
   // https://web.dev/evolving-cls/
   // https://web.dev/cls-web-tooling/
@@ -48,7 +48,7 @@ function _trackCLS() {
     (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && logger.log('[Measurements] Adding CLS');
     _measurements['cls'] = { value: metric.value, unit: 'millisecond' };
     _clsEntry = entry ;
-  });
+  }, reportAllChanges);
 }
 
 /** Starts tracking the Largest Contentful Paint on the current page. */
@@ -69,7 +69,7 @@ function _trackLCP(reportAllChanges) {
 }
 
 /** Starts tracking the First Input Delay on the current page. */
-function _trackFID() {
+function _trackFID(reportAllChanges) {
   getFID(metric => {
     var entry = metric.entries.pop();
     if (!entry) {
@@ -81,7 +81,7 @@ function _trackFID() {
     (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__) && logger.log('[Measurements] Adding FID');
     _measurements['fid'] = { value: metric.value, unit: 'millisecond' };
     _measurements['mark.fid'] = { value: timeOrigin + startTime, unit: 'second' };
-  });
+  }, reportAllChanges);
 }
 
 /** Add performance related spans to a transaction */
diff --git a/node_modules/@sentry/tracing/esm/idletransaction.js b/node_modules/@sentry/tracing/esm/idletransaction.js
index 5d3666e..698dd65 100644
--- a/node_modules/@sentry/tracing/esm/idletransaction.js
+++ b/node_modules/@sentry/tracing/esm/idletransaction.js
@@ -4,7 +4,7 @@ import { Transaction } from './transaction.js';
 
 var DEFAULT_IDLE_TIMEOUT = 1000;
 var DEFAULT_FINAL_TIMEOUT = 30000;
-var HEARTBEAT_INTERVAL = 5000;
+var HEARTBEAT_INTERVAL = 10000;
 
 /**
  * @inheritDoc
