name: Pre build
on:
    pull_request:
        branches:
            - 'master'
            - 'staging'
            - 'testing'
            - 'release/*'
jobs:
    lint-check:
        name: '<PERSON><PERSON> and check'
        runs-on: ubuntu-latest
        timeout-minutes: 10
        steps:
            - name: Checkout code
              uses: actions/checkout@v3
              with:
                  fetch-depth: 1
            - name: Setup node env
              uses: actions/setup-node@v3
              with:
                  node-version: '18'
                  cache: yarn
            - name: Authenticate with private NPM package
              run: |
                  echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc
            - name: Lint & Check
              run: yarn info:check
            # - name: Run eslint
            #   run: yarn lint:actions
            # - name: Yarn install dependencies
            #   run: echo "Nexus deps could not be installed here"
            # - name: Eslint & TypeScript
            #   run: echo "Unable to run lint because previous step is not implemented for yet"
            # - name: Run unit test script
            #   run: echo "Unable to run unit tests for yet"

    sonar-scan:
        name: 'Sonar scan'
        runs-on: ubuntu-latest
        timeout-minutes: 10
        steps:
            - name: Checkout code
              uses: actions/checkout@v3
              with:
                  fetch-depth: 0
            - name: Sonar<PERSON>ube Scan
              if: ${{ github.event_name == 'pull_request' }}
              uses: sonarsource/sonarqube-scan-action@v2.3.0
              with:
                  projectBaseDir: .
                  args: >
                      -Dsonar.exclusions=**/__tests__/**,**/*.test.ts,node_modules/**,dist/**,scripts/**,test/**,tests/**,build/**,config/**,cypress/**
                      -Dsonar.projectKey=${{ github.event.repository.name }}
                      -Dsonar.pullrequest.provider=github
                      -Dsonar.pullrequest.github.repository=${{ github.repository }}
                      -Dsonar.pullrequest.key=${{ github.event.number }}
                      -Dsonar.pullrequest.branch=${{ github.head_ref }}
                      -Dsonar.pullrequest.base=${{ github.base_ref }}
                      -Dsonar.qualitygate.wait=true
                      -Dsonar.qualitygate.timeout=900
              env:
                  SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
                  SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
